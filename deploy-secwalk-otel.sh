#!/bin/bash
# SecWalk OTEL Collector 一键部署脚本
# 使用方法: ./deploy-secwalk-otel.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_prerequisites() {
    log_info "检查前置条件..."
    
    # 检查kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装或不在PATH中"
        exit 1
    fi
    
    # 检查集群连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群"
        exit 1
    fi
    
    # 检查必要的YAML文件
    required_files=(
        "k8s-secwalk-otel-namespace-rbac.yaml"
        "k8s-secwalk-otel-configmap.yaml"
        "k8s-secwalk-otel-deployment.yaml"
        "k8s-secwalk-otel-monitoring.yaml"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "必需文件 $file 不存在"
            exit 1
        fi
    done
    
    log_success "前置条件检查通过"
}

# 部署函数
deploy_resources() {
    log_info "开始部署SecWalk OTEL Collector..."
    
    # 1. 创建命名空间和RBAC
    log_info "创建命名空间和RBAC配置..."
    kubectl apply -f k8s-secwalk-otel-namespace-rbac.yaml
    
    # 2. 创建ConfigMap
    log_info "创建ConfigMap..."
    kubectl apply -f k8s-secwalk-otel-configmap.yaml
    
    # 3. 部署OTEL Collector
    log_info "部署OTEL Collector..."
    kubectl apply -f k8s-secwalk-otel-deployment.yaml
    
    # 4. 部署监控和自动扩缩容配置
    log_info "部署监控配置..."
    kubectl apply -f k8s-secwalk-otel-monitoring.yaml
    
    log_success "所有资源已提交到集群"
}

# 等待部署完成
wait_for_deployment() {
    log_info "等待Pod启动..."
    
    # 等待Deployment可用
    if kubectl wait --for=condition=available deployment/otel-collector-secwalk -n secwalk-monitoring --timeout=300s; then
        log_success "Deployment已就绪"
    else
        log_error "Deployment启动超时"
        return 1
    fi
    
    # 等待Pod就绪
    if kubectl wait --for=condition=ready pod -l app=otel-collector-secwalk -n secwalk-monitoring --timeout=300s; then
        log_success "Pod已就绪"
    else
        log_error "Pod启动超时"
        return 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署状态..."
    
    echo ""
    log_info "Pod状态:"
    kubectl get pods -n secwalk-monitoring -l app=otel-collector-secwalk
    
    echo ""
    log_info "Service状态:"
    kubectl get svc -n secwalk-monitoring -l app=otel-collector-secwalk
    
    echo ""
    log_info "ConfigMap状态:"
    kubectl get configmap -n secwalk-monitoring -l app=otel-collector-secwalk
    
    # 检查健康状态
    log_info "检查健康状态..."
    if kubectl get pods -n secwalk-monitoring -l app=otel-collector-secwalk -o jsonpath='{.items[0].status.phase}' | grep -q "Running"; then
        log_success "OTEL Collector运行正常"
    else
        log_warning "OTEL Collector可能存在问题，请检查日志"
    fi
}

# 显示后续操作指南
show_next_steps() {
    echo ""
    log_success "部署完成！"
    echo ""
    log_info "后续操作:"
    echo "1. 检查健康状态:"
    echo "   kubectl port-forward -n secwalk-monitoring svc/otel-collector-secwalk 13133:13133"
    echo "   curl http://localhost:13133/"
    echo ""
    echo "2. 访问调试界面:"
    echo "   kubectl port-forward -n secwalk-monitoring svc/otel-collector-secwalk 55679:55679"
    echo "   浏览器访问: http://localhost:55679/debug/tracez"
    echo ""
    echo "3. 查看日志:"
    echo "   kubectl logs -n secwalk-monitoring deployment/otel-collector-secwalk -f"
    echo ""
    echo "4. 查看指标:"
    echo "   kubectl port-forward -n secwalk-monitoring svc/otel-collector-secwalk 8888:8888"
    echo "   curl http://localhost:8888/metrics"
    echo ""
    log_info "配置SecWalk服务发送遥测数据到:"
    echo "   OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-collector-secwalk.secwalk-monitoring.svc.cluster.local:4317"
}

# 错误处理
handle_error() {
    log_error "部署过程中发生错误"
    log_info "查看详细错误信息:"
    echo "kubectl describe pods -n secwalk-monitoring -l app=otel-collector-secwalk"
    echo "kubectl logs -n secwalk-monitoring deployment/otel-collector-secwalk"
    exit 1
}

# 主函数
main() {
    echo "========================================"
    echo "SecWalk OTEL Collector 部署脚本"
    echo "========================================"
    echo ""
    
    # 设置错误处理
    trap handle_error ERR
    
    # 执行部署步骤
    check_prerequisites
    deploy_resources
    wait_for_deployment
    verify_deployment
    show_next_steps
    
    echo ""
    log_success "部署脚本执行完成！"
}

# 如果脚本被直接执行（而不是被source）
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
