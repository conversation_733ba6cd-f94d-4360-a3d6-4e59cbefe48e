# AWS OpenTelemetry Collector Configuration for SecWalk Service Observability
# This configuration is optimized for observing secwalk service in a Kubernetes cluster

extensions:
  health_check:
    endpoint: 0.0.0.0:13133
  pprof:
    endpoint: 0.0.0.0:1777
  zpages:
    endpoint: 0.0.0.0:55679

receivers:
  # OTLP receiver for receiving telemetry data from secwalk service
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318
        cors:
          allowed_origins:
            - http://*
            - https://*

  # AWS X-Ray receiver for distributed tracing
  awsxray:
    endpoint: 0.0.0.0:2000
    transport: udp

  # Prometheus receiver for scraping metrics from secwalk service
  prometheus:
    config:
      scrape_configs:
        - job_name: 'secwalk-service'
          scrape_interval: 30s
          static_configs:
            - targets: ['secwalk-service:8080']  # Adjust port as needed
          metrics_path: '/metrics'
          scrape_timeout: 10s

processors:
  # Batch processor for traces - optimized for secwalk service
  batch/traces:
    timeout: 2s
    send_batch_size: 100
    send_batch_max_size: 1000

  # Batch processor for metrics
  batch/metrics:
    timeout: 30s
    send_batch_size: 1000
    send_batch_max_size: 1500

  # Batch processor for logs
  batch/logs:
    timeout: 5s
    send_batch_size: 200
    send_batch_max_size: 500

  # Resource processor to add service-specific attributes
  resource:
    attributes:
      - key: service.name
        value: secwalk
        action: upsert
      - key: service.namespace
        value: security
        action: upsert
      - key: deployment.environment
        from_attribute: DEPLOYMENT_ENVIRONMENT
        action: upsert
      - key: k8s.cluster.name
        from_attribute: K8S_CLUSTER_NAME
        action: upsert

  # Memory limiter to prevent OOM
  memory_limiter:
    limit_mib: 512
    spike_limit_mib: 128
    check_interval: 5s

  # Attributes processor for secwalk-specific filtering and transformation
  attributes/secwalk:
    actions:
      - key: http.user_agent
        action: delete
      - key: secwalk.request_id
        action: upsert
        from_attribute: trace.id
      - key: secwalk.component
        action: upsert
        value: "security-scanner"

exporters:
  # AWS X-Ray exporter for distributed tracing
  awsxray:
    no_verify_ssl: false
    local_mode: false
    region: us-west-2  # Adjust to your AWS region

  # AWS CloudWatch EMF exporter for metrics
  awsemf:
    no_verify_ssl: false
    region: us-west-2  # Adjust to your AWS region
    namespace: SecWalk/Metrics
    log_group_name: /aws/otel/secwalk
    log_stream_name: secwalk-metrics
    dimension_rollup_option: NoDimensionRollup
    parse_json_encoded_attr_values: ["Sources", "kubernetes"]
    metric_declarations:
      - dimensions: [[service.name], [service.name, service.namespace]]
        metric_name_selectors:
          - secwalk.*
          - http_requests_total
          - http_request_duration_seconds
          - process_cpu_seconds_total
          - process_memory_bytes

  # AWS CloudWatch Logs exporter for application logs
  awscloudwatchlogs:
    log_group_name: /aws/otel/secwalk/logs
    log_stream_name: secwalk-application-logs
    region: us-west-2  # Adjust to your AWS region

  # Debug exporter for troubleshooting (remove in production)
  debug:
    verbosity: basic

service:
  pipelines:
    # Traces pipeline for distributed tracing
    traces:
      receivers: [otlp, awsxray]
      processors: [memory_limiter, resource, attributes/secwalk, batch/traces]
      exporters: [awsxray]

    # Metrics pipeline for performance monitoring
    metrics:
      receivers: [otlp, prometheus]
      processors: [memory_limiter, resource, batch/metrics]
      exporters: [awsemf]

    # Logs pipeline for application logging
    logs:
      receivers: [otlp]
      processors: [memory_limiter, resource, batch/logs]
      exporters: [awscloudwatchlogs]

  extensions: [health_check, pprof, zpages]

  # Telemetry configuration for the collector itself
  telemetry:
    logs:
      level: info
    metrics:
      level: basic
      address: 0.0.0.0:8888
