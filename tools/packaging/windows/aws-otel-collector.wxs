<?xml version='1.0' encoding='UTF-8'?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi"  xmlns:util="http://schemas.microsoft.com/wix/UtilExtension">
    <Product Id="EBBD8548-75D1-41D3-A402-ABE189F0C167"
UpgradeCode="B7C263DD-95A5-436A-A025-DCA5200C2BE3"
Name="ADOT Collector"
Version="0.43.3"
Codepage='1252'
Manufacturer="Amazon.com, Inc."
Language="1033">



    <Package Id='*'
Keywords='Installer'
Description="ADOT Collector Installer"
Comments='Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.'
Manufacturer='Amazon.com, Inc.'
InstallerVersion='200'
Languages='1033'
Compressed='yes'
SummaryCodepage='1252'
InstallScope="perMachine"
Platform="x64"
    />

    <MajorUpgrade
    AllowDowngrades="no"
    DowngradeErrorMessage="A later version of ADOT Collector is already installed. Setup will now exit"
    AllowSameVersionUpgrades="no" />

    <MediaTemplate EmbedCab="yes"/>

    <Feature Id="ProductFeature" Title="ADOT Collector" Level="1">
    <ComponentRef Id="ApplicationComponent"/>
    <ComponentRef Id='Ctl'/>
    <ComponentRef Id='AOCAGENT_VERSION'/>
    <ComponentRef Id='LICENSE'/>
    <ComponentRef Id='CommonConfigYML'/>
    <ComponentRef Id='CreateLogsFolder'/>
    <ComponentRef Id='CreateConfigsFolder'/>
    </Feature>

    <Directory Id="TARGETDIR" Name="SourceDir">
    <Directory Id='ProgramFiles64Folder'>
    <Directory Id='PFilesAmazon' Name='Amazon'>
    <Directory Id='INSTALLDIR' Name='AWSOTelCollector'/>
    </Directory>
    </Directory>

    <Directory Id='CommonAppDataFolder' Name='AppDataFolder'>
    <Directory Id='AppDataFolderAmazon' Name='Amazon'>
    <Directory Id='Config' Name='AWSOTelCollector'>
    <Directory Id="Configs" Name="Configs"/>
    <Directory Id='Logs' Name='Logs'/>
    </Directory>
    </Directory>
    </Directory>
    </Directory>

    <DirectoryRef Id="INSTALLDIR">
    <Component Id="ApplicationComponent" Guid="5f344c26-c8f5-4a10-83c0-0651399fb8ff" Win64='yes'>
    <File Id="ExecutableFile" Name=".aws-otel-collector.exe" KeyPath="yes" Source="./build/windows/amd64/aoc"/>
    <ServiceInstall
Id="Sevice"
Name="AWSOTelCollector"
DisplayName="ADOT Collector"
Description="Collects, processes, and exports telemetry from various configurable sources."
Type="ownProcess"
Vital="yes"
Start="auto"
Account="LocalSystem"
ErrorControl="normal"
Arguments=" --config=&quot;[INSTALLDIR]config.yaml&quot;"
Interactive="no">
    </ServiceInstall>
    <ServiceControl
Id="StartService"
Name="AWSOTelCollector"
Stop="both"
Remove="uninstall"
Wait="yes" />
    </Component>
    <Component Id='Ctl' Guid='dc332e07-822a-495c-8c88-281e9c6d753e' Win64='yes'>
    <File Source='./tools/ctl/windows/aws-otel-collector-ctl.ps1' KeyPath='yes'/>
    </Component>
    <Component Id='AOCAGENT_VERSION' Guid='f4ddf7bf-48fc-41f6-a914-4153a7cf0afc' Win64='yes'>
    <File Source='./VERSION' KeyPath='yes'/>
    </Component>
    <Component Id='LICENSE' Guid='ac70ef6c-8ec4-4a91-8059-2c18543df863' Win64='yes'>
    <File Source='./LICENSE' KeyPath='yes'/>
    </Component>
    </DirectoryRef>

    <DirectoryRef Id="Config">
    <Component Id='CommonConfigYML' Guid='293f73c5-1f51-4e65-86e3-97425ec75c94' Win64='yes' NeverOverwrite='yes' Permanent='yes'>
    <CreateFolder>
      <!-- Set permissions so that only admin and system can access this folder
         Configures the permissions in the SDDL format: https://learn.microsoft.com/en-us/windows/win32/secauthz/security-descriptor-string-format
         DACL flags P + AI are used to not inherit permissions from parent folder and to propagate permissions to child elements.
         The subsequent ACE strings with the format "ace_type;ace_flags;rights;object_guid;inherit_object_guid;account_sid;(resource_attribute)" do the following:
         - Allow access (ace_type A) to the local system (account_sid SY) and administrators (account_sid BA)
         - ace_flags "OI" + "CI": inherit ACE permissions for the SIDs
         - Rights "FA": gives permissions to read, write and delete files.
      -->
      <PermissionEx Sddl="D:PAI(A;OICI;FA;;;SY)(A;OICI;FA;;;BA)"/>
    </CreateFolder>
    <File Source='./config.yaml' KeyPath='yes'/>
    </Component>
    </DirectoryRef>

    <DirectoryRef Id="Logs">
    <Component Id='CreateLogsFolder' Guid='fe9042cb-a4fa-4b8e-9852-685a342338b5' Win64='yes'>
    <CreateFolder />
    </Component>
    </DirectoryRef>

    <DirectoryRef Id="Configs">
    <Component Id='CreateConfigsFolder' Guid='c860d000-ed10-11e8-8eb2-f2801f1b9fd1' Win64='yes'>
    <CreateFolder />
    </Component>
    </DirectoryRef>
    </Product>
    </Wix>
