# Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License").
# You may not use this file except in compliance with the License.
# A copy of the License is located at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# or in the "license" file accompanying this file. This file is distributed
# on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
# express or implied. See the License for the specific language governing
# permissions and limitations under the License.

# Location: /etc/systemd/system/aws-otel-collector.service
# systemctl enable aws-otel-collector
# systemctl start aws-otel-collector
# systemctl | grep aws-otel-collector
# https://www.freedesktop.org/software/systemd/man/systemd.unit.html

[Unit]
Description=ADOT Collector
After=network.target

[Service]
Type=simple
EnvironmentFile=/opt/aws/aws-otel-collector/etc/.env
ExecStart=/opt/aws/aws-otel-collector/bin/aws-otel-collector $config
KillMode=process
Restart=on-failure
RestartSec=60s
User=aoc
Group=aoc

[Install]
WantedBy=multi-user.target
