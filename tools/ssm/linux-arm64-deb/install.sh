#!/usr/bin/env bash
# Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License").
# You may not use this file except in compliance with the License.
# A copy of the License is located at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# or in the "license" file accompanying this file. This file is distributed
# on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
# express or implied. See the License for the specific language governing
# permissions and limitations under the License.

set -e
set -u

echo 'Installing AWSDistroOTel-Collector.'

dpkg -i -E ./aws-otel-collector.deb

readonly cmd='/opt/aws/aws-otel-collector/bin/aws-otel-collector-ctl'
readonly conf='/opt/aws/aws-otel-collector/etc/config.yaml'

if [ ${SSM_CONFIG:+has_config} ]; then
    echo "${SSM_CONFIG}" | base64 -d > ${conf}
fi

"${cmd}" -a start
