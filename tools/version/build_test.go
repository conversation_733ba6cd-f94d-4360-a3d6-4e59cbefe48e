// Copyright 2019, OpenTelemetry Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package version

import (
	"runtime"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

// InfoVar is a singleton instance of the Info struct.
var InfoVar = Info([][2]string{
	{"Version", Version},
	{"GitHash", GitHash},
	{"Goversion", runtime.Version()},
	{"OS", runtime.GOOS},
	{"Architecture", runtime.GOARCH},
})

func TestInfoString(t *testing.T) {
	infoString := InfoVar.String()
	for _, el := range InfoVar {
		assert.True(t, strings.Contains(infoString, el[0]))
		assert.True(t, strings.Contains(infoString, el[1]))
	}
}
