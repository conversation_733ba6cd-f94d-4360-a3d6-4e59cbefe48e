# Namespace for Universal OTEL monitoring
apiVersion: v1
kind: Namespace
metadata:
  name: monitoring
  labels:
    name: monitoring
    purpose: observability

---
# ServiceAccount for Universal OTEL Collector
apiVersion: v1
kind: ServiceAccount
metadata:
  name: otel-collector-universal
  namespace: monitoring
  labels:
    app: otel-collector-universal
  annotations:
    # Add AWS IAM role annotation if using IRSA (IAM Roles for Service Accounts)
    # eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT-ID:role/otel-collector-role

---
# ClusterRole for Universal OTEL Collector
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: otel-collector-universal
  labels:
    app: otel-collector-universal
rules:
  - apiGroups: [""]
    resources: ["pods", "nodes", "services", "endpoints", "namespaces"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["apps"]
    resources: ["deployments", "replicasets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["extensions"]
    resources: ["deployments", "replicasets"]
    verbs: ["get", "list", "watch"]

---
# ClusterRoleBinding for Universal OTEL Collector
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: otel-collector-universal
  labels:
    app: otel-collector-universal
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: otel-collector-universal
subjects:
  - kind: ServiceAccount
    name: otel-collector-universal
    namespace: monitoring
