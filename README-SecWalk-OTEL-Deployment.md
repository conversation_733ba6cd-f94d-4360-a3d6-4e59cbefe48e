# SecWalk OTEL Collector 部署指南

本指南详细说明如何在Kubernetes集群中部署AWS OpenTelemetry Collector来观测SecWalk服务。

## 概述

此部署包含以下组件：
- **AWS OTEL Collector**: 专门配置用于观测SecWalk服务的遥测数据收集器
- **Kubernetes部署文件**: 完整的K8s资源定义，包括高可用性和监控配置
- **专用配置**: 针对SecWalk服务优化的OTEL配置

## 前置条件

1. **Kubernetes集群**: 版本1.19+
2. **AWS权限**: 需要访问CloudWatch、X-Ray和CloudWatch Logs的权限
3. **Docker镜像**: `aws-otel-collector-secwalk:v0.43.3` 已构建并可用
4. **kubectl**: 已配置并能访问目标集群

## 部署步骤

### 1. 准备Docker镜像

确保Docker镜像已构建并推送到容器注册表：

```bash
# 如果使用本地镜像，需要推送到集群可访问的注册表
docker tag aws-otel-collector-secwalk:v0.43.3 your-registry/aws-otel-collector-secwalk:v0.43.3
docker push your-registry/aws-otel-collector-secwalk:v0.43.3
```

### 2. 配置AWS权限

#### 选项A: 使用IRSA (推荐)
```bash
# 创建IAM角色并关联到ServiceAccount
eksctl create iamserviceaccount \
  --name otel-collector-secwalk \
  --namespace secwalk-monitoring \
  --cluster your-cluster-name \
  --attach-policy-arn arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy \
  --attach-policy-arn arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess \
  --approve
```

#### 选项B: 使用节点IAM角色
确保EKS节点组具有以下策略：
- `CloudWatchAgentServerPolicy`
- `AWSXRayDaemonWriteAccess`

### 3. 部署Kubernetes资源

按顺序部署以下文件：

```bash
# 1. 创建命名空间和RBAC
kubectl apply -f k8s-secwalk-otel-namespace-rbac.yaml

# 2. 创建ConfigMap
kubectl apply -f k8s-secwalk-otel-configmap.yaml

# 3. 部署OTEL Collector
kubectl apply -f k8s-secwalk-otel-deployment.yaml

# 4. 部署监控和自动扩缩容配置（可选）
kubectl apply -f k8s-secwalk-otel-monitoring.yaml
```

### 4. 验证部署

```bash
# 检查Pod状态
kubectl get pods -n secwalk-monitoring

# 检查服务状态
kubectl get svc -n secwalk-monitoring

# 查看日志
kubectl logs -n secwalk-monitoring deployment/otel-collector-secwalk

# 检查健康状态
kubectl port-forward -n secwalk-monitoring svc/otel-collector-secwalk 13133:13133
curl http://localhost:13133/
```

## 配置说明

### 重要配置项

1. **AWS区域**: 在以下文件中调整AWS区域设置
   - `k8s-secwalk-otel-deployment.yaml`: `AWS_REGION` 环境变量
   - `config-secwalk.yaml`: exporters中的region配置

2. **SecWalk服务发现**: 在配置中调整以下设置
   - Prometheus scrape target: `secwalk-service:8080`
   - 根据实际的SecWalk服务名称和端口进行调整

3. **资源限制**: 根据集群资源情况调整
   - CPU: 200m (request) - 1000m (limit)
   - Memory: 512Mi (request) - 1Gi (limit)

### 端口说明

| 端口 | 协议 | 用途 |
|------|------|------|
| 4317 | TCP | OTLP gRPC接收器 |
| 4318 | TCP | OTLP HTTP接收器 |
| 2000 | UDP | AWS X-Ray接收器 |
| 13133 | TCP | 健康检查 |
| 8888 | TCP | 指标暴露 |
| 1777 | TCP | pprof调试 |
| 55679 | TCP | zPages调试 |

## SecWalk服务集成

### 1. 配置SecWalk发送遥测数据

在SecWalk服务中配置OTEL SDK，指向OTEL Collector：

```yaml
# SecWalk服务的环境变量
OTEL_EXPORTER_OTLP_ENDPOINT: "http://otel-collector-secwalk.secwalk-monitoring.svc.cluster.local:4317"
OTEL_SERVICE_NAME: "secwalk"
OTEL_SERVICE_NAMESPACE: "security"
```

### 2. 启用指标暴露

确保SecWalk服务在`/metrics`端点暴露Prometheus格式的指标。

## 监控和告警

### 查看遥测数据

1. **AWS X-Ray**: 查看分布式追踪
2. **CloudWatch Metrics**: 查看性能指标
3. **CloudWatch Logs**: 查看应用日志

### 监控OTEL Collector本身

```bash
# 访问zPages调试界面
kubectl port-forward -n secwalk-monitoring svc/otel-collector-secwalk 55679:55679
# 浏览器访问: http://localhost:55679/debug/tracez

# 查看Collector指标
kubectl port-forward -n secwalk-monitoring svc/otel-collector-secwalk 8888:8888
curl http://localhost:8888/metrics
```

## 故障排除

### 常见问题

1. **Pod启动失败**
   ```bash
   kubectl describe pod -n secwalk-monitoring -l app=otel-collector-secwalk
   ```

2. **AWS权限问题**
   ```bash
   kubectl logs -n secwalk-monitoring deployment/otel-collector-secwalk | grep -i "permission\|access\|auth"
   ```

3. **配置错误**
   ```bash
   kubectl get configmap -n secwalk-monitoring otel-collector-secwalk-config -o yaml
   ```

### 调试模式

临时启用调试输出：
```bash
kubectl patch deployment -n secwalk-monitoring otel-collector-secwalk -p '{"spec":{"template":{"spec":{"containers":[{"name":"otel-collector","env":[{"name":"OTEL_LOG_LEVEL","value":"debug"}]}]}}}}'
```

## 扩展和优化

### 性能调优

1. **批处理大小**: 根据数据量调整batch processor配置
2. **内存限制**: 调整memory_limiter processor
3. **副本数量**: 根据负载调整HPA配置

### 安全加固

1. **网络策略**: 启用并配置NetworkPolicy
2. **Pod安全策略**: 配置PodSecurityPolicy或Pod Security Standards
3. **密钥管理**: 使用Kubernetes Secrets管理敏感配置

## 更新和维护

### 更新OTEL Collector

```bash
# 更新镜像版本
kubectl set image deployment/otel-collector-secwalk -n secwalk-monitoring otel-collector=aws-otel-collector-secwalk:v0.44.0

# 滚动重启
kubectl rollout restart deployment/otel-collector-secwalk -n secwalk-monitoring
```

### 配置更新

```bash
# 更新ConfigMap后重启Pod
kubectl rollout restart deployment/otel-collector-secwalk -n secwalk-monitoring
```

## 快速部署脚本

创建一键部署脚本：

```bash
#!/bin/bash
# deploy-secwalk-otel.sh

set -e

echo "部署SecWalk OTEL Collector..."

# 检查kubectl连接
kubectl cluster-info

# 部署资源
kubectl apply -f k8s-secwalk-otel-namespace-rbac.yaml
kubectl apply -f k8s-secwalk-otel-configmap.yaml
kubectl apply -f k8s-secwalk-otel-deployment.yaml
kubectl apply -f k8s-secwalk-otel-monitoring.yaml

# 等待部署完成
echo "等待Pod启动..."
kubectl wait --for=condition=ready pod -l app=otel-collector-secwalk -n secwalk-monitoring --timeout=300s

# 验证部署
echo "验证部署状态..."
kubectl get pods -n secwalk-monitoring
kubectl get svc -n secwalk-monitoring

echo "部署完成！"
echo "健康检查: kubectl port-forward -n secwalk-monitoring svc/otel-collector-secwalk 13133:13133"
echo "调试界面: kubectl port-forward -n secwalk-monitoring svc/otel-collector-secwalk 55679:55679"
```
