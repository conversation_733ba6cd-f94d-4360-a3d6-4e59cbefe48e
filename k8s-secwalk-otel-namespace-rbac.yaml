# Namespace for SecWalk monitoring components
apiVersion: v1
kind: Namespace
metadata:
  name: secwalk-monitoring
  labels:
    name: secwalk-monitoring
    purpose: observability

---
# ServiceAccount for OTEL Collector
apiVersion: v1
kind: ServiceAccount
metadata:
  name: otel-collector-secwalk
  namespace: secwalk-monitoring
  labels:
    app: otel-collector-secwalk
  annotations:
    # Add AWS IAM role annotation if using IRSA (IAM Roles for Service Accounts)
    # eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT-ID:role/otel-collector-role

---
# ClusterRole for OTEL Collector to access Kubernetes API
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: otel-collector-secwalk
  labels:
    app: otel-collector-secwalk
rules:
  - apiGroups: [""]
    resources: ["pods", "nodes", "services", "endpoints", "namespaces"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["apps"]
    resources: ["deployments", "replicasets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["extensions"]
    resources: ["deployments", "replicasets"]
    verbs: ["get", "list", "watch"]

---
# ClusterRoleBinding to bind the ClusterRole to ServiceAccount
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: otel-collector-secwalk
  labels:
    app: otel-collector-secwalk
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: otel-collector-secwalk
subjects:
  - kind: ServiceAccount
    name: otel-collector-secwalk
    namespace: secwalk-monitoring
