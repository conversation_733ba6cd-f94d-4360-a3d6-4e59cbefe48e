{"clustertargets": [{"type": "EKS", "targets": [{"name": "collector-ci-amd64-1-26", "region": "us-west-2", "excluded_tests": ["containerinsight_eks"]}, {"name": "collector-ci-amd64-1-27", "region": "us-west-2", "excluded_tests": ["containerinsight_eks"]}, {"name": "collector-ci-amd64-1-28", "region": "us-west-2", "excluded_tests": ["containerinsight_eks"]}, {"name": "collector-ci-amd64-1-29", "region": "us-west-2", "excluded_tests": ["containerinsight_eks"]}, {"name": "collector-ci-amd64-1-30", "region": "us-west-2", "excluded_tests": ["containerinsight_eks"]}, {"name": "collector-ci-amd64-1-31", "region": "us-west-2", "excluded_tests": ["containerinsight_eks"]}]}, {"type": "EKS_ARM64", "targets": [{"name": "collector-ci-arm64-1-26", "region": "us-west-2", "excluded_tests": ["containerinsight_eks"]}, {"name": "collector-ci-arm64-1-27", "region": "us-west-2", "excluded_tests": ["containerinsight_eks"]}, {"name": "collector-ci-arm64-1-28", "region": "us-west-2", "excluded_tests": ["containerinsight_eks"]}, {"name": "collector-ci-arm64-1-29", "region": "us-west-2", "excluded_tests": ["containerinsight_eks"]}, {"name": "collector-ci-arm64-1-30", "region": "us-west-2", "excluded_tests": ["containerinsight_eks"]}, {"name": "collector-ci-arm64-1-31", "region": "us-west-2", "excluded_tests": ["containerinsight_eks"]}]}, {"type": "EKS_FARGATE", "targets": [{"name": "collector-ci-fargate-1-26", "region": "us-west-2"}, {"name": "collector-ci-fargate-1-27", "region": "us-west-2"}, {"name": "collector-ci-fargate-1-28", "region": "us-west-2"}, {"name": "collector-ci-fargate-1-29", "region": "us-west-2"}, {"name": "collector-ci-fargate-1-30", "region": "us-west-2"}, {"name": "collector-ci-fargate-1-31", "region": "us-west-2"}]}], "tests": [{"case_name": "xrayreceiver", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64", "CANARY"]}, {"case_name": "xrayreceiver_mock", "platforms": ["PERF"]}, {"case_name": "otlp_metric_amp", "platforms": ["EC2"]}, {"case_name": "statsd", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64"]}, {"case_name": "statsd_mock", "platforms": ["LOCAL", "EC2", "ECS", "EKS", "EKS_ARM64", "PERF"]}, {"case_name": "kafka_otlp_metric_2_8_1", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64"]}, {"case_name": "kafka_otlp_metric_3_2_0", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64"]}, {"case_name": "kafka_otlp_trace_2_8_1", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64"]}, {"case_name": "kafka_otlp_trace_3_2_0", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64"]}, {"case_name": "kafka_otlp_metric_mock_2_8_1", "platforms": ["PERF"]}, {"case_name": "kafka_otlp_metric_mock_3_2_0", "platforms": ["PERF"]}, {"case_name": "kafka_otlp_mock_2_8_1", "platforms": ["PERF"]}, {"case_name": "kafka_otlp_mock_3_2_0", "platforms": ["PERF"]}, {"case_name": "otlp_metric", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64", "CANARY"]}, {"case_name": "otlp_logs", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64", "CANARY"]}, {"case_name": "configmap_provider_http", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64"]}, {"case_name": "configmap_provider_https", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64"]}, {"case_name": "configmap_provider_s3", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64"]}, {"case_name": "otlp_metric_mock", "platforms": ["LOCAL", "PERF"]}, {"case_name": "otlp_metric_adot_operator", "platforms": ["EKS_ADOT_OPERATOR", "EKS_ADOT_OPERATOR_ARM64"]}, {"case_name": "otlp_trace", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64", "CANARY"]}, {"case_name": "otlp_trace_adot_operator", "platforms": ["EKS_ADOT_OPERATOR", "EKS_ADOT_OPERATOR_ARM64"]}, {"case_name": "otlp_trace_resourcedetection_ec2", "platforms": ["EC2"]}, {"case_name": "otlp_trace_resourcedetection_ecs", "platforms": ["ECS"]}, {"case_name": "otlp_trace_resourcedetection_eks", "platforms": ["EKS", "EKS_ARM64"]}, {"case_name": "otlp_mock", "platforms": ["LOCAL", "PERF", "CANARY"]}, {"case_name": "ecsmetrics", "platforms": ["ECS"]}, {"case_name": "otlp_grpc_exporter_cw_amp_xray_ecs", "platforms": ["ECS"]}, {"case_name": "otlp_grpc_exporter_metric_mock", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64", "LOCAL", "PERF", "CANARY"]}, {"case_name": "otlp_grpc_exporter_trace_mock", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64", "LOCAL", "PERF", "CANARY"]}, {"case_name": "otlp_http_exporter_metric_mock", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64", "LOCAL", "PERF", "CANARY"]}, {"case_name": "otlp_http_exporter_trace_mock", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64", "LOCAL", "PERF", "CANARY"]}, {"case_name": "sapm_exporter_trace_mock", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64", "LOCAL", "PERF"]}, {"case_name": "signalfx_exporter_metric_mock", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64", "LOCAL", "PERF"]}, {"case_name": "datadog_exporter_metric_mock", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64", "LOCAL", "PERF"]}, {"case_name": "datadog_exporter_trace_mock", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64", "LOCAL", "PERF"]}, {"case_name": "prometheus_mock", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64", "LOCAL", "PERF"]}, {"case_name": "prometheus_static", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64"]}, {"case_name": "prometheus_static_adot_operator", "platforms": ["EKS_ADOT_OPERATOR", "EKS_ADOT_OPERATOR_ARM64"]}, {"case_name": "prometheus_sd", "platforms": ["EKS", "EKS_ARM64"]}, {"case_name": "prometheus_sd_adot_operator", "platforms": ["EKS_ADOT_OPERATOR", "EKS_ADOT_OPERATOR_ARM64"]}, {"case_name": "containerinsight_eks_prometheus", "platforms": ["EKS"]}, {"case_name": "containerinsight_eks", "platforms": ["EKS", "EKS_ARM64"]}, {"case_name": "containerinsights_eks_containerd", "platforms": ["EKS", "EKS_ARM64"]}, {"case_name": "zipkin_mock", "platforms": ["LOCAL", "EC2", "ECS", "EKS", "EKS_ARM64", "PERF"]}, {"case_name": "jaeger_mock", "platforms": ["LOCAL", "EC2", "ECS", "EKS", "EKS_ARM64", "PERF"]}, {"case_name": "logzio_exporter_trace_mock", "platforms": ["LOCAL", "EC2", "ECS", "EKS", "EKS_ARM64", "PERF"]}, {"case_name": "ssm_package", "platforms": ["EC2", "CANARY"]}, {"case_name": "ecs_instance_metrics", "platforms": ["ECS"]}, {"case_name": "eks_containerinsights_fargate_docker", "platforms": ["EKS_FARGATE"]}, {"case_name": "eks_containerinsights_fargate_docker_metric", "platforms": ["EKS_FARGATE"]}, {"case_name": "ecshealthcheck", "platforms": ["ECS"]}, {"case_name": "containerinsight_ecs_prometheus", "platforms": ["ECS"]}, {"case_name": "otlp_metric_k8sattr", "platforms": ["EKS", "EKS_ARM64"]}, {"case_name": "otlp_metric_k8sattr_podassoc", "platforms": ["EKS", "EKS_ARM64"]}, {"case_name": "otlp_trace_k8sattr", "platforms": ["EKS", "EKS_ARM64"]}, {"case_name": "otlp_trace_k8sattr_podassoc", "platforms": ["EKS", "EKS_ARM64"]}, {"case_name": "otlp_trace_loadbalancing", "platforms": ["EC2", "ECS", "EKS", "EKS_ARM64"]}]}