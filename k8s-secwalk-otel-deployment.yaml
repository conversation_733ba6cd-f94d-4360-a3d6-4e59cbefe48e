# OTEL Collector Deployment for SecWalk Service Observability
apiVersion: apps/v1
kind: Deployment
metadata:
  name: otel-collector-secwalk
  namespace: secwalk-monitoring
  labels:
    app: otel-collector-secwalk
    component: otel-collector
spec:
  replicas: 2  # High availability setup
  selector:
    matchLabels:
      app: otel-collector-secwalk
  template:
    metadata:
      labels:
        app: otel-collector-secwalk
        component: otel-collector
    spec:
      serviceAccountName: otel-collector-secwalk
      containers:
        - name: otel-collector
          image: aws-otel-collector-secwalk:v0.43.3
          imagePullPolicy: IfNotPresent
          command:
            - "/awscollector"
            - "--config=/etc/otel-collector-config/otel-config.yaml"
          ports:
            - name: otlp-grpc
              containerPort: 4317
              protocol: TCP
            - name: otlp-http
              containerPort: 4318
              protocol: TCP
            - name: xray-udp
              containerPort: 2000
              protocol: UDP
            - name: health-check
              containerPort: 13133
              protocol: TCP
            - name: pprof
              containerPort: 1777
              protocol: TCP
            - name: zpages
              containerPort: 55679
              protocol: TCP
            - name: metrics
              containerPort: 8888
              protocol: TCP
          env:
            - name: AWS_REGION
              value: "us-west-2"  # Adjust to your AWS region
            - name: DEPLOYMENT_ENVIRONMENT
              value: "production"  # Adjust as needed
            - name: K8S_CLUSTER_NAME
              value: "secwalk-cluster"  # Adjust to your cluster name
            - name: AWS_ROLE_ARN
              value: ""  # Set if using IRSA
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
            requests:
              cpu: 200m
              memory: 512Mi
          volumeMounts:
            - name: otel-collector-config-vol
              mountPath: /etc/otel-collector-config
              readOnly: true
          livenessProbe:
            httpGet:
              path: /
              port: health-check
            initialDelaySeconds: 15
            periodSeconds: 20
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /
              port: health-check
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 3
            failureThreshold: 3
      volumes:
        - name: otel-collector-config-vol
          configMap:
            name: otel-collector-secwalk-config
            items:
              - key: otel-config.yaml
                path: otel-config.yaml
      # Node affinity to spread across different nodes for HA
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - otel-collector-secwalk
                topologyKey: kubernetes.io/hostname

---
# Service to expose OTEL Collector
apiVersion: v1
kind: Service
metadata:
  name: otel-collector-secwalk
  namespace: secwalk-monitoring
  labels:
    app: otel-collector-secwalk
    component: service
spec:
  type: ClusterIP
  ports:
    - name: otlp-grpc
      port: 4317
      targetPort: otlp-grpc
      protocol: TCP
    - name: otlp-http
      port: 4318
      targetPort: otlp-http
      protocol: TCP
    - name: xray-udp
      port: 2000
      targetPort: xray-udp
      protocol: UDP
    - name: health-check
      port: 13133
      targetPort: health-check
      protocol: TCP
    - name: pprof
      port: 1777
      targetPort: pprof
      protocol: TCP
    - name: zpages
      port: 55679
      targetPort: zpages
      protocol: TCP
    - name: metrics
      port: 8888
      targetPort: metrics
      protocol: TCP
  selector:
    app: otel-collector-secwalk

---
# Headless Service for StatefulSet-like behavior (if needed)
apiVersion: v1
kind: Service
metadata:
  name: otel-collector-secwalk-headless
  namespace: secwalk-monitoring
  labels:
    app: otel-collector-secwalk
    component: headless-service
spec:
  type: ClusterIP
  clusterIP: None
  ports:
    - name: otlp-grpc
      port: 4317
      targetPort: otlp-grpc
      protocol: TCP
  selector:
    app: otel-collector-secwalk
