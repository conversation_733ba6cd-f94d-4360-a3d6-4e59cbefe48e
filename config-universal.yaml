# AWS OpenTelemetry Collector Universal Configuration
# 通用配置，支持通过环境变量动态配置监测目标

extensions:
  health_check:
    endpoint: 0.0.0.0:13133
  pprof:
    endpoint: 0.0.0.0:1777
  zpages:
    endpoint: 0.0.0.0:55679

receivers:
  # OTLP receiver for receiving telemetry data from any service
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318
        cors:
          allowed_origins:
            - http://*
            - https://*

  # AWS X-Ray receiver for distributed tracing
  awsxray:
    endpoint: 0.0.0.0:2000
    transport: udp

  # Prometheus receiver for scraping metrics from target services
  prometheus:
    config:
      scrape_configs:
        # 动态配置的服务监控目标
        - job_name: '${MONITOR_SERVICE_NAME:-default-service}'
          scrape_interval: ${SCRAPE_INTERVAL:-30s}
          static_configs:
            - targets: ['${MONITOR_SERVICE_TARGET:-localhost:8080}']
          metrics_path: '${METRICS_PATH:-/metrics}'
          scrape_timeout: ${SCRAPE_TIMEOUT:-10s}
          # 支持多个服务目标
        - job_name: 'additional-service-1'
          scrape_interval: ${SCRAPE_INTERVAL_2:-30s}
          static_configs:
            - targets: ['${MONITOR_SERVICE_TARGET_2:-}']
          metrics_path: '${METRICS_PATH_2:-/metrics}'
          scrape_timeout: ${SCRAPE_TIMEOUT_2:-10s}
        - job_name: 'additional-service-2'
          scrape_interval: ${SCRAPE_INTERVAL_3:-30s}
          static_configs:
            - targets: ['${MONITOR_SERVICE_TARGET_3:-}']
          metrics_path: '${METRICS_PATH_3:-/metrics}'
          scrape_timeout: ${SCRAPE_TIMEOUT_3:-10s}

processors:
  # Batch processor for traces
  batch/traces:
    timeout: ${BATCH_TRACES_TIMEOUT:-2s}
    send_batch_size: ${BATCH_TRACES_SIZE:-100}
    send_batch_max_size: ${BATCH_TRACES_MAX_SIZE:-1000}

  # Batch processor for metrics
  batch/metrics:
    timeout: ${BATCH_METRICS_TIMEOUT:-30s}
    send_batch_size: ${BATCH_METRICS_SIZE:-1000}
    send_batch_max_size: ${BATCH_METRICS_MAX_SIZE:-1500}

  # Batch processor for logs
  batch/logs:
    timeout: ${BATCH_LOGS_TIMEOUT:-5s}
    send_batch_size: ${BATCH_LOGS_SIZE:-200}
    send_batch_max_size: ${BATCH_LOGS_MAX_SIZE:-500}

  # Resource processor to add service-specific attributes
  resource:
    attributes:
      - key: service.name
        value: ${MONITOR_SERVICE_NAME:-unknown-service}
        action: upsert
      - key: service.namespace
        value: ${SERVICE_NAMESPACE:-default}
        action: upsert
      - key: deployment.environment
        value: ${DEPLOYMENT_ENVIRONMENT:-production}
        action: upsert
      - key: k8s.cluster.name
        value: ${K8S_CLUSTER_NAME:-unknown-cluster}
        action: upsert
      - key: service.version
        value: ${SERVICE_VERSION:-unknown}
        action: upsert

  # Memory limiter to prevent OOM
  memory_limiter:
    limit_mib: ${MEMORY_LIMIT_MIB:-512}
    spike_limit_mib: ${MEMORY_SPIKE_LIMIT_MIB:-128}
    check_interval: ${MEMORY_CHECK_INTERVAL:-5s}

  # Attributes processor for service-specific filtering and transformation
  attributes/service:
    actions:
      - key: http.user_agent
        action: delete
      - key: service.request_id
        action: upsert
        from_attribute: trace.id
      - key: service.component
        value: ${SERVICE_COMPONENT:-application}
        action: upsert

exporters:
  # AWS X-Ray exporter for distributed tracing
  awsxray:
    no_verify_ssl: false
    local_mode: false
    region: ${AWS_REGION:-us-west-2}

  # AWS CloudWatch EMF exporter for metrics
  awsemf:
    no_verify_ssl: false
    region: ${AWS_REGION:-us-west-2}
    namespace: ${CLOUDWATCH_NAMESPACE:-CustomApp/Metrics}
    log_group_name: ${CLOUDWATCH_LOG_GROUP:-/aws/otel/application}
    log_stream_name: ${CLOUDWATCH_LOG_STREAM:-application-metrics}
    dimension_rollup_option: ${DIMENSION_ROLLUP:-NoDimensionRollup}
    parse_json_encoded_attr_values: ["Sources", "kubernetes"]
    metric_declarations:
      - dimensions: [[service.name], [service.name, service.namespace]]
        metric_name_selectors:
          - ${METRIC_SELECTOR_1:-*}
          - http_requests_total
          - http_request_duration_seconds
          - process_cpu_seconds_total
          - process_memory_bytes

  # AWS CloudWatch Logs exporter for application logs
  awscloudwatchlogs:
    log_group_name: ${CLOUDWATCH_LOGS_GROUP:-/aws/otel/application/logs}
    log_stream_name: ${CLOUDWATCH_LOGS_STREAM:-application-logs}
    region: ${AWS_REGION:-us-west-2}

  # Debug exporter for troubleshooting (controlled by environment variable)
  debug:
    verbosity: ${DEBUG_VERBOSITY:-basic}

service:
  pipelines:
    # Traces pipeline for distributed tracing
    traces:
      receivers: [otlp, awsxray]
      processors: [memory_limiter, resource, attributes/service, batch/traces]
      exporters: [awsxray]

    # Metrics pipeline for performance monitoring
    metrics:
      receivers: [otlp, prometheus]
      processors: [memory_limiter, resource, batch/metrics]
      exporters: [awsemf]

    # Logs pipeline for application logging
    logs:
      receivers: [otlp]
      processors: [memory_limiter, resource, batch/logs]
      exporters: [awscloudwatchlogs]

  extensions: [health_check, pprof, zpages]

  # Telemetry configuration for the collector itself
  telemetry:
    logs:
      level: ${OTEL_LOG_LEVEL:-info}
    metrics:
      level: ${OTEL_METRICS_LEVEL:-basic}
      address: 0.0.0.0:8888
