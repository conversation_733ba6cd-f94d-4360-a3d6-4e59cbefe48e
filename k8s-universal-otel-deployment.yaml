# Universal OTEL Collector Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: otel-collector-universal
  namespace: monitoring
  labels:
    app: otel-collector-universal
    component: otel-collector
spec:
  replicas: 2
  selector:
    matchLabels:
      app: otel-collector-universal
  template:
    metadata:
      labels:
        app: otel-collector-universal
        component: otel-collector
    spec:
      serviceAccountName: otel-collector-universal
      containers:
        - name: otel-collector
          image: aws-otel-collector-secwalk:v0.43.3  # 使用已构建的通用镜像
          imagePullPolicy: IfNotPresent
          command:
            - "/awscollector"
            - "--config=/etc/otel-collector-config/otel-config.yaml"
          ports:
            - name: otlp-grpc
              containerPort: 4317
              protocol: TCP
            - name: otlp-http
              containerPort: 4318
              protocol: TCP
            - name: xray-udp
              containerPort: 2000
              protocol: UDP
            - name: health-check
              containerPort: 13133
              protocol: TCP
            - name: pprof
              containerPort: 1777
              protocol: TCP
            - name: zpages
              containerPort: 55679
              protocol: TCP
            - name: metrics
              containerPort: 8888
              protocol: TCP
          env:
            # AWS配置
            - name: AWS_REGION
              value: "us-west-2"
            
            # 监测目标配置 - 主要服务 (SecWalk)
            - name: MONITOR_SERVICE_NAME
              value: "secwalk"
            - name: MONITOR_SERVICE_TARGET
              value: "secwalk-service:8080"
            - name: SCRAPE_INTERVAL
              value: "30s"
            - name: METRICS_PATH
              value: "/metrics"
            - name: SCRAPE_TIMEOUT
              value: "10s"
            
            # 监测目标配置 - 额外服务1 (可选)
            - name: MONITOR_SERVICE_TARGET_2
              value: ""  # 留空表示不监测，可以设置为 "service2:9090"
            - name: SCRAPE_INTERVAL_2
              value: "30s"
            - name: METRICS_PATH_2
              value: "/metrics"
            
            # 监测目标配置 - 额外服务2 (可选)
            - name: MONITOR_SERVICE_TARGET_3
              value: ""  # 留空表示不监测，可以设置为 "service3:9091"
            - name: SCRAPE_INTERVAL_3
              value: "30s"
            - name: METRICS_PATH_3
              value: "/metrics"
            
            # 服务标识配置
            - name: SERVICE_NAMESPACE
              value: "security"
            - name: SERVICE_COMPONENT
              value: "security-scanner"
            - name: SERVICE_VERSION
              value: "v1.0.0"
            
            # 集群和环境配置
            - name: DEPLOYMENT_ENVIRONMENT
              value: "production"
            - name: K8S_CLUSTER_NAME
              value: "main-cluster"
            
            # CloudWatch配置
            - name: CLOUDWATCH_NAMESPACE
              value: "SecWalk/Metrics"
            - name: CLOUDWATCH_LOG_GROUP
              value: "/aws/otel/secwalk"
            - name: CLOUDWATCH_LOG_STREAM
              value: "secwalk-metrics"
            - name: CLOUDWATCH_LOGS_GROUP
              value: "/aws/otel/secwalk/logs"
            - name: CLOUDWATCH_LOGS_STREAM
              value: "secwalk-application-logs"
            
            # 性能调优配置
            - name: MEMORY_LIMIT_MIB
              value: "512"
            - name: MEMORY_SPIKE_LIMIT_MIB
              value: "128"
            - name: BATCH_TRACES_TIMEOUT
              value: "2s"
            - name: BATCH_TRACES_SIZE
              value: "100"
            - name: BATCH_METRICS_TIMEOUT
              value: "30s"
            - name: BATCH_METRICS_SIZE
              value: "1000"
            
            # 调试配置
            - name: OTEL_LOG_LEVEL
              value: "info"
            - name: DEBUG_VERBOSITY
              value: "basic"
            
            # 指标选择器
            - name: METRIC_SELECTOR_1
              value: "secwalk.*"
              
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
            requests:
              cpu: 200m
              memory: 512Mi
          volumeMounts:
            - name: otel-collector-config-vol
              mountPath: /etc/otel-collector-config
              readOnly: true
          livenessProbe:
            httpGet:
              path: /
              port: health-check
            initialDelaySeconds: 15
            periodSeconds: 20
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /
              port: health-check
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 3
            failureThreshold: 3
      volumes:
        - name: otel-collector-config-vol
          configMap:
            name: otel-collector-universal-config
            items:
              - key: otel-config.yaml
                path: otel-config.yaml
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - otel-collector-universal
                topologyKey: kubernetes.io/hostname

---
# Service to expose OTEL Collector
apiVersion: v1
kind: Service
metadata:
  name: otel-collector-universal
  namespace: monitoring
  labels:
    app: otel-collector-universal
    component: service
spec:
  type: ClusterIP
  ports:
    - name: otlp-grpc
      port: 4317
      targetPort: otlp-grpc
      protocol: TCP
    - name: otlp-http
      port: 4318
      targetPort: otlp-http
      protocol: TCP
    - name: xray-udp
      port: 2000
      targetPort: xray-udp
      protocol: UDP
    - name: health-check
      port: 13133
      targetPort: health-check
      protocol: TCP
    - name: pprof
      port: 1777
      targetPort: pprof
      protocol: TCP
    - name: zpages
      port: 55679
      targetPort: zpages
      protocol: TCP
    - name: metrics
      port: 8888
      targetPort: metrics
      protocol: TCP
  selector:
    app: otel-collector-universal
