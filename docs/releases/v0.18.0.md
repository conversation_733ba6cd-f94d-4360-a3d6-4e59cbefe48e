# Changelog

## [v0.18.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.18.0) (2022-05-12)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.17.0...v0.18.0)

**Upstream release notes:**
- [v0.51.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.51.0)
- [v0.50.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.50.0)
- [v0.49.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.49.0)
- [v0.48.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.48.0)
- [v0.47.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.47.0)
- [v0.46.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.46.0)

**Implemented enhancements:**

- Add \[extension/sigv4authextension\] to ADOT Collector \(PR\) [\#1066](https://github.com/aws-observability/aws-otel-collector/pull/1066) ([erichsueh3](https://github.com/erichsueh3))


**Merged pull requests:**

- Update OTel dependencies to v0.51.0 [\#1217](https://github.com/aws-observability/aws-otel-collector/pull/1217) ([bryan-aguilar](https://github.com/bryan-aguilar))
- Upgraded dependencies of collector to v0.50.0 [\#1197](https://github.com/aws-observability/aws-otel-collector/pull/1197) ([Kausik-A](https://github.com/Kausik-A))
- Upgrade dependency 0.49.0 [\#1190](https://github.com/aws-observability/aws-otel-collector/pull/1190) ([ruthvik17](https://github.com/ruthvik17))
- Update OTEL Collector version to v0.48.0 [\#1178](https://github.com/aws-observability/aws-otel-collector/pull/1178) ([bryan-aguilar](https://github.com/bryan-aguilar))
- Change job to service.name in configs [\#1175](https://github.com/aws-observability/aws-otel-collector/pull/1175) ([bryan-aguilar](https://github.com/bryan-aguilar))
- Upgrade Collector Dependencies to v0.47.0 [\#1069](https://github.com/aws-observability/aws-otel-collector/pull/1069) ([erichsueh3](https://github.com/erichsueh3))
- Ensure feature gate flag values are applied to global registry [\#1053](https://github.com/aws-observability/aws-otel-collector/pull/1053) ([Aneurysm9](https://github.com/Aneurysm9))
- Upgrade collector to v0.46.0 [\#1037](https://github.com/aws-observability/aws-otel-collector/pull/1037) ([cedricziel](https://github.com/cedricziel))


\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
