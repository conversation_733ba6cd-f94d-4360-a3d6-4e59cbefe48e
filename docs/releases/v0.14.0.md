# Changelog

## [v0.14.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.14.0) (2021-10-29)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.13.0...v0.14.0)

**Closed issues:**

- Operator Image Mirroring With new ADOT Collector Releases [\#693](https://github.com/aws-observability/aws-otel-collector/issues/693)
- Can't use Prometheus Exporter on AWS-OTel [\#690](https://github.com/aws-observability/aws-otel-collector/issues/690)
- <PERSON><PERSON>'s add\_event and record\_exception not working with X-Ray [\#666](https://github.com/aws-observability/aws-otel-collector/issues/666)
- Loading Collector config from AOT\_CONFIG\_CONTENT causes ECS Task to fail on v0.13.0 [\#662](https://github.com/aws-observability/aws-otel-collector/issues/662)
- Docker example failed to run because 'service' has invalid keys: telemetry [\#657](https://github.com/aws-observability/aws-otel-collector/issues/657)

**Merged pull requests:**

- Upgrade OTel dependencies to v0.38.0. [\#696](https://github.com/aws-observability/aws-otel-collector/pull/696) ([jefchien](https://github.com/jefchien))
- Add bug report and customer inquiry templates [\#692](https://github.com/aws-observability/aws-otel-collector/pull/692) ([sethAmazon](https://github.com/sethAmazon))
- Improve build process by caching go dependencies [\#689](https://github.com/aws-observability/aws-otel-collector/pull/689) ([khanhntd](https://github.com/khanhntd))
- Separate Prometheus receiver from existing ECS AMP config files. [\#686](https://github.com/aws-observability/aws-otel-collector/pull/686) ([jefchien](https://github.com/jefchien))
- Update v0.14.0 release notes. [\#683](https://github.com/aws-observability/aws-otel-collector/pull/683) ([jefchien](https://github.com/jefchien))
- Add the Prometheus receiver to the ECS AMP config files. [\#682](https://github.com/aws-observability/aws-otel-collector/pull/682) ([jefchien](https://github.com/jefchien))
- Bump version to v0.14.0 [\#681](https://github.com/aws-observability/aws-otel-collector/pull/681) ([jefchien](https://github.com/jefchien))
- Log on failure to get latest uploaded release candidate [\#678](https://github.com/aws-observability/aws-otel-collector/pull/678) ([sethAmazon](https://github.com/sethAmazon))
- Upgrade OTel dependencies to v0.37.0. [\#676](https://github.com/aws-observability/aws-otel-collector/pull/676) ([jefchien](https://github.com/jefchien))
- Add Nightly Build Unit Test [\#675](https://github.com/aws-observability/aws-otel-collector/pull/675) ([sethAmazon](https://github.com/sethAmazon))
- Bump aws-otel-collector version to v0.13.0 [\#669](https://github.com/aws-observability/aws-otel-collector/pull/669) ([JamesJHPark](https://github.com/JamesJHPark))
- Add Collector configs for ECS deployment variants [\#667](https://github.com/aws-observability/aws-otel-collector/pull/667) ([anuraaga](https://github.com/anuraaga))
- Fix git checkout error in release WF [\#661](https://github.com/aws-observability/aws-otel-collector/pull/661) ([mxiamxia](https://github.com/mxiamxia))
- Add AppRunner default config for X-Ray integration [\#647](https://github.com/aws-observability/aws-otel-collector/pull/647) ([mxiamxia](https://github.com/mxiamxia))



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
