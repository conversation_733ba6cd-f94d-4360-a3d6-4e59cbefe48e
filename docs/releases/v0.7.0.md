# Changelog

## [v0.7.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.7.0) (2021-01-29)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/pkg/lambdacomponents/v0.6.0...v0.7.0)

**Implemented enhancements:**

- Publish the Docker image [\#47](https://github.com/aws-observability/aws-otel-collector/issues/47)

**Fixed bugs:**

- XRay exporter is failing the exports to the collector  [\#129](https://github.com/aws-observability/aws-otel-collector/issues/129)

**Closed issues:**

- \[EMF exporter\]Wired dimension when using StatsD receiver and EMF exporter [\#309](https://github.com/aws-observability/aws-otel-collector/issues/309)
- AOC stops for long running  ECS task [\#281](https://github.com/aws-observability/aws-otel-collector/issues/281)
- OTEL Client missing AwsXRayIdGenerator silently fails [\#261](https://github.com/aws-observability/aws-otel-collector/issues/261)
- Document how to start with docker run [\#258](https://github.com/aws-observability/aws-otel-collector/issues/258)
- Docker-demo doesn't work [\#187](https://github.com/aws-observability/aws-otel-collector/issues/187)
- CFN Example Uses Admin Permissions [\#157](https://github.com/aws-observability/aws-otel-collector/issues/157)
- Source for aws-otel-emitter app referenced in eks demo docs [\#133](https://github.com/aws-observability/aws-otel-collector/issues/133)
- Prometheus receiver fails for kube-system components and the API server [\#97](https://github.com/aws-observability/aws-otel-collector/issues/97)
- Benchmarks - machine configuration [\#94](https://github.com/aws-observability/aws-otel-collector/issues/94)

**Merged pull requests:**

- Finalize the components will be enabled for the next release [\#327](https://github.com/aws-observability/aws-otel-collector/pull/327) ([mxiamxia](https://github.com/mxiamxia))
- Finalize the components will be enabled for the next release [\#322](https://github.com/aws-observability/aws-otel-collector/pull/322) ([mxiamxia](https://github.com/mxiamxia))
- Finalize the components will be enabled for the next release [\#321](https://github.com/aws-observability/aws-otel-collector/pull/321) ([mxiamxia](https://github.com/mxiamxia))
- Update collector reference to v0.19.0 [\#320](https://github.com/aws-observability/aws-otel-collector/pull/320) ([vastin](https://github.com/vastin))
- fix the outdated EC2 user guide and rename CI CD workflow name to fix… [\#315](https://github.com/aws-observability/aws-otel-collector/pull/315) ([mxiamxia](https://github.com/mxiamxia))
- Fix AWS credentials profile [\#307](https://github.com/aws-observability/aws-otel-collector/pull/307) ([vastin](https://github.com/vastin))
- fix: use the correct unsigned package folder for windows pkg sign [\#304](https://github.com/aws-observability/aws-otel-collector/pull/304) ([haojhcwa](https://github.com/haojhcwa))
- chore: Add windows package signing step [\#303](https://github.com/aws-observability/aws-otel-collector/pull/303) ([haojhcwa](https://github.com/haojhcwa))
- Bump github.com/open-telemetry/opentelemetry-collector-contrib/exporter/awsxrayexporter from 0.17.0 to 0.18.0 [\#302](https://github.com/aws-observability/aws-otel-collector/pull/302) ([dependabot[bot]](https://github.com/apps/dependabot))
- Bump github.com/open-telemetry/opentelemetry-collector-contrib/exporter/signalfxexporter from 0.17.0 to 0.18.0 [\#301](https://github.com/aws-observability/aws-otel-collector/pull/301) ([dependabot[bot]](https://github.com/apps/dependabot))
- Bump github.com/open-telemetry/opentelemetry-collector-contrib/exporter/datadogexporter from 0.17.0 to 0.18.0 [\#300](https://github.com/aws-observability/aws-otel-collector/pull/300) ([dependabot[bot]](https://github.com/apps/dependabot))
- Bump github.com/open-telemetry/opentelemetry-collector-contrib/exporter/sapmexporter from 0.17.0 to 0.18.0 [\#299](https://github.com/aws-observability/aws-otel-collector/pull/299) ([dependabot[bot]](https://github.com/apps/dependabot))
- Bump github.com/open-telemetry/opentelemetry-collector-contrib/exporter/splunkhecexporter from 0.17.0 to 0.18.0 [\#298](https://github.com/aws-observability/aws-otel-collector/pull/298) ([dependabot[bot]](https://github.com/apps/dependabot))
- Bump github.com/open-telemetry/opentelemetry-collector-contrib/receiver/awsecscontainermetricsreceiver from 0.17.0 to 0.18.0 [\#297](https://github.com/aws-observability/aws-otel-collector/pull/297) ([dependabot[bot]](https://github.com/apps/dependabot))
- Bump github.com/open-telemetry/opentelemetry-collector-contrib/processor/metricstransformprocessor from 0.17.0 to 0.18.0 [\#296](https://github.com/aws-observability/aws-otel-collector/pull/296) ([dependabot[bot]](https://github.com/apps/dependabot))
- Bump github.com/open-telemetry/opentelemetry-collector-contrib/exporter/newrelicexporter from 0.17.0 to 0.18.0 [\#295](https://github.com/aws-observability/aws-otel-collector/pull/295) ([dependabot[bot]](https://github.com/apps/dependabot))
- Bump github.com/open-telemetry/opentelemetry-collector-contrib/exporter/awsprometheusremotewriteexporter from 0.17.0 to 0.18.0 [\#294](https://github.com/aws-observability/aws-otel-collector/pull/294) ([dependabot[bot]](https://github.com/apps/dependabot))
- fix performance model generation [\#290](https://github.com/aws-observability/aws-otel-collector/pull/290) ([wyTrivail](https://github.com/wyTrivail))
- bypass the limit of workflow can only spin 256 jobs for a single defined job [\#288](https://github.com/aws-observability/aws-otel-collector/pull/288) ([wyTrivail](https://github.com/wyTrivail))
- add a script to release packages to s3 [\#287](https://github.com/aws-observability/aws-otel-collector/pull/287) ([wyTrivail](https://github.com/wyTrivail))
- Use aws cli instead of old testing framework for candidate uploading [\#285](https://github.com/aws-observability/aws-otel-collector/pull/285) ([wyTrivail](https://github.com/wyTrivail))
- Expand ec2 test to more ami and arm [\#284](https://github.com/aws-observability/aws-otel-collector/pull/284) ([wyTrivail](https://github.com/wyTrivail))
- Revert "disable cronjob soaking" [\#283](https://github.com/aws-observability/aws-otel-collector/pull/283) ([mxiamxia](https://github.com/mxiamxia))
- Update README.md [\#282](https://github.com/aws-observability/aws-otel-collector/pull/282) ([awssandra](https://github.com/awssandra))
- Update the user guides for examples to reflect the latest changes [\#279](https://github.com/aws-observability/aws-otel-collector/pull/279) ([mxiamxia](https://github.com/mxiamxia))
- Add ECS template configurations for prometheus pipeline [\#278](https://github.com/aws-observability/aws-otel-collector/pull/278) ([JasonXZLiu](https://github.com/JasonXZLiu))
- added prometheus ecs test cases [\#277](https://github.com/aws-observability/aws-otel-collector/pull/277) ([amanbrar1999](https://github.com/amanbrar1999))
- fix the file-changes-action bug in workflow [\#276](https://github.com/aws-observability/aws-otel-collector/pull/276) ([mxiamxia](https://github.com/mxiamxia))
- Fix getting started template with IRSA [\#275](https://github.com/aws-observability/aws-otel-collector/pull/275) ([JasonXZLiu](https://github.com/JasonXZLiu))
- Update Collector to v0.17.0 [\#273](https://github.com/aws-observability/aws-otel-collector/pull/273) ([tigrannajaryan](https://github.com/tigrannajaryan))
- Update AWS environment variable names in extra configuration file [\#269](https://github.com/aws-observability/aws-otel-collector/pull/269) ([mxiamxia](https://github.com/mxiamxia))
- merge the changes from v0.6.0 branch to main branch [\#267](https://github.com/aws-observability/aws-otel-collector/pull/267) ([mxiamxia](https://github.com/mxiamxia))
- Delete build-and-push-public-ecr.yaml [\#265](https://github.com/aws-observability/aws-otel-collector/pull/265) ([wyTrivail](https://github.com/wyTrivail))
- Change Logging File Permission from 0755 to 0750 [\#264](https://github.com/aws-observability/aws-otel-collector/pull/264) ([JasonXZLiu](https://github.com/JasonXZLiu))
- Update Performance Model [\#262](https://github.com/aws-observability/aws-otel-collector/pull/262) ([github-actions[bot]](https://github.com/apps/github-actions))
- Add CodeQL Security Scanning Workflow for AOC [\#252](https://github.com/aws-observability/aws-otel-collector/pull/252) ([JasonXZLiu](https://github.com/JasonXZLiu))

## [pkg/lambdacomponents/v0.6.0](https://github.com/aws-observability/aws-otel-collector/tree/pkg/lambdacomponents/v0.6.0) (2020-12-15)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.6.0...pkg/lambdacomponents/v0.6.0)

**Closed issues:**

- ecs-demo.md references missing CloudFormation templates [\#218](https://github.com/aws-observability/aws-otel-collector/issues/218)
- Include Logz.io traces Exporter from upstream [\#196](https://github.com/aws-observability/aws-otel-collector/issues/196)

**Merged pull requests:**

- Refactor AWS environment variable setting code [\#272](https://github.com/aws-observability/aws-otel-collector/pull/272) ([mxiamxia](https://github.com/mxiamxia))
- add AWS profile customization support in extra config file [\#266](https://github.com/aws-observability/aws-otel-collector/pull/266) ([mxiamxia](https://github.com/mxiamxia))



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
