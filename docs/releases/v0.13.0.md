# Changelog

## [v0.13.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.13.0) (2021-09-21)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.12.0...v0.13.0)

**Upstream release notes:**
- [v0.34.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.34.0)
- [v0.35.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.35.0)
- [v0.36.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.36.0)

**Breaking changes:**
- The CLI flags controlling collector logs and metrics exposition have been removed.  Log settings can be controlled 
  through the `service` section of the configuration file or with the `--set` CLI flag.

**Known issues:**
- Collector fails to start when using the ecsobserver extension to auto-discover Prometheus targets to scrape.  [\#5373](https://github.com/open-telemetry/opentelemetry-collector-contrib/issues/5373)

**Closed issues:**

- Collector does not start in ECS when using an SSM parameter for the config [\#644](https://github.com/aws-observability/aws-otel-collector/issues/644)
- Updated traces performance test results for Datadog exporter against real backend [\#593](https://github.com/aws-observability/aws-otel-collector/issues/593)

**Merged pull requests:**

- Revert "Increase the number of CI tests that can be run in parallel. … [\#649](https://github.com/aws-observability/aws-otel-collector/pull/649) ([straussb](https://github.com/straussb))
- collector update and flag removal [\#648](https://github.com/aws-observability/aws-otel-collector/pull/648) ([Aneurysm9](https://github.com/Aneurysm9))
- Upgrade otel dependencies to v0.35.0. [\#643](https://github.com/aws-observability/aws-otel-collector/pull/643) ([jefchien](https://github.com/jefchien))
- Use the stable lint tool on code check [\#642](https://github.com/aws-observability/aws-otel-collector/pull/642) ([mxiamxia](https://github.com/mxiamxia))
- Increase the number of CI tests that can be run in parallel. [\#641](https://github.com/aws-observability/aws-otel-collector/pull/641) ([straussb](https://github.com/straussb))
- Upgrade otel dependencies to v0.34.0. [\#640](https://github.com/aws-observability/aws-otel-collector/pull/640) ([jefchien](https://github.com/jefchien))
- Update Performance Model [\#637](https://github.com/aws-observability/aws-otel-collector/pull/637) ([github-actions[bot]](https://github.com/apps/github-actions))
- Removed clean-old-ec2 job. [\#636](https://github.com/aws-observability/aws-otel-collector/pull/636) ([jefchien](https://github.com/jefchien))
- Fix ADOT Operator releasing tools [\#635](https://github.com/aws-observability/aws-otel-collector/pull/635) ([mxiamxia](https://github.com/mxiamxia))
- Temporarily comment out ADOT operator mirroring script in the C/D wor… [\#634](https://github.com/aws-observability/aws-otel-collector/pull/634) ([straussb](https://github.com/straussb))
- Add OLTP Metric ADOT Operator Test [\#630](https://github.com/aws-observability/aws-otel-collector/pull/630) ([sethAmazon](https://github.com/sethAmazon))
- Use New Sample App And Fix Exporter Endpoint [\#604](https://github.com/aws-observability/aws-otel-collector/pull/604) ([sethAmazon](https://github.com/sethAmazon))



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
