# Changelog

## [v0.12.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.12.0) (2021-08-25)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.11.0...v0.12.0)

**Fixed bugs:**

- AMP metrics ingest for running in EKS on Fargate fails [\#542](https://github.com/aws-observability/aws-otel-collector/issues/542)

**Closed issues:**

- --config=/etc/ecs/container-insights/otel-task-metrics-config.yaml" [\#603](https://github.com/aws-observability/aws-otel-collector/issues/603)
- How to configure statsd receiver for OTEL collector running as fargate sidecar? [\#601](https://github.com/aws-observability/aws-otel-collector/issues/601)
- -javaagent:/opt/aws-opentelemetry-agent.jar [\#594](https://github.com/aws-observability/aws-otel-collector/issues/594)

**Merged pull requests:**

- Add ecs ec2 instance level metrics to docker file [\#621](https://github.com/aws-observability/aws-otel-collector/pull/621) ([JohnWu20](https://github.com/JohnWu20))
- Upgrade otel dependencies to v0.33.0. [\#619](https://github.com/aws-observability/aws-otel-collector/pull/619) ([straussb](https://github.com/straussb))
- Add ECS EC2 instance level metrics logic to AOC [\#617](https://github.com/aws-observability/aws-otel-collector/pull/617) ([JohnWu20](https://github.com/JohnWu20))
- Bump otel version to v0.32.0 and add back logz.io test cases [\#614](https://github.com/aws-observability/aws-otel-collector/pull/614) ([mxiamxia](https://github.com/mxiamxia))
- Added resource detection trace test cases. [\#610](https://github.com/aws-observability/aws-otel-collector/pull/610) ([jefchien](https://github.com/jefchien))
- Add adot-operator to matrix output [\#608](https://github.com/aws-observability/aws-otel-collector/pull/608) ([Saber-W](https://github.com/Saber-W))
- Add a new CI workflow job to test the ADOT Operator [\#606](https://github.com/aws-observability/aws-otel-collector/pull/606) ([Saber-W](https://github.com/Saber-W))
- Add two test cases for ADOT Operator [\#602](https://github.com/aws-observability/aws-otel-collector/pull/602) ([Saber-W](https://github.com/Saber-W))
- Mirror Operator images from Quay and GCR to AWS Observability ECR public gallery [\#592](https://github.com/aws-observability/aws-otel-collector/pull/592) ([Saber-W](https://github.com/Saber-W))
- Bump go.opentelemetry.io/collector from 0.30.0 to 0.30.1 [\#591](https://github.com/aws-observability/aws-otel-collector/pull/591) ([dependabot[bot]](https://github.com/apps/dependabot))
- Documentation: cleanup docker collector example, fix passing credentials to the collector in docker [\#588](https://github.com/aws-observability/aws-otel-collector/pull/588) ([fishnix](https://github.com/fishnix))
- Fixed typo in ecs-demo.md [\#586](https://github.com/aws-observability/aws-otel-collector/pull/586) ([anasofia99](https://github.com/anasofia99))
- Add AWS PRW exporter to lambda components [\#584](https://github.com/aws-observability/aws-otel-collector/pull/584) ([KKelvinLo](https://github.com/KKelvinLo))
- Update OTel deps to v0.30.0 and update sample app [\#582](https://github.com/aws-observability/aws-otel-collector/pull/582) ([mxiamxia](https://github.com/mxiamxia))
- Bump github.com/opencontainers/runc from 1.0.0-rc95 to 1.0.1 [\#581](https://github.com/aws-observability/aws-otel-collector/pull/581) ([dependabot[bot]](https://github.com/apps/dependabot))
- ecs: Add internal doc and cfn template for ECS Prometheus [\#560](https://github.com/aws-observability/aws-otel-collector/pull/560) ([pingleig](https://github.com/pingleig))



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
