# Changelog

## [v0.23.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.23.0) (2022-11-01)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.22.0...v0.23.0)

**Upstream release notes:**

- [v0.62.1](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.62.1)
- [v0.62.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.62.0)

**Merged pull requests:**

- Remove build-time definitions of distribution name [\#1575](https://github.com/aws-observability/aws-otel-collector/pull/1575) ([Aneurysm9](https://github.com/Aneurysm9))
- Add arm64 manifest for debian [\#1574](https://github.com/aws-observability/aws-otel-collector/pull/1574) ([Kausik-A](https://github.com/Kausik-A))
- Update upstream components [\#1562](https://github.com/aws-observability/aws-otel-collector/pull/1562) ([Aneurysm9](https://github.com/Aneurysm9))
- Fix useragent used by CWLogs client [\#1531](https://github.com/aws-observability/aws-otel-collector/pull/1531) ([Aneurysm9](https://github.com/Aneurysm9))
- Add HealthCheck app for ECS/Docker [\#1285](https://github.com/aws-observability/aws-otel-collector/pull/1285) ([PaurushGarg](https://github.com/PaurushGarg))



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
