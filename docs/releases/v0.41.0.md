# Changelog

## [v0.41.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.41.0) (2024-10-03)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.40.2...v0.41.0)

* Updated OpenTelemetry Collector dependencies to `v1.15.0`/`v0.109.0`
* Updated OpenTelemetry Collector contrib dependencies to `v0.109.0`

_Note:_ The `memory_ballast` extension has been sunset upstream in favor of the `GOMEMLIMIT` environment variable.  This release includes the `memory_ballast` extension from `v0.108.1` of the OpenTelemetry Collector.  The extension will be removed entirely in a future release should it become incompatible with the OpenTelemetry Collector framework's current release. See the [Go documentation](https://pkg.go.dev/runtime#hdr-Environment_Variables) for more information about `GOMEMLIMIT`'s usage.

_Note:_ All listening receivers will now listen on `localhost` by default instead of `0.0.0.0`. This may break expectations in containerized environments like Kubernetes. If you depend on `0.0.0.0` disable the `component.UseLocalHostAsDefaultHost` feature gate or explicitly set the endpoint to `0.0.0.0`.

_Note:_ Expansion of BASH-style environment variables, such as `$FOO`, is no longer be supported. Use `${FOO}` or `${env:FOO}` instead.

_Note:_ The `logging` exporter has been deprecated upstream in favor of the `debug` exporter.  If you are using the recommended `verbosity` configuration field the `debug` exporter can be substituted without further configuration changes.