# Changelog

## [v0.31.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.31.0) (2023-07-11)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.30.0...v0.31.0)

**Merged pull requests:**

- Do not set prometheus normalize feature gate to alpha status at ADOT Collector startup [\#2178](https://github.com/aws-observability/aws-otel-collector/pull/2178) ([bryan-aguilar](https://github.com/bryan-aguilar))
- Chore: Update collector components to v0.80.0 [\#2169](https://github.com/aws-observability/aws-otel-collector/pull/2169) ([rapphil](https://github.com/rapphil))
- Update windows permissions in logs and configuration [\#2158](https://github.com/aws-observability/aws-otel-collector/pull/2158) ([rapphil](https://github.com/rapphil))
- Reduce permission in the folder that stores configuration and logs on Windows [\#2156](https://github.com/aws-observability/aws-otel-collector/pull/2156) ([rapphil](https://github.com/rapphil))
- Update go version to use 1.20.5 in workflows [\#2117](https://github.com/aws-observability/aws-otel-collector/pull/2117) ([vasireddy99](https://github.com/vasireddy99))
- Update aws-otel-fargate-sidecar-deployment-cfn.yaml [\#2054](https://github.com/aws-observability/aws-otel-collector/pull/2054) ([PaurushGarg](https://github.com/PaurushGarg))


\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
