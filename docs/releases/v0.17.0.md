# Changelog

## [v0.17.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.17.0) (2022-02-21)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.16.0...v0.17.0)

**Upstream release notes:**
- [v0.45.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.45.0)
- [v0.44.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.44.0)

**Implemented enhancements:**

- ARM build and dependencies [\#766](https://github.com/aws-observability/aws-otel-collector/issues/766)
- Docker Image support for ARM64 [\#536](https://github.com/aws-observability/aws-otel-collector/issues/536)
- Support k8sprocessor [\#415](https://github.com/aws-observability/aws-otel-collector/issues/415)
- Split EMF metrics in awsemfexporter if metrics size reaches 100s [\#413](https://github.com/aws-observability/aws-otel-collector/issues/413)
- Support HTTP or HTTPS proxies to contact AWS services [\#335](https://github.com/aws-observability/aws-otel-collector/issues/335)
- Provide arm64 docker images in dockerhub [\#274](https://github.com/aws-observability/aws-otel-collector/issues/274)
- Dynamic sampling in OpenTelemetry collector [\#111](https://github.com/aws-observability/aws-otel-collector/issues/111)

**Fixed bugs:**

- Collector Logging and XRay Trace Inconsistent  [\#497](https://github.com/aws-observability/aws-otel-collector/issues/497)
- Incorrect metric name in ECS section [\#448](https://github.com/aws-observability/aws-otel-collector/issues/448)
- Using aws credentials file which rotates access key and access secret [\#408](https://github.com/aws-observability/aws-otel-collector/issues/408)
- ServiceLen View Logs feature is disabled on X-Ray traces instrumented by ADOT [\#376](https://github.com/aws-observability/aws-otel-collector/issues/376)
- UNKNOWN: NoCredentialProviders if AWS_PROFILE is specified [\#156](https://github.com/aws-observability/aws-otel-collector/issues/156)

**Closed issues:**

- support random trace_id format in AWS X-Ray as traces from jaeger receiver are not exported to AWS xray [\#961](https://github.com/aws-observability/aws-otel-collector/issues/961)
- awsprometheusremotewriteexporter drop  all labels except instance [\#958](https://github.com/aws-observability/aws-otel-collector/issues/958)
- extracfg.txt not working as expected [\#957](https://github.com/aws-observability/aws-otel-collector/issues/957)
- flag.go out of sync with upstream [\#951](https://github.com/aws-observability/aws-otel-collector/issues/951)
- No traces/metrics generated in CloudWatch Console when AWSOTelCollector deployed on Amazon EKS as sidecar [\#933](https://github.com/aws-observability/aws-otel-collector/issues/933)
- Running layer inside container crashes on execution [\#932](https://github.com/aws-observability/aws-otel-collector/issues/932)
- EKS fargate OTel awsxray exporter fail  [\#928](https://github.com/aws-observability/aws-otel-collector/issues/928)
- Powershell script for creating windows MSI does not work - cannot deploy as service to windows server [\#893](https://github.com/aws-observability/aws-otel-collector/issues/893)
- Minimal dockerfile that requires no build tool chain [\#878](https://github.com/aws-observability/aws-otel-collector/issues/878)
- Upstream collector release v0.42.0 breaks config [\#867](https://github.com/aws-observability/aws-otel-collector/issues/867)
- Configure branch protection rules  [\#734](https://github.com/aws-observability/aws-otel-collector/issues/734)
- Debug logs in on OTel collector running inside eks [\#668](https://github.com/aws-observability/aws-otel-collector/issues/668)
- Waiting for an answer: OTel metrics with statsd giving configuration error when starting aws-otel-collector [\#580](https://github.com/aws-observability/aws-otel-collector/issues/580)
- opentelemetry.auto.trace : WARN io.opentelemetry.javaagent.instrumentation.tomcat.common.TomcatTracer - Malformed url? scheme: http, host: null, port: 8900, path: null, query: null [\#578](https://github.com/aws-observability/aws-otel-collector/issues/578)
- Exporter to AMP wont work [\#513](https://github.com/aws-observability/aws-otel-collector/issues/513)
- Export raw values for cumulative metrics in EMF Exporter [\#510](https://github.com/aws-observability/aws-otel-collector/issues/510)
- [feature][prometheus] ECS service discovery extension for prometheus [\#412](https://github.com/aws-observability/aws-otel-collector/issues/412)
- Move developer docs and examples to the ADOT developer portal [\#411](https://github.com/aws-observability/aws-otel-collector/issues/411)
- Prometheus histograms, summary and counters [\#382](https://github.com/aws-observability/aws-otel-collector/issues/382)
- Exporter for AWS Elastic Search  [\#343](https://github.com/aws-observability/aws-otel-collector/issues/343)

**Merged pull requests:**

- Upgrade OTel dependencies to v0.45.1 [\#983](https://github.com/aws-observability/aws-otel-collector/pull/983) ([bryan-aguilar](https://github.com/bryan-aguilar))
- Upgrade OTel dependencies to v0.44.0 [\#974](https://github.com/aws-observability/aws-otel-collector/pull/974) ([bryan-aguilar](https://github.com/bryan-aguilar))
- Change AWS OTel references to ADOT in docs [\#972](https://github.com/aws-observability/aws-otel-collector/pull/972) ([bryan-aguilar](https://github.com/bryan-aguilar))
- Update flags.go to be in sync with upstream [\#955](https://github.com/aws-observability/aws-otel-collector/pull/955) ([bryan-aguilar](https://github.com/bryan-aguilar))
- Add OTEL_METRICS_EXPORTER environment variable to example config. [\#935](https://github.com/aws-observability/aws-otel-collector/pull/935) ([jefchien](https://github.com/jefchien))
- Add EKS fargate container insights using ADOT configuration file [\#922](https://github.com/aws-observability/aws-otel-collector/pull/922) ([skyduo](https://github.com/skyduo))
- Update docs with instructions for windows build. [\#911](https://github.com/aws-observability/aws-otel-collector/pull/911) ([jefchien](https://github.com/jefchien))



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
