# Changelog

## [v0.20.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.20.0) (2022-07-27)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.19.0...v0.20.0)

**Upstream release notes:**

- [v0.56.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.56.0)
- [v0.55.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.55.0)

**Merged pull requests:**

- Updated upstream collector dependancies to v0.56.0 [\#1383](https://github.com/aws-observability/aws-otel-collector/pull/1383) ([Kausik-A](https://github.com/Kausik-A))
- Changed comment in canary workflow [\#1367](https://github.com/aws-observability/aws-otel-collector/pull/1367) ([erichsueh3](https://github.com/erichsueh3))
- Ensure operator tag list always sorted from latest to oldest in Operator CI [\#1366](https://github.com/aws-observability/aws-otel-collector/pull/1366) ([erichsueh3](https://github.com/erichsueh3))
- Updated upstream collector dependancies to v0.55.0 [\#1357](https://github.com/aws-observability/aws-otel-collector/pull/1357) ([Kausik-A](https://github.com/Kausik-A))
- Add aws\_region `us-east-1` in weekly resource cleaners [\#1356](https://github.com/aws-observability/aws-otel-collector/pull/1356) ([vasireddy99](https://github.com/vasireddy99))
- Fix typos in stale bot workflow [\#1355](https://github.com/aws-observability/aws-otel-collector/pull/1355) ([srprash](https://github.com/srprash))
- Set operator image from go.mod collector dependency [\#1344](https://github.com/aws-observability/aws-otel-collector/pull/1344) ([erichsueh3](https://github.com/erichsueh3))
- Allowlist for Operator CD [\#1342](https://github.com/aws-observability/aws-otel-collector/pull/1342) ([erichsueh3](https://github.com/erichsueh3))
- \[main\] Add terraform aws env creds to ci-operator workflow [\#1341](https://github.com/aws-observability/aws-otel-collector/pull/1341) ([bryan-aguilar](https://github.com/bryan-aguilar))
- Remove Operator release from Collector CD [\#1332](https://github.com/aws-observability/aws-otel-collector/pull/1332) ([bryan-aguilar](https://github.com/bryan-aguilar))
- Upgrade ADOT Collector version to v0.19.0 [\#1331](https://github.com/aws-observability/aws-otel-collector/pull/1331) ([bryan-aguilar](https://github.com/bryan-aguilar))
- Add operator rollback workflow [\#1330](https://github.com/aws-observability/aws-otel-collector/pull/1330) ([bryan-aguilar](https://github.com/bryan-aguilar))



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
