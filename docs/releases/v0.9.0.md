# Changelog

## [v0.9.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.9.0) (2021-04-28)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/pkg/lambdacomponents/v0.9.0...v0.9.0)

**Closed issues:**

- Latests releases not available on Dockerhub [\#481](https://github.com/aws-observability/aws-otel-collector/issues/481)
- Unable to export traces to XRay [\#469](https://github.com/aws-observability/aws-otel-collector/issues/469)
- ECS EC2 Prometheus pipeline example - service replicas? [\#454](https://github.com/aws-observability/aws-otel-collector/issues/454)
- aws-otel-collector when run in container gives a permission denied error to create directory [\#451](https://github.com/aws-observability/aws-otel-collector/issues/451)
- Upgrade dependencies in lambdacomponents module to the latest 0.24 [\#432](https://github.com/aws-observability/aws-otel-collector/issues/432)
- SQL Instrumentation Error - Invalid Subsegment [\#390](https://github.com/aws-observability/aws-otel-collector/issues/390)

**Merged pull requests:**

- Fix log init fail when running as nonroot [\#484](https://github.com/aws-observability/aws-otel-collector/pull/484) ([bjrara](https://github.com/bjrara))
- bump version to v0.9.0 [\#480](https://github.com/aws-observability/aws-otel-collector/pull/480) ([mxiamxia](https://github.com/mxiamxia))
- remove container insight components [\#479](https://github.com/aws-observability/aws-otel-collector/pull/479) ([pxaws](https://github.com/pxaws))
- build: Bump builder image go version to 1.16.3 [\#478](https://github.com/aws-observability/aws-otel-collector/pull/478) ([pingleig](https://github.com/pingleig))
- bump version to 0.9.0 [\#476](https://github.com/aws-observability/aws-otel-collector/pull/476) ([mxiamxia](https://github.com/mxiamxia))
- Fix SSM package version [\#475](https://github.com/aws-observability/aws-otel-collector/pull/475) ([vastin](https://github.com/vastin))
- Add re-run support and SSM package test in CD workflow [\#474](https://github.com/aws-observability/aws-otel-collector/pull/474) ([vastin](https://github.com/vastin))
- bump version to 0.9.0 [\#473](https://github.com/aws-observability/aws-otel-collector/pull/473) ([mxiamxia](https://github.com/mxiamxia))
- lock the tf version to fix aws resource destroy error [\#471](https://github.com/aws-observability/aws-otel-collector/pull/471) ([mxiamxia](https://github.com/mxiamxia))
- Fix CD workflow of CPT/MXP [\#470](https://github.com/aws-observability/aws-otel-collector/pull/470) ([vastin](https://github.com/vastin))
- Fix S3 download permission [\#468](https://github.com/aws-observability/aws-otel-collector/pull/468) ([vastin](https://github.com/vastin))
- e2e: Add var for aws region [\#467](https://github.com/aws-observability/aws-otel-collector/pull/467) ([pingleig](https://github.com/pingleig))
- e2e: Remove centos6 [\#466](https://github.com/aws-observability/aws-otel-collector/pull/466) ([pingleig](https://github.com/pingleig))
- Add AWS Region in Canary workflow to fix resource destroy error on invalid aws region [\#465](https://github.com/aws-observability/aws-otel-collector/pull/465) ([mxiamxia](https://github.com/mxiamxia))
- Support dry-run in CD workflow [\#463](https://github.com/aws-observability/aws-otel-collector/pull/463) ([vastin](https://github.com/vastin))
- fix lambda component factory build error [\#462](https://github.com/aws-observability/aws-otel-collector/pull/462) ([mxiamxia](https://github.com/mxiamxia))

## [pkg/lambdacomponents/v0.9.0](https://github.com/aws-observability/aws-otel-collector/tree/pkg/lambdacomponents/v0.9.0) (2021-04-22)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/pkg/lambdacomponents/v0.9.1...pkg/lambdacomponents/v0.9.0)

## [pkg/lambdacomponents/v0.9.1](https://github.com/aws-observability/aws-otel-collector/tree/pkg/lambdacomponents/v0.9.1) (2021-04-22)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/pkg/lambdacomponents/v0.8.2...pkg/lambdacomponents/v0.9.1)

**Fixed bugs:**

- failed to shutdown exporter [\#419](https://github.com/aws-observability/aws-otel-collector/issues/419)

**Closed issues:**

- \[bug\] Config error should return exit code 1 [\#340](https://github.com/aws-observability/aws-otel-collector/issues/340)

**Merged pull requests:**

- add the lastest deps from upstream and update lambda component [\#460](https://github.com/aws-observability/aws-otel-collector/pull/460) ([mxiamxia](https://github.com/mxiamxia))
- add logzio testcase [\#459](https://github.com/aws-observability/aws-otel-collector/pull/459) ([mxiamxia](https://github.com/mxiamxia))
- Update CI workflows and docs [\#458](https://github.com/aws-observability/aws-otel-collector/pull/458) ([pxaws](https://github.com/pxaws))
- remove unused IMDS v1 test func from packaging script [\#457](https://github.com/aws-observability/aws-otel-collector/pull/457) ([mxiamxia](https://github.com/mxiamxia))
- Config cleansing after job and instance label is added in prometheus receiver [\#456](https://github.com/aws-observability/aws-otel-collector/pull/456) ([bjrara](https://github.com/bjrara))
- add doc for container insight eks support [\#455](https://github.com/aws-observability/aws-otel-collector/pull/455) ([pxaws](https://github.com/pxaws))
- Update doc layout [\#453](https://github.com/aws-observability/aws-otel-collector/pull/453) ([bjrara](https://github.com/bjrara))
- allow adding arbitrary env vars in extraCfg  [\#452](https://github.com/aws-observability/aws-otel-collector/pull/452) ([mxiamxia](https://github.com/mxiamxia))
- fix the collector fail to start issue after updated the new config deps [\#450](https://github.com/aws-observability/aws-otel-collector/pull/450) ([mxiamxia](https://github.com/mxiamxia))
- Add SSM package release support in CD workflow [\#449](https://github.com/aws-observability/aws-otel-collector/pull/449) ([vastin](https://github.com/vastin))
- bug fix for container insight [\#447](https://github.com/aws-observability/aws-otel-collector/pull/447) ([pxaws](https://github.com/pxaws))
- dep: Update config parser interface [\#445](https://github.com/aws-observability/aws-otel-collector/pull/445) ([pingleig](https://github.com/pingleig))
- ci: Use larger VPC for ec2 tests [\#444](https://github.com/aws-observability/aws-otel-collector/pull/444) ([pingleig](https://github.com/pingleig))
- Fix incorrect variable setting in terraform destroy [\#443](https://github.com/aws-observability/aws-otel-collector/pull/443) ([bjrara](https://github.com/bjrara))
- Enhance CI workflow and Fix ECS test [\#442](https://github.com/aws-observability/aws-otel-collector/pull/442) ([vastin](https://github.com/vastin))
- Add eks container insight tests to CI workflow [\#441](https://github.com/aws-observability/aws-otel-collector/pull/441) ([pxaws](https://github.com/pxaws))
- Add missing skip-step conditions to fix CI workflow re-run [\#440](https://github.com/aws-observability/aws-otel-collector/pull/440) ([vastin](https://github.com/vastin))
- ci: Disable patch for ci [\#439](https://github.com/aws-observability/aws-otel-collector/pull/439) ([pingleig](https://github.com/pingleig))
- Add SSM build and speed up CI re-run [\#436](https://github.com/aws-observability/aws-otel-collector/pull/436) ([vastin](https://github.com/vastin))
- function name is in line with opentelemetry-lambda [\#434](https://github.com/aws-observability/aws-otel-collector/pull/434) ([wangzlei](https://github.com/wangzlei))
- Add an event type to trigger CI workflow [\#433](https://github.com/aws-observability/aws-otel-collector/pull/433) ([vastin](https://github.com/vastin))
- workflow: Enable patch for CI [\#431](https://github.com/aws-observability/aws-otel-collector/pull/431) ([pingleig](https://github.com/pingleig))
- Add more regex to match new haproxy backend format [\#430](https://github.com/aws-observability/aws-otel-collector/pull/430) ([bjrara](https://github.com/bjrara))
- Add documents for container insight sample workloads [\#427](https://github.com/aws-observability/aws-otel-collector/pull/427) ([bjrara](https://github.com/bjrara))
- Support container insights for AOC [\#416](https://github.com/aws-observability/aws-otel-collector/pull/416) ([pxaws](https://github.com/pxaws))

## [pkg/lambdacomponents/v0.8.2](https://github.com/aws-observability/aws-otel-collector/tree/pkg/lambdacomponents/v0.8.2) (2021-04-10)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/pkg/lambdacomponents/v0.8.0...pkg/lambdacomponents/v0.8.2)

## [pkg/lambdacomponents/v0.8.0](https://github.com/aws-observability/aws-otel-collector/tree/pkg/lambdacomponents/v0.8.0) (2021-04-09)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/pkg/lambdacomponents/v0.8.1...pkg/lambdacomponents/v0.8.0)

## [pkg/lambdacomponents/v0.8.1](https://github.com/aws-observability/aws-otel-collector/tree/pkg/lambdacomponents/v0.8.1) (2021-04-09)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.8.0...pkg/lambdacomponents/v0.8.1)

**Implemented enhancements:**

- Connection Refused running otel agent in EC2 [\#345](https://github.com/aws-observability/aws-otel-collector/issues/345)

**Fixed bugs:**

- Permanent error: InvalidParameterException: Log groups starting with AWS/ are reserved for AWS. [\#353](https://github.com/aws-observability/aws-otel-collector/issues/353)

**Closed issues:**

- There is a serious issue with the documentation [\#409](https://github.com/aws-observability/aws-otel-collector/issues/409)
- Not recognizing AWS credentials either from env or ~/.aws/credentials [\#407](https://github.com/aws-observability/aws-otel-collector/issues/407)
- Performance test results for Logz.io exporter against real backend [\#389](https://github.com/aws-observability/aws-otel-collector/issues/389)
- Fargate CFN Example Issues [\#159](https://github.com/aws-observability/aws-otel-collector/issues/159)

**Merged pull requests:**

- Remove duplicated test case [\#426](https://github.com/aws-observability/aws-otel-collector/pull/426) ([bjrara](https://github.com/bjrara))
- dep: Upgrade from 0.23 to 0.24 [\#425](https://github.com/aws-observability/aws-otel-collector/pull/425) ([pingleig](https://github.com/pingleig))
- Support the partner components from upstream [\#423](https://github.com/aws-observability/aws-otel-collector/pull/423) ([mxiamxia](https://github.com/mxiamxia))
- Add default configurations for Container Insights on EKS with Prometheus [\#422](https://github.com/aws-observability/aws-otel-collector/pull/422) ([bjrara](https://github.com/bjrara))
- Revert "Add default configurations for Container Insights on EKS with Prometheus" [\#420](https://github.com/aws-observability/aws-otel-collector/pull/420) ([mxiamxia](https://github.com/mxiamxia))
- update the deps to v0.23.0 from upstream [\#414](https://github.com/aws-observability/aws-otel-collector/pull/414) ([mxiamxia](https://github.com/mxiamxia))
- Update developer guides. [\#410](https://github.com/aws-observability/aws-otel-collector/pull/410) ([vastin](https://github.com/vastin))
- Add default configurations for Container Insights on EKS with Prometheus [\#394](https://github.com/aws-observability/aws-otel-collector/pull/394) ([bjrara](https://github.com/bjrara))
- Introduce resourcedetectionprocessor for cluster name injection [\#393](https://github.com/aws-observability/aws-otel-collector/pull/393) ([bjrara](https://github.com/bjrara))



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
