# Changelog

## [v0.11.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.11.0) (2021-07-02)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.10.0...v0.11.0)

**Implemented enhancements:**

- Collector config not resolving resource attributes when used in log\_stream\_name [\#482](https://github.com/aws-observability/aws-otel-collector/issues/482)

**Fixed bugs:**

- Non-string OpenTelemetry span attributes appearing as blank strings in X-Ray UI [\#518](https://github.com/aws-observability/aws-otel-collector/issues/518)

**Closed issues:**

- get the latest "aws-opentelemetry-agent.jar" in DockerFile [\#558](https://github.com/aws-observability/aws-otel-collector/issues/558)
- \[Pending discussion\] Fail translate if "System", "Memory", "TCPv4", "TCPv6" has resources defined. [\#545](https://github.com/aws-observability/aws-otel-collector/issues/545)
- Windows Event Batch Size Research [\#544](https://github.com/aws-observability/aws-otel-collector/issues/544)
- \[Feature Request\] New agent configurations to support multi region/credentials for different output plugins [\#543](https://github.com/aws-observability/aws-otel-collector/issues/543)
- opentelemetry-collector between aws-otel-collector  Permanent error: SerializationException [\#537](https://github.com/aws-observability/aws-otel-collector/issues/537)
- in a single View of AWS  X-RAY , i should see an end-to-end tracing path of Lambda functions , API - Gateway and Dynamodb [\#535](https://github.com/aws-observability/aws-otel-collector/issues/535)
- Alerting on data collected in otel traces [\#533](https://github.com/aws-observability/aws-otel-collector/issues/533)
- OTEL collector config when running as a sidecar in fargate [\#532](https://github.com/aws-observability/aws-otel-collector/issues/532)
- otel collector on EC2 config parameters [\#531](https://github.com/aws-observability/aws-otel-collector/issues/531)
- OTEL-Collector cloudwatch log file name [\#530](https://github.com/aws-observability/aws-otel-collector/issues/530)
- Traces sampling Rate when using aws otel collector [\#529](https://github.com/aws-observability/aws-otel-collector/issues/529)
- AWS Collector build process is inconsistent with OTel Collector build process [\#494](https://github.com/aws-observability/aws-otel-collector/issues/494)
- Collector not capturing Resource Attribute ServiceName [\#483](https://github.com/aws-observability/aws-otel-collector/issues/483)
- ECS resource labels are not exported to prometheus using awsprometheusremotewrite exporter [\#472](https://github.com/aws-observability/aws-otel-collector/issues/472)
- Create README for new components in container insight [\#438](https://github.com/aws-observability/aws-otel-collector/issues/438)
- \[meta\] Tracker for release 0.9.0 [\#418](https://github.com/aws-observability/aws-otel-collector/issues/418)
- \[sync\] Breaking change in upstream since v0.23 [\#417](https://github.com/aws-observability/aws-otel-collector/issues/417)
- Need the application binary for Arm CPUs \(Raspberry Pi\) [\#406](https://github.com/aws-observability/aws-otel-collector/issues/406)
- Build failing on Raspbian OS [\#402](https://github.com/aws-observability/aws-otel-collector/issues/402)
- Error "Permanent error: server returned HTTP status 400 Bad Request" [\#396](https://github.com/aws-observability/aws-otel-collector/issues/396)
- Populate stack trace for exceptions captured in OTel .Net span [\#383](https://github.com/aws-observability/aws-otel-collector/issues/383)
- AWS OTel Collector hardcodes log file paths [\#369](https://github.com/aws-observability/aws-otel-collector/issues/369)
- ASGI request errors not propagating to root trace [\#305](https://github.com/aws-observability/aws-otel-collector/issues/305)
- EKS Collector vs Auto Instrumentation Agent port mismatch \(4317 vs 55680\) [\#289](https://github.com/aws-observability/aws-otel-collector/issues/289)
- Track performance tuning for xrayexporter [\#205](https://github.com/aws-observability/aws-otel-collector/issues/205)
- Feature request: adds support for zipkin receiver [\#64](https://github.com/aws-observability/aws-otel-collector/issues/64)
- Include AWS Resource Detectors by Default [\#62](https://github.com/aws-observability/aws-otel-collector/issues/62)
- Create release-notes folder [\#34](https://github.com/aws-observability/aws-otel-collector/issues/34)

**Merged pull requests:**

- e2e: Enable Container Insight ECS Prometheus [\#570](https://github.com/aws-observability/aws-otel-collector/pull/570) ([pingleig](https://github.com/pingleig))
- \[dep\] Update to latest contrib repo to include ECS Prometheus [\#569](https://github.com/aws-observability/aws-otel-collector/pull/569) ([pingleig](https://github.com/pingleig))
- Enlarge VPC in CD workflow [\#567](https://github.com/aws-observability/aws-otel-collector/pull/567) ([mxiamxia](https://github.com/mxiamxia))
- fix logzio create permission issue. [\#566](https://github.com/aws-observability/aws-otel-collector/pull/566) ([mxiamxia](https://github.com/mxiamxia))
- Update to the deps which also fixed LogzIO exporter permission errors [\#564](https://github.com/aws-observability/aws-otel-collector/pull/564) ([mxiamxia](https://github.com/mxiamxia))
- Bump go.uber.org/zap from 1.17.0 to 1.18.1 [\#563](https://github.com/aws-observability/aws-otel-collector/pull/563) ([dependabot[bot]](https://github.com/apps/dependabot))
- Update otel version and enable container insights eks test cases [\#561](https://github.com/aws-observability/aws-otel-collector/pull/561) ([pxaws](https://github.com/pxaws))
- \[extension/ecsobsever\] Enable ecsobserver extension [\#559](https://github.com/aws-observability/aws-otel-collector/pull/559) ([pingleig](https://github.com/pingleig))
- Enable container insights infra metrics for EKS [\#550](https://github.com/aws-observability/aws-otel-collector/pull/550) ([pxaws](https://github.com/pxaws))
- Update Performance Model [\#549](https://github.com/aws-observability/aws-otel-collector/pull/549) ([github-actions[bot]](https://github.com/apps/github-actions))
- dep: Update core and contrib to v0.28.0 [\#541](https://github.com/aws-observability/aws-otel-collector/pull/541) ([pingleig](https://github.com/pingleig))
- Use examples/docker Dir In Make File For Docker Compose Up [\#540](https://github.com/aws-observability/aws-otel-collector/pull/540) ([sethAmazon](https://github.com/sethAmazon))
- Remove the unexpected import in go.mod [\#534](https://github.com/aws-observability/aws-otel-collector/pull/534) ([mxiamxia](https://github.com/mxiamxia))
- Fix typo in WINDOWS in release links template [\#528](https://github.com/aws-observability/aws-otel-collector/pull/528) ([cprice404-aws](https://github.com/cprice404-aws))
- Bump go.uber.org/zap from 1.16.0 to 1.17.0 [\#527](https://github.com/aws-observability/aws-otel-collector/pull/527) ([dependabot[bot]](https://github.com/apps/dependabot))
- Update Performance Model [\#526](https://github.com/aws-observability/aws-otel-collector/pull/526) ([github-actions[bot]](https://github.com/apps/github-actions))
- Bump github.com/opencontainers/runc from 1.0.0-rc93 to 1.0.0-rc95 [\#523](https://github.com/aws-observability/aws-otel-collector/pull/523) ([dependabot[bot]](https://github.com/apps/dependabot))
- Add CloudFormation, Task difination and config file for ECS  instance metrics [\#521](https://github.com/aws-observability/aws-otel-collector/pull/521) ([JohnWu20](https://github.com/JohnWu20))



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
