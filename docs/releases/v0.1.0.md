# Changelog

## [v0.1.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.1.0) (2020-10-20)

**Closed issues:**

- StatsD receiver and EMF Exporter Misses Metrics in Cloud Watch Console  and Has Empty Dimension [\#40](https://github.com/aws-observability/aws-otel-collector/issues/40)

**Merged pull requests:**

- add dependabot to pull the latest dependencies and update README to reflect the right docker image links [\#46](https://github.com/aws-observability/aws-otel-collector/pull/46) ([mxiamxia](https://github.com/mxiamxia))

**Closed issues:**

- Collector not forwarding arguments like setting logging variable [\#26](https://github.com/aws-observability/aws-otel-collector/issues/26)
- Update the collector service name to follow the AWS Distro format [\#25](https://github.com/aws-observability/aws-otel-collector/issues/25)

**Merged pull requests:**
- update main files with OpenTelemetry copyright [\#44](https://github.com/aws-observability/aws-otel-collector/pull/44) ([mxiamxia](https://github.com/mxiamxia))
- Create pull\_request\_template.md [\#42](https://github.com/aws-observability/aws-otel-collector/pull/42) ([wyTrivail](https://github.com/wyTrivail))
- Update the AWSEMFExporter to the latest from upstream [\#41](https://github.com/aws-observability/aws-otel-collector/pull/41) ([mxiamxia](https://github.com/mxiamxia))
- enable the essential ports for container [\#39](https://github.com/aws-observability/aws-otel-collector/pull/39) ([mxiamxia](https://github.com/mxiamxia))
- bump OTel Collector version and add HTTP endpoint for OTLP receiver [\#35](https://github.com/aws-observability/aws-otel-collector/pull/35) ([mxiamxia](https://github.com/mxiamxia))
- update EKS example deployment templates to the latest [\#29](https://github.com/aws-observability/aws-otel-collector/pull/29) ([mxiamxia](https://github.com/mxiamxia))

**Closed issues:**

- Rename deployment-template file names [\#17](https://github.com/aws-observability/aws-otel-collector/issues/17)

**Merged pull requests:**

- update the collector default config to match the existing behavior [\#38](https://github.com/aws-observability/aws-otel-collector/pull/38) ([mxiamxia](https://github.com/mxiamxia))
- Update CI.yml [\#27](https://github.com/aws-observability/aws-otel-collector/pull/27) ([wyTrivail](https://github.com/wyTrivail))
- replace collector name to "aws-otel-collector" all over the places [\#24](https://github.com/aws-observability/aws-otel-collector/pull/24) ([mxiamxia](https://github.com/mxiamxia))
- Update CI/CD scripts with the new branch name 'main' [\#21](https://github.com/aws-observability/aws-otel-collector/pull/21) ([mxiamxia](https://github.com/mxiamxia))
- rename all the collector name to aws-otel-collector in all the docs [\#20](https://github.com/aws-observability/aws-otel-collector/pull/20) ([mxiamxia](https://github.com/mxiamxia))
- update awsemfexporter version [\#18](https://github.com/aws-observability/aws-otel-collector/pull/18) ([shaochengwang](https://github.com/shaochengwang))
- update x-ray exporter and otel collector [\#16](https://github.com/aws-observability/aws-otel-collector/pull/16) ([mxiamxia](https://github.com/mxiamxia))
- Upgrade OTel collector to the latest version in AOC [\#15](https://github.com/aws-observability/aws-otel-collector/pull/15) ([mxiamxia](https://github.com/mxiamxia))
- Update the default config  [\#12](https://github.com/aws-observability/aws-otel-collector/pull/12) ([mxiamxia](https://github.com/mxiamxia))

**Merged pull requests:**

- Fixworkflow [\#14](https://github.com/aws-observability/aws-otel-collector/pull/14) ([wyTrivail](https://github.com/wyTrivail))
- replace ghcr with ecr to unblock ci [\#13](https://github.com/aws-observability/aws-otel-collector/pull/13) ([wyTrivail](https://github.com/wyTrivail))
- update docker compose example with new private test docker image [\#11](https://github.com/aws-observability/aws-otel-collector/pull/11) ([mxiamxia](https://github.com/mxiamxia))
- Docker Compose example update [\#10](https://github.com/aws-observability/aws-otel-collector/pull/10) ([mxiamxia](https://github.com/mxiamxia))
- \[in progress\]update collector 0.10.0 [\#9](https://github.com/aws-observability/aws-otel-collector/pull/9) ([wyTrivail](https://github.com/wyTrivail))
- revert the changes for private repo github action [\#8](https://github.com/aws-observability/aws-otel-collector/pull/8) ([wyTrivail](https://github.com/wyTrivail))
- drop the local action as it can't support 'uses' [\#7](https://github.com/aws-observability/aws-otel-collector/pull/7) ([wyTrivail](https://github.com/wyTrivail))
-  add checkout into every integ-test in order to run local action [\#6](https://github.com/aws-observability/aws-otel-collector/pull/6) ([wyTrivail](https://github.com/wyTrivail))
- use private repo as github action [\#5](https://github.com/aws-observability/aws-otel-collector/pull/5) ([wyTrivail](https://github.com/wyTrivail))
- bump version to 0.1.11 [\#4](https://github.com/aws-observability/aws-otel-collector/pull/4) ([wyTrivail](https://github.com/wyTrivail))
- fix workflow [\#3](https://github.com/aws-observability/aws-otel-collector/pull/3) ([wyTrivail](https://github.com/wyTrivail))
- add workflow [\#2](https://github.com/aws-observability/aws-otel-collector/pull/2) ([wyTrivail](https://github.com/wyTrivail))
- Initial commit for AOC:beta [\#1](https://github.com/aws-observability/aws-otel-collector/pull/1) ([mxiamxia](https://github.com/mxiamxia))


\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
