# Changelog

## [v0.32.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.32.0) (2023-08-11)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.31.0...v0.32.0)

**Merged pull requests:**

- dependabot updates Fri Aug 11 04:54:36 UTC 2023 [\#2264](https://github.com/aws-observability/aws-otel-collector/pull/2264)
- Add the load balancing exporter and k8s attributes processor [\#2262](https://github.com/aws-observability/aws-otel-collector/pull/2262) ([humivo](https://github.com/humivo))
- Add Log statement about statsd receiver [\#2250](https://github.com/aws-observability/aws-otel-collector/pull/2250) ([vasireddy99](https://github.com/vasireddy99))
- Dependabot updates [\#2245](https://github.com/aws-observability/aws-otel-collector/pull/2245) ([vasireddy99](https://github.com/vasireddy99))
- \[chore\] Add Patch file for kafka exporter [\#2244](https://github.com/aws-observability/aws-otel-collector/pull/2244) ([vasireddy99](https://github.com/vasireddy99))
- \[main\]  Vendor modules and add patch file for StatsD Receiver [\#2241](https://github.com/aws-observability/aws-otel-collector/pull/2241) ([vasireddy99](https://github.com/vasireddy99))
- Dependabot prs/2023 08 01 t120653 and OTel Collector v0.82.0  [\#2229](https://github.com/aws-observability/aws-otel-collector/pull/2229) ([vasireddy99](https://github.com/vasireddy99))
- Adding ECS\_Observer\_Test\_Case [\#2227](https://github.com/aws-observability/aws-otel-collector/pull/2227) ([PaurushGarg](https://github.com/PaurushGarg))
- \[chore\] update main branch with release/v0.31.x [\#2213](https://github.com/aws-observability/aws-otel-collector/pull/2213) ([bryan-aguilar](https://github.com/bryan-aguilar))
- \[chore\] Add missing cluster targets [\#2212](https://github.com/aws-observability/aws-otel-collector/pull/2212) ([bryan-aguilar](https://github.com/bryan-aguilar))
- \[chore\] Store and use testcases file from collector repo [\#2205](https://github.com/aws-observability/aws-otel-collector/pull/2205) ([bryan-aguilar](https://github.com/bryan-aguilar))
- \[fix\] Don't overwrite systemd environment file [\#2261](https://github.com/aws-observability/aws-otel-collector/pull/2261) [\#2267](https://github.com/aws-observability/aws-otel-collector/pull/2267) ([rapphil](https://github.com/rapphil))


\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
