# Changelog

## [v0.29.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.29.0) (2023-05-12)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.28.0...v0.29.0)

**Merged pull requests:**

- Update notices for prometheus receiver [\#2053](https://github.com/aws-observability/aws-otel-collector/pull/2053) ([bryan-aguilar](https://github.com/bryan-aguilar))
- Add new processors [\#2052](https://github.com/aws-observability/aws-otel-collector/pull/2052) ([rapphil](https://github.com/rapphil))
- Update to use public ecr rather than dockerhub in vended templates. [\#2045](https://github.com/aws-observability/aws-otel-collector/pull/2045) ([bryan-aguilar](https://github.com/bryan-aguilar))
- Disable pkg.translator.prometheus.NormalizeName feature gate by default [\#2044](https://github.com/aws-observability/aws-otel-collector/pull/2044) ([bryan-aguilar](https://github.com/bryan-aguilar))
- Deprecate lambdacomponents module [\#1981](https://github.com/aws-observability/aws-otel-collector/pull/1981) ([bryan-aguilar](https://github.com/bryan-aguilar))



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
