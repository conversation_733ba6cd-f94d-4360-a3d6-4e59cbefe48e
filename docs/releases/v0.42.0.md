# Changelog

## [v0.42.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.42.0) (2024-12-17)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.41.1...v0.42.0)

**Merged pull requests:**

- \[chore\] Bump OTel Collector and collector contrib v0.115.0, Removed logging exporter and ballast extension  [\#2915](https://github.com/aws-observability/aws-otel-collector/pull/2915) ([vasireddy99](https://github.com/vasireddy99))

**Breaking Changes:**

- The `memory_ballast` extension was removed upstream and ADOT Collector in favor of the `GOMEMLIMIT` environment variable, See the [Go documentation](https://pkg.go.dev/runtime#hdr-Environment_Variables) for more information about `GOMEMLIMIT`'s usage.

- The `logging` exporter has been removed upstream in favor of the `debug` exporter. This release removes the logging exporter. See [#11337](https://github.com/open-telemetry/opentelemetry-collector/issues/11337) to migrate to the debug exporter.

\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
