# Changelog

## [v0.28.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.28.0) (2023-04-06)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.27.1...v0.28.0)

**Merged pull requests:**

New features:

- Add kafka receiver/exporter and confmap providers (s3/http/https) [\#1945](https://github.com/aws-observability/aws-otel-collector/pull/1945) ([rapphil](https://github.com/rapphil))
- Update upstream OTel dependencies to v0.74.0 [\#1920](https://github.com/aws-observability/aws-otel-collector/pull/1920) ([bryan-aguilar](https://github.com/bryan-aguilar))

Fixes:

- Parse flags before creating confmap provider [\#1924](https://github.com/aws-observability/aws-otel-collector/pull/1924) ([bryan-aguilar](https://github.com/bryan-aguilar))
- Create flagset and config provider for windows svc [\#1921](https://github.com/aws-observability/aws-otel-collector/pull/1921) ([bryan-aguilar](https://github.com/bryan-aguilar))

\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
