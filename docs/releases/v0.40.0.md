# Changelog

## [v0.40.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.40.0) (2024-06-25)

* Updated OpenTelemetry Collector dependencies to `v1.9.0`/`v0.102.1`
* Updated OpenTelemetry Collector contrib dependencies to `v0.102.0`

_Note:_ The `envprovider` configuration source has been modified upstream to require that environment
variable names must be ASCII only, start with either a letter or an underscore, and contain only
underscores, letters, or numbers.  See [here](https://github.com/open-telemetry/opentelemetry-collector/blob/main/CHANGELOG.md#-breaking-changes--1)
for more details.

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.39.1...v0.40.0)

**Merged pull requests:**

- fix missing escape of `$` on replacement placeholders [#2751](https://github.com/aws-observability/aws-otel-collector/pull/2751)
