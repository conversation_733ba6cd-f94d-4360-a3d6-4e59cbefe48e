# Changelog

## [v0.10.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.10.0) (2021-05-22)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.9.0...v0.10.0)

**Closed issues:**

- Include Batch processor [\#502](https://github.com/aws-observability/aws-otel-collector/issues/502)
- Statsd Receiver [\#110](https://github.com/aws-observability/aws-otel-collector/issues/110)

**Merged pull requests:**

- chore: use os.Chown instead of calling chown command [\#524](https://github.com/aws-observability/aws-otel-collector/pull/524) ([haojhcwa](https://github.com/haojhcwa))
- update OTel collector deps to v0.27.0 [\#522](https://github.com/aws-observability/aws-otel-collector/pull/522) ([mxiamxia](https://github.com/mxiamxia))
- Fix Docker command [\#515](https://github.com/aws-observability/aws-otel-collector/pull/515) ([willarmiros](https://github.com/willarmiros))
- Update versions of Collector and components to v0.26.0, update usages for new version  [\#514](https://github.com/aws-observability/aws-otel-collector/pull/514) ([dhruv-vora](https://github.com/dhruv-vora))
- Update Performance Model [\#509](https://github.com/aws-observability/aws-otel-collector/pull/509) ([github-actions[bot]](https://github.com/apps/github-actions))
- Update CI job after terraform test case update [\#501](https://github.com/aws-observability/aws-otel-collector/pull/501) ([bjrara](https://github.com/bjrara))
- Update Performance Model [\#499](https://github.com/aws-observability/aws-otel-collector/pull/499) ([github-actions[bot]](https://github.com/apps/github-actions))
- Enable SSM package testcase in canary test [\#498](https://github.com/aws-observability/aws-otel-collector/pull/498) ([vastin](https://github.com/vastin))
- Add support of default config in SSM package [\#495](https://github.com/aws-observability/aws-otel-collector/pull/495) ([vastin](https://github.com/vastin))
- Don't set default version of SSM in CI workflow [\#493](https://github.com/aws-observability/aws-otel-collector/pull/493) ([vastin](https://github.com/vastin))
- Fix SSM version name/quota and speed up CI [\#491](https://github.com/aws-observability/aws-otel-collector/pull/491) ([vastin](https://github.com/vastin))
- Disable fail-fast in CD workflow [\#490](https://github.com/aws-observability/aws-otel-collector/pull/490) ([vastin](https://github.com/vastin))
- Fix cache key in CD workflow [\#488](https://github.com/aws-observability/aws-otel-collector/pull/488) ([vastin](https://github.com/vastin))
- terraform\_version: 0.14.10 [\#487](https://github.com/aws-observability/aws-otel-collector/pull/487) ([mxiamxia](https://github.com/mxiamxia))
- fix DockerHub release account username in CD WF [\#486](https://github.com/aws-observability/aws-otel-collector/pull/486) ([mxiamxia](https://github.com/mxiamxia))



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
