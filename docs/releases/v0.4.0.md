# Changelog

## [v0.4.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.4.0) (2020-11-18)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.3.0...v0.4.0)

**Fixed bugs:**

- awsxrayreceiver throws fatal errors when the input data load is intensiv [\#92](https://github.com/aws-observability/aws-otel-collector/issues/92)

**Closed issues:**

- Include OTLPHTTP Exporter from upstream [\#104](https://github.com/aws-observability/aws-otel-collector/issues/104)
- EC2 instance type [\#69](https://github.com/aws-observability/aws-otel-collector/issues/69)
- ECS Container Insights Metrics miss the expected dimensions [\#36](https://github.com/aws-observability/aws-otel-collector/issues/36)
- EMEExporter - added new Summary Metric type and fix the wrong Min/Max value on Histogram metrics
- Enhancement - allow setting log levels by using environment variable

**Merged pull requests:**

- fargate-fix: update ecs metrics receiver to the latest commit of Nov 18th. [\#137](https://github.com/aws-observability/aws-otel-collector/pull/137) ([hossain-rayhan](https://github.com/hossain-rayhan))
- Enhance readme and added debug feature [\#136](https://github.com/aws-observability/aws-otel-collector/pull/136) ([mxiamxia](https://github.com/mxiamxia))
- fix the new breaking zap logger hook [\#135](https://github.com/aws-observability/aws-otel-collector/pull/135) ([mxiamxia](https://github.com/mxiamxia))
- remove resource destroy from soaking [\#134](https://github.com/aws-observability/aws-otel-collector/pull/134) ([wyTrivail](https://github.com/wyTrivail))
- Update CI.yml [\#132](https://github.com/aws-observability/aws-otel-collector/pull/132) ([wyTrivail](https://github.com/wyTrivail))
- Add xray receiver to the default config file [\#131](https://github.com/aws-observability/aws-otel-collector/pull/131) ([JohnWu20](https://github.com/JohnWu20))
- remove installation of aws-cli in soaking.yml [\#128](https://github.com/aws-observability/aws-otel-collector/pull/128) ([wyTrivail](https://github.com/wyTrivail))
- disable terraform lock [\#126](https://github.com/aws-observability/aws-otel-collector/pull/126) ([wyTrivail](https://github.com/wyTrivail))
- Update README for otlphttpexporter [\#125](https://github.com/aws-observability/aws-otel-collector/pull/125) ([mxiamxia](https://github.com/mxiamxia))
- enable otlp http exporter integration test cases for all the testing … [\#124](https://github.com/aws-observability/aws-otel-collector/pull/124) ([mxiamxia](https://github.com/mxiamxia))
- Setup windows soaking [\#117](https://github.com/aws-observability/aws-otel-collector/pull/117) ([wyTrivail](https://github.com/wyTrivail))
- Fix misspelling at "ndoes" [\#116](https://github.com/aws-observability/aws-otel-collector/pull/116) ([cicerosilvajunior](https://github.com/cicerosilvajunior))
- update e2etest testcases to fix ci [\#113](https://github.com/aws-observability/aws-otel-collector/pull/113) ([wyTrivail](https://github.com/wyTrivail))
-  create soaking test [\#112](https://github.com/aws-observability/aws-otel-collector/pull/112) ([wyTrivail](https://github.com/wyTrivail))
- Update testcases.json [\#108](https://github.com/aws-observability/aws-otel-collector/pull/108) ([wyTrivail](https://github.com/wyTrivail))
- update to the latest versions of the collector and components [\#107](https://github.com/aws-observability/aws-otel-collector/pull/107) ([hossain-rayhan](https://github.com/hossain-rayhan))
- Update issue templates [\#103](https://github.com/aws-observability/aws-otel-collector/pull/103) ([mxiamxia](https://github.com/mxiamxia))
- Enhance the example sample apps to prevent unnecessary errors [\#101](https://github.com/aws-observability/aws-otel-collector/pull/101) ([mxiamxia](https://github.com/mxiamxia))
- Fix docker shared creds [\#100](https://github.com/aws-observability/aws-otel-collector/pull/100) ([dradetsky](https://github.com/dradetsky))
- speed up the docker build [\#93](https://github.com/aws-observability/aws-otel-collector/pull/93) ([wyTrivail](https://github.com/wyTrivail))
- enable people to build image with dockerfile directly [\#89](https://github.com/aws-observability/aws-otel-collector/pull/89) ([wyTrivail](https://github.com/wyTrivail))
- add pr build workflow [\#86](https://github.com/aws-observability/aws-otel-collector/pull/86) ([wyTrivail](https://github.com/wyTrivail))


\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
