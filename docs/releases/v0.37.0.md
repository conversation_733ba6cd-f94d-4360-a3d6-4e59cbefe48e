# Changelog

## [v0.37.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.37.0) (2024-01-19)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.36.0...v0.37.0)

**Changelog**

- **Patch** [opentelemetry-collector/exporterhelper] Cleanup logging for export failures [#9282](https://github.com/open-telemetry/opentelemetry-collector/pull/9282)
- **Patch** [opentelemetry-collector-contrib/exporter/prometheusremotewrite] Fix: Validate context is canceled during retries [#30308]
- Bump `go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc` dependency to v0.47.0

**Merged pull requests:**

- Remove Deprecated dynatrace exporter [\#2568](https://github.com/aws-observability/aws-otel-collector/pull/2568) ([vasireddy99](https://github.com/vasireddy99))


\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
