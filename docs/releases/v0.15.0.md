# Changelog

## [v0.15.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.15.0) (2021-11-22)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.14.1...v0.15.0)

**Upstream release notes:**
- [v0.39.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.39.0)

**Closed issues:**

- Add Codeowners file [\#733](https://github.com/aws-observability/aws-otel-collector/issues/733)
- Cancel PR-build workflow on new push to PR [\#717](https://github.com/aws-observability/aws-otel-collector/issues/717)
- Takes too long to build OTel container in test cases [\#716](https://github.com/aws-observability/aws-otel-collector/issues/716)
- Close issues and PR for inactivity after a certain amount of time. [\#713](https://github.com/aws-observability/aws-otel-collector/issues/713)
- Add EC2 instance cleaner  [\#711](https://github.com/aws-observability/aws-otel-collector/issues/711)
- AWS OTEL Java Agent 1.7 giving "java.lang.IndexOutOfBoundsException: Index 0 out of bounds for length 0" [\#704](https://github.com/aws-observability/aws-otel-collector/issues/704)
- See if soaking tests will work on Ubuntu latest [\#701](https://github.com/aws-observability/aws-otel-collector/issues/701)
- E2E tests have failed for distribution packages having too many versions.  [\#699](https://github.com/aws-observability/aws-otel-collector/issues/699)
- Nightly Builds [\#679](https://github.com/aws-observability/aws-otel-collector/issues/679)
- Use multimod for versioning [\#677](https://github.com/aws-observability/aws-otel-collector/issues/677)
- Don't trigger workflow for README changes [\#56](https://github.com/aws-observability/aws-otel-collector/issues/56)

**Merged pull requests:**

- Add processors to default components. [\#760](https://github.com/aws-observability/aws-otel-collector/pull/760) ([jefchien](https://github.com/jefchien))
- Change CI to always run create SSM package if it doesn't exist. [\#758](https://github.com/aws-observability/aws-otel-collector/pull/758) ([jefchien](https://github.com/jefchien))
- Change release note header wording. [\#754](https://github.com/aws-observability/aws-otel-collector/pull/754) ([jefchien](https://github.com/jefchien))
- Fix multimod sync-core optional path and core-path. [\#751](https://github.com/aws-observability/aws-otel-collector/pull/751) ([khanhntd](https://github.com/khanhntd))
- Upgrade OTel dependencies to v0.39.0. [\#750](https://github.com/aws-observability/aws-otel-collector/pull/750) ([jefchien](https://github.com/jefchien))
- Add Codeowners [\#742](https://github.com/aws-observability/aws-otel-collector/pull/742) ([aateeqi](https://github.com/aateeqi))
- Cancel CI workflow on new push. [\#728](https://github.com/aws-observability/aws-otel-collector/pull/728) ([jefchien](https://github.com/jefchien))
- Add multimod [\#726](https://github.com/aws-observability/aws-otel-collector/pull/726) ([khanhntd](https://github.com/khanhntd))
- Cancel PR-build workflow on new push. [\#724](https://github.com/aws-observability/aws-otel-collector/pull/724) ([jefchien](https://github.com/jefchien))
- Move document version clean out of the release-candidate job. [\#722](https://github.com/aws-observability/aws-otel-collector/pull/722) ([jefchien](https://github.com/jefchien))
- Move out binary build from Dockerfile [\#721](https://github.com/aws-observability/aws-otel-collector/pull/721) ([sethAmazon](https://github.com/sethAmazon))
- Update performance model [\#718](https://github.com/aws-observability/aws-otel-collector/pull/718) ([github-actions[bot]](https://github.com/apps/github-actions))
- Add max stats to performance tests [\#715](https://github.com/aws-observability/aws-otel-collector/pull/715) ([sethAmazon](https://github.com/sethAmazon))
- Close issues and PR for inactivity after a certain amount of time. [\#714](https://github.com/aws-observability/aws-otel-collector/pull/714) ([khanhntd](https://github.com/khanhntd))
- Delete EC2 Instances that are running over 30 days [\#712](https://github.com/aws-observability/aws-otel-collector/pull/712) ([sethAmazon](https://github.com/sethAmazon))
- Update Performance Model [\#710](https://github.com/aws-observability/aws-otel-collector/pull/710) ([github-actions[bot]](https://github.com/apps/github-actions))
- Add path-ignore to PR-build and CI workflows. [\#709](https://github.com/aws-observability/aws-otel-collector/pull/709) ([jefchien](https://github.com/jefchien))
- Add workflow dispatch to performance test workflow. [\#708](https://github.com/aws-observability/aws-otel-collector/pull/708) ([jefchien](https://github.com/jefchien))
- Add workflow dispatch to resource clean action [\#705](https://github.com/aws-observability/aws-otel-collector/pull/705) ([sethAmazon](https://github.com/sethAmazon))
- Add header template and fixed formatting for release note generator. [\#703](https://github.com/aws-observability/aws-otel-collector/pull/703) ([jefchien](https://github.com/jefchien))
- Run soaking tests on Ubuntu latest \(\#701\) [\#702](https://github.com/aws-observability/aws-otel-collector/pull/702) ([sethAmazon](https://github.com/sethAmazon))
- Clean Distributor Document versions \(\#699\) [\#700](https://github.com/aws-observability/aws-otel-collector/pull/700) ([sethAmazon](https://github.com/sethAmazon))
- Add workflow dispatch to soaking tests [\#694](https://github.com/aws-observability/aws-otel-collector/pull/694) ([sethAmazon](https://github.com/sethAmazon))




\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
