# Changelog

## [v0.6.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.6.0) (2020-12-14)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.5.0...v0.6.0)

**Closed issues:**

- Local dev environment support [\#249](https://github.com/aws-observability/aws-otel-collector/issues/249)
- Performance model should include throughput [\#209](https://github.com/aws-observability/aws-otel-collector/issues/209)

**Merged pull requests:**

- chore: add doc for signed linux package verification [\#254](https://github.com/aws-observability/aws-otel-collector/pull/254) ([haojhcwa](https://github.com/haojhcwa))
- disable cronjob soaking [\#247](https://github.com/aws-observability/aws-otel-collector/pull/247) ([wyTrivail](https://github.com/wyTrivail))
- chore: Add gpg linux package signing and RPM FIPS support [\#246](https://github.com/aws-observability/aws-otel-collector/pull/246) ([haojhcwa](https://github.com/haojhcwa))
- Add Getting Started Templates for the Prometheus Pipeline [\#244](https://github.com/aws-observability/aws-otel-collector/pull/244) ([JasonXZLiu](https://github.com/JasonXZLiu))
- fix: use official aws-otel-collector image [\#243](https://github.com/aws-observability/aws-otel-collector/pull/243) ([hossain-rayhan](https://github.com/hossain-rayhan))
- Add build and push workflow for aws-observability public ecr images [\#242](https://github.com/aws-observability/aws-otel-collector/pull/242) ([amanbrar1999](https://github.com/amanbrar1999))
- fix the ecr public pushing [\#239](https://github.com/aws-observability/aws-otel-collector/pull/239) ([wyTrivail](https://github.com/wyTrivail))
- use ecr alias [\#238](https://github.com/aws-observability/aws-otel-collector/pull/238) ([wyTrivail](https://github.com/wyTrivail))
- Revert "bump version to v0.5.0" [\#236](https://github.com/aws-observability/aws-otel-collector/pull/236) ([mxiamxia](https://github.com/mxiamxia))
- bump version to v0.5.0 [\#235](https://github.com/aws-observability/aws-otel-collector/pull/235) ([mxiamxia](https://github.com/mxiamxia))
- limit the concurrent job in cd workflow to 5 [\#234](https://github.com/aws-observability/aws-otel-collector/pull/234) ([wyTrivail](https://github.com/wyTrivail))
- Update CI.yml to only upload candidate when all the tests are passed [\#233](https://github.com/aws-observability/aws-otel-collector/pull/233) ([wyTrivail](https://github.com/wyTrivail))
- Update soaking.yml to use github sha [\#232](https://github.com/aws-observability/aws-otel-collector/pull/232) ([wyTrivail](https://github.com/wyTrivail))
- fix release note generation in cd workflow [\#230](https://github.com/aws-observability/aws-otel-collector/pull/230) ([wyTrivail](https://github.com/wyTrivail))
- Update the ecs docs [\#220](https://github.com/aws-observability/aws-otel-collector/pull/220) ([JohnWu20](https://github.com/JohnWu20))
- Add test case for gRPC exporter [\#213](https://github.com/aws-observability/aws-otel-collector/pull/213) ([shaochengwang](https://github.com/shaochengwang))



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
