# Changelog

## [v0.16.0](https://github.com/aws-observability/aws-otel-collector/tree/v0.16.0) (2022-01-27)

[Full Changelog](https://github.com/aws-observability/aws-otel-collector/compare/v0.15.1...v0.16.0)

**Upstream release notes:**
- [v0.43.1](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.43.1)
- [v0.43.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.43.0)
- [v0.42.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.42.0)
- [v0.41.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.41.0)
- [v0.40.0](https://github.com/open-telemetry/opentelemetry-collector/releases/tag/v0.40.0)

**Implemented enhancements:**

- Create github page for benchmarking [\#811](https://github.com/aws-observability/aws-otel-collector/issues/811)
- Publish nightly releases to docker hub [\#516](https://github.com/aws-observability/aws-otel-collector/issues/516)

**Fixed bugs:**

- Docker image build failing [\#691](https://github.com/aws-observability/aws-otel-collector/issues/691)
- The latest version of Logzio exporter failed to be started on EC2 and OnPremise hosts [\#574](https://github.com/aws-observability/aws-otel-collector/issues/574)

**Closed issues:**

- Histograms in CloudWatch [\#918](https://github.com/aws-observability/aws-otel-collector/issues/918)
- Agent cpu/memory when idle  [\#914](https://github.com/aws-observability/aws-otel-collector/issues/914)
- Sidecar deployment definition aws-otel-fargate-sidecar-deployment-cfn.yaml errors [\#906](https://github.com/aws-observability/aws-otel-collector/issues/906)
- It's possible that InstanceId label can be collected in task/container level metrics? [\#895](https://github.com/aws-observability/aws-otel-collector/issues/895)
- Naming convention for binary [\#880](https://github.com/aws-observability/aws-otel-collector/issues/880)
- Dockerfile mass copy config [\#879](https://github.com/aws-observability/aws-otel-collector/issues/879)
- Fix broken benchmarking links in README [\#871](https://github.com/aws-observability/aws-otel-collector/issues/871)
- Refactor AWS emf cloudwatch pusher logic into new cloudwatch package [\#869](https://github.com/aws-observability/aws-otel-collector/issues/869)
- Unspecified pod metrics landing in CloudWatch [\#847](https://github.com/aws-observability/aws-otel-collector/issues/847)
- Remove usages of docker load from workflows [\#846](https://github.com/aws-observability/aws-otel-collector/issues/846)
- Replace soaking tests with performance tests. [\#833](https://github.com/aws-observability/aws-otel-collector/issues/833)
- Many examples still using old aws-otel-emitter image [\#831](https://github.com/aws-observability/aws-otel-collector/issues/831)
- SSL not supported on OTLP/gRPC port 4317 [\#817](https://github.com/aws-observability/aws-otel-collector/issues/817)
- 403 UnrecognizedClientException Python Tracing [\#814](https://github.com/aws-observability/aws-otel-collector/issues/814)
- Environment variables are not getting resolved when passing config through SSM  [\#808](https://github.com/aws-observability/aws-otel-collector/issues/808)
- Merging from PR is needed when testing with integration test [\#793](https://github.com/aws-observability/aws-otel-collector/issues/793)
- Some of the integration tests in CD are failing [\#785](https://github.com/aws-observability/aws-otel-collector/issues/785)
- Possible to collect the metrics from the cluster where the task is located [\#776](https://github.com/aws-observability/aws-otel-collector/issues/776)
- Dockerfile should build from alpine instead of scratch [\#770](https://github.com/aws-observability/aws-otel-collector/issues/770)
- Datadog Exporter Key Validation Fails in latest version [\#759](https://github.com/aws-observability/aws-otel-collector/issues/759)
- Is AWS-OTEL-Collector generally available and link to download docker tar ball [\#737](https://github.com/aws-observability/aws-otel-collector/issues/737)
- Use CWCI as the abbreviation for CloudWatch Container Insights [\#736](https://github.com/aws-observability/aws-otel-collector/issues/736)
- Add docker image to public ecr on ci pass [\#729](https://github.com/aws-observability/aws-otel-collector/issues/729)
- Metrics exporting issue, unknown field "doubleSum" in v1.Metric [\#707](https://github.com/aws-observability/aws-otel-collector/issues/707)
- Label job on file\_sd\_configs produces unknown errors on ADOT [\#665](https://github.com/aws-observability/aws-otel-collector/issues/665)
- Replace free GitHub Ubuntu runner by fixed AWS EC2 host [\#656](https://github.com/aws-observability/aws-otel-collector/issues/656)
- Allow metric type overrides for prometheus scrape [\#629](https://github.com/aws-observability/aws-otel-collector/issues/629)
- Combine ECR and DockerHub download rate metrics into one [\#622](https://github.com/aws-observability/aws-otel-collector/issues/622)
- Allow collector to run as a different user [\#618](https://github.com/aws-observability/aws-otel-collector/issues/618)
- AWS Prometheus Remote Write Exporter - Retry [\#605](https://github.com/aws-observability/aws-otel-collector/issues/605)
- Collector on ECS Fargate not showing the correct environment in X-Ray [\#590](https://github.com/aws-observability/aws-otel-collector/issues/590)
- Collector container showing error about unable to resolve host or service.  [\#585](https://github.com/aws-observability/aws-otel-collector/issues/585)
- latest otel collector version for ec2 and fargate [\#579](https://github.com/aws-observability/aws-otel-collector/issues/579)
- Prometheus Counter has 0 value in CW [\#576](https://github.com/aws-observability/aws-otel-collector/issues/576)
- Prometheus histogram is translated to CW MinMaxSumCount with no Min and Max fields populated [\#575](https://github.com/aws-observability/aws-otel-collector/issues/575)
- Configure Logs Group for OTEL Metrics [\#573](https://github.com/aws-observability/aws-otel-collector/issues/573)
- otel errors [\#568](https://github.com/aws-observability/aws-otel-collector/issues/568)
- sending Java application custom metrics using OTEL to Cloudwatch [\#555](https://github.com/aws-observability/aws-otel-collector/issues/555)
- From which month AWS OTEL going to be supported in Production environment? [\#554](https://github.com/aws-observability/aws-otel-collector/issues/554)
- Sending springboot application custom metrics using OTEL [\#553](https://github.com/aws-observability/aws-otel-collector/issues/553)
- Exporter for AMP Credential error [\#520](https://github.com/aws-observability/aws-otel-collector/issues/520)
- Add steps to configure collector to send data to a different account [\#82](https://github.com/aws-observability/aws-otel-collector/issues/82)

**Merged pull requests:**

- Upgrade OTel dependencies to v0.43.1. [\#925](https://github.com/aws-observability/aws-otel-collector/pull/925) ([bryan-aguilar](https://github.com/bryan-aguilar))
- Fix cfn-lint for aws-otel-fargate-sidecar-deployment-cfn.yaml. \(\#906\) [\#909](https://github.com/aws-observability/aws-otel-collector/pull/909) ([sethAmazon](https://github.com/sethAmazon))
- EKS ARM64 test case matrix creation. [\#903](https://github.com/aws-observability/aws-otel-collector/pull/903) ([sethAmazon](https://github.com/sethAmazon))
- Update aws-otel-emitter in all examples. [\#900](https://github.com/aws-observability/aws-otel-collector/pull/900) ([vasireddy99](https://github.com/vasireddy99))
- Change build path for linux binaries to match GOARCH. [\#890](https://github.com/aws-observability/aws-otel-collector/pull/890) ([jefchien](https://github.com/jefchien))
- Add tests for ARM64 cluster. [\#889](https://github.com/aws-observability/aws-otel-collector/pull/889) ([sethAmazon](https://github.com/sethAmazon))
- Simplify Dockerfile copy. [\#885](https://github.com/aws-observability/aws-otel-collector/pull/885) ([jefchien](https://github.com/jefchien))
- Support buildx for amd64 and arm64 builds. \(\#766\) [\#870](https://github.com/aws-observability/aws-otel-collector/pull/870) ([sethAmazon](https://github.com/sethAmazon))
- Upgrade OTel dependencies to v0.42.0. [\#868](https://github.com/aws-observability/aws-otel-collector/pull/868) ([jefchien](https://github.com/jefchien))
- Upgrade containerd. [\#863](https://github.com/aws-observability/aws-otel-collector/pull/863) ([jefchien](https://github.com/jefchien))
- Add BuildKit for ADOT collector image. [\#860](https://github.com/aws-observability/aws-otel-collector/pull/860) ([khanhntd](https://github.com/khanhntd))
- Fix the lumberjack logger to ensure that all context fields are recorded. [\#849](https://github.com/aws-observability/aws-otel-collector/pull/849) ([Aneurysm9](https://github.com/Aneurysm9))
- Remove version filter and trigger performance tests on all passing runs. [\#842](https://github.com/aws-observability/aws-otel-collector/pull/842) ([sethAmazon](https://github.com/sethAmazon))
- Convert private ECR to public ECR. [\#840](https://github.com/aws-observability/aws-otel-collector/pull/840) ([khanhntd](https://github.com/khanhntd))
- Change operation per runs to cover all the issues. [\#837](https://github.com/aws-observability/aws-otel-collector/pull/837) ([khanhntd](https://github.com/khanhntd))
- Destroy var file For Operator E2E tests. [\#835](https://github.com/aws-observability/aws-otel-collector/pull/835) ([sethAmazon](https://github.com/sethAmazon))
- Remove soaking test workflow. [\#832](https://github.com/aws-observability/aws-otel-collector/pull/832) ([jefchien](https://github.com/jefchien))
- Add test case for OTLP and send metrics,traces to cloudwatch, AMP, XRAY with ECS on EC2. [\#824](https://github.com/aws-observability/aws-otel-collector/pull/824) ([khanhntd](https://github.com/khanhntd))
- Trigger performance test after successful CI run. [\#818](https://github.com/aws-observability/aws-otel-collector/pull/818) ([jefchien](https://github.com/jefchien))
- Upgrade OTel dependencies to v0.41.0. [\#798](https://github.com/aws-observability/aws-otel-collector/pull/798) ([jefchien](https://github.com/jefchien))
- Upgrade OTel dependencies to v0.40.0. [\#778](https://github.com/aws-observability/aws-otel-collector/pull/778) ([jefchien](https://github.com/jefchien))
- Add OTLP to AMP Metric Test. [\#762](https://github.com/aws-observability/aws-otel-collector/pull/762) ([sethAmazon](https://github.com/sethAmazon))



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
