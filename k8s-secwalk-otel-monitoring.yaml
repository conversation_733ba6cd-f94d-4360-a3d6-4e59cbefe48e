# ServiceMonitor for monitoring OTEL Collector itself (requires Prometheus Operator)
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: otel-collector-secwalk
  namespace: secwalk-monitoring
  labels:
    app: otel-collector-secwalk
    component: monitoring
spec:
  selector:
    matchLabels:
      app: otel-collector-secwalk
      component: service
  endpoints:
    - port: metrics
      interval: 30s
      path: /metrics
      scheme: http

---
# PodMonitor for monitoring OTEL Collector pods directly (alternative to ServiceMonitor)
apiVersion: monitoring.coreos.com/v1
kind: PodMonitor
metadata:
  name: otel-collector-secwalk-pods
  namespace: secwalk-monitoring
  labels:
    app: otel-collector-secwalk
    component: pod-monitoring
spec:
  selector:
    matchLabels:
      app: otel-collector-secwalk
  podMetricsEndpoints:
    - port: metrics
      interval: 30s
      path: /metrics
      scheme: http

---
# HorizontalPodAutoscaler for auto-scaling based on CPU/Memory usage
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: otel-collector-secwalk-hpa
  namespace: secwalk-monitoring
  labels:
    app: otel-collector-secwalk
    component: autoscaling
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: otel-collector-secwalk
  minReplicas: 2
  maxReplicas: 10
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
        - type: Percent
          value: 10
          periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
        - type: Percent
          value: 50
          periodSeconds: 60

---
# PodDisruptionBudget to ensure availability during updates
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: otel-collector-secwalk-pdb
  namespace: secwalk-monitoring
  labels:
    app: otel-collector-secwalk
    component: disruption-budget
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: otel-collector-secwalk

---
# NetworkPolicy to control traffic to OTEL Collector (optional, adjust as needed)
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: otel-collector-secwalk-netpol
  namespace: secwalk-monitoring
  labels:
    app: otel-collector-secwalk
    component: network-policy
spec:
  podSelector:
    matchLabels:
      app: otel-collector-secwalk
  policyTypes:
    - Ingress
    - Egress
  ingress:
    # Allow traffic from secwalk service namespace
    - from:
        - namespaceSelector:
            matchLabels:
              name: secwalk  # Adjust to your secwalk service namespace
      ports:
        - protocol: TCP
          port: 4317
        - protocol: TCP
          port: 4318
        - protocol: UDP
          port: 2000
    # Allow monitoring traffic
    - from:
        - namespaceSelector:
            matchLabels:
              name: monitoring
      ports:
        - protocol: TCP
          port: 8888
        - protocol: TCP
          port: 13133
  egress:
    # Allow egress to AWS services
    - {}  # Allow all egress (adjust as needed for security)
