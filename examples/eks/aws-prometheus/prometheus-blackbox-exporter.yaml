---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blackbox-exporter-deployment
  namespace: otelcol
  labels:
    app: blackbox-exporter
spec:
  replicas: 1
  selector:
    matchLabels:
      app: blackbox-exporter
  template:
    metadata:
      labels:
        app: blackbox-exporter
    spec:
      serviceAccountName: otelcol-admin
      containers:
      - name: blackbox-exporter-cont
        image: prom/blackbox-exporter
        ports:
        - name: web
          containerPort: 9115
---
apiVersion: v1
kind: Service
metadata:
  name: blackbox-exporter-service
  namespace: otelcol
  labels:
    app: blackbox-exporter
spec:
  ports:
  - name: web
    port: 9115
    targetPort: 9115
    protocol: TCP
  selector:
    app: blackbox-exporter
  type: NodePort
---