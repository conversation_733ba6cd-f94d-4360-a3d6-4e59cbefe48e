---
# create a namespace
apiVersion: v1
kind: Namespace
metadata:
  name: aoc-prometheus-pipeline-demo
  labels:
    name: prometheus-sample-app
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus-sample-app
  namespace: aoc-prometheus-pipeline-demo
  labels:
    app: prometheus-sample-app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus-sample-app
  template:
    metadata:
      labels:
        app: prometheus-sample-app
    spec:
      containers:
      - name: prometheus-sample-app
        image: "" # change to your local image
        command: ["/bin/main", "-listen_address=0.0.0.0:8080", "-metric_count=10"]
        ports:
        - name: web
          containerPort: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus-sample-app-service
  namespace: aoc-prometheus-pipeline-demo
  labels:
    app: prometheus-sample-app
  annotations:
    scrape: "true"
spec:
  ports:
  - name: web
    port: 8080
    targetPort: 8080
    protocol: TCP
  selector:
    app: prometheus-sample-app
---
