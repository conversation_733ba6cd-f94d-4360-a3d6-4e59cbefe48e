{"family": "aws-otel-FARGATE", "taskRoleArn": "{{ecsTaskRoleArn}}", "executionRoleArn": "{{ecsTaskExecutionRoleArn}}", "networkMode": "awsvpc", "containerDefinitions": [{"name": "aws-otel-collector", "image": "amazon/aws-otel-collector", "command": ["{{command}}"], "essential": true, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/ecs-aws-otel-sidecar-collector", "awslogs-region": "{{region}}", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "True"}}, "healthCheck": {"command": ["/healthcheck"], "interval": 5, "timeout": 6, "retries": 5, "startPeriod": 1}}, {"name": "aws-otel-emitter", "image": "public.ecr.aws/aws-otel-test/aws-otel-goxray-sample-app:latest", "essential": false, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/ecs-aws-otel-sidecar-app", "awslogs-region": "{{region}}", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "True"}}, "dependsOn": [{"containerName": "aws-otel-collector", "condition": "START"}]}, {"image": "public.ecr.aws/nginx/nginx:latest", "name": "nginx", "essential": false, "dependsOn": [{"containerName": "aws-otel-collector", "condition": "START"}]}, {"image": "alpine/socat:latest", "memory": 512, "dependsOn": [{"containerName": "aws-otel-collector", "condition": "START"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "True", "awslogs-region": "us-west-2", "awslogs-stream-prefix": "ecs", "awslogs-group": "/ecs/statsd-emitter"}}, "entryPoint": ["/bin/sh", "-c", "while true; do echo 'statsdTestMetric:1|c' | socat -v -t 0 - UDP:127.0.0.1:8125; sleep 1; done"], "name": "aoc-statsd-emitter", "cpu": 256, "essential": false}], "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512"}