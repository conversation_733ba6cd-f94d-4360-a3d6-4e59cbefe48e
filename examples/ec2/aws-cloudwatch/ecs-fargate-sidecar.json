{"family": "aws-otel-FARGATE", "taskRoleArn": "{{ecsTaskRoleArn}}", "executionRoleArn": "{{ecsExecutionRoleArn}}", "networkMode": "awsvpc", "containerDefinitions": [{"name": "aws-otel-emitter", "image": "aottestbed/aws-otel-collector-sample-app:java-0.1.0", "essential": true, "entryPoint": [], "command": [], "environment": [{"name": "OTEL_OTLP_ENDPOINT", "value": "localhost:4317"}, {"name": "OTEL_RESOURCE_ATTRIBUTES", "value": "service.namespace=AWSObservability,service.name=CloudWatchOTService"}, {"name": "S3_REGION", "value": "{{region}}"}], "dependsOn": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/ecs-aws-otel-sidecar-emitter", "awslogs-region": "{{region}}", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "True"}}}, {"name": "aws-otel-collector", "image": "public.ecr.aws/aws-observability/aws-otel-collector:latest", "portMappings": [{"containerPort": 4317, "hostPort": 4317, "protocol": "tcp"}], "essential": true, "entryPoint": [], "command": [], "environment": [], "dependsOn": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/ecs-aws-otel-sidecar-collector", "awslogs-region": "{{region}}", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "True"}}}], "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512"}