# 多服务监测配置示例
# 同时监测多个服务的环境变量配置

env:
  # 主要监测服务 - SecWalk
  - name: MONITOR_SERVICE_NAME
    value: "secwalk"
  - name: MONITOR_SERVICE_TARGET
    value: "secwalk-service.secwalk-namespace:8080"
  - name: SCRAPE_INTERVAL
    value: "30s"
  - name: METRICS_PATH
    value: "/metrics"
  
  # 额外监测服务1 - API Gateway
  - name: MONITOR_SERVICE_TARGET_2
    value: "api-gateway-service.api-namespace:9090"
  - name: SCRAPE_INTERVAL_2
    value: "15s"
  - name: METRICS_PATH_2
    value: "/actuator/prometheus"
  - name: SCRAPE_TIMEOUT_2
    value: "5s"
  
  # 额外监测服务2 - Database Service
  - name: MONITOR_SERVICE_TARGET_3
    value: "database-exporter.db-namespace:9187"
  - name: SCRAPE_INTERVAL_3
    value: "60s"
  - name: METRICS_PATH_3
    value: "/metrics"
  - name: SCRAPE_TIMEOUT_3
    value: "15s"
  
  # 通用服务标识
  - name: SERVICE_NAMESPACE
    value: "microservices"
  - name: SERVICE_COMPONENT
    value: "multi-service-stack"
  
  # CloudWatch配置
  - name: CLOUDWATCH_NAMESPACE
    value: "MicroServices/Metrics"
  - name: CLOUDWATCH_LOG_GROUP
    value: "/aws/otel/microservices"
  - name: CLOUDWATCH_LOGS_GROUP
    value: "/aws/otel/microservices/logs"
  
  # 指标选择器 - 匹配所有服务的指标
  - name: METRIC_SELECTOR_1
    value: "*"

# 各个服务需要配置的OTEL环境变量示例

# SecWalk服务配置
secwalk_service_env:
  - name: OTEL_EXPORTER_OTLP_ENDPOINT
    value: "http://otel-collector-universal.monitoring.svc.cluster.local:4317"
  - name: OTEL_SERVICE_NAME
    value: "secwalk"
  - name: OTEL_SERVICE_NAMESPACE
    value: "security"

# API Gateway服务配置
api_gateway_service_env:
  - name: OTEL_EXPORTER_OTLP_ENDPOINT
    value: "http://otel-collector-universal.monitoring.svc.cluster.local:4317"
  - name: OTEL_SERVICE_NAME
    value: "api-gateway"
  - name: OTEL_SERVICE_NAMESPACE
    value: "gateway"

# Database服务配置（如果支持OTEL）
database_service_env:
  - name: OTEL_EXPORTER_OTLP_ENDPOINT
    value: "http://otel-collector-universal.monitoring.svc.cluster.local:4317"
  - name: OTEL_SERVICE_NAME
    value: "database"
  - name: OTEL_SERVICE_NAMESPACE
    value: "data"
