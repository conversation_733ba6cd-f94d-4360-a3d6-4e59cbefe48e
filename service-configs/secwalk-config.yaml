# SecWalk服务监测配置示例
# 将这些环境变量添加到 k8s-universal-otel-deployment.yaml 中

env:
  # 主要监测服务配置 - SecWalk
  - name: MONITOR_SERVICE_NAME
    value: "secwalk"
  - name: MONITOR_SERVICE_TARGET
    value: "secwalk-service.secwalk-namespace:8080"  # 调整为实际的服务名和命名空间
  - name: SCRAPE_INTERVAL
    value: "30s"
  - name: METRICS_PATH
    value: "/metrics"
  - name: SCRAPE_TIMEOUT
    value: "10s"
  
  # 服务标识
  - name: SERVICE_NAMESPACE
    value: "security"
  - name: SERVICE_COMPONENT
    value: "security-scanner"
  - name: SERVICE_VERSION
    value: "v1.0.0"
  
  # CloudWatch配置
  - name: CLOUDWATCH_NAMESPACE
    value: "SecWalk/Metrics"
  - name: CLOUDWATCH_LOG_GROUP
    value: "/aws/otel/secwalk"
  - name: CLOUDWATCH_LOG_STREAM
    value: "secwalk-metrics"
  - name: CLOUDWATCH_LOGS_GROUP
    value: "/aws/otel/secwalk/logs"
  - name: CLOUDWATCH_LOGS_STREAM
    value: "secwalk-application-logs"
  
  # 指标选择器
  - name: METRIC_SELECTOR_1
    value: "secwalk.*"

# SecWalk服务需要配置的OTEL环境变量
# 在SecWalk服务的Deployment中添加：
secwalk_service_env:
  - name: OTEL_EXPORTER_OTLP_ENDPOINT
    value: "http://otel-collector-universal.monitoring.svc.cluster.local:4317"
  - name: OTEL_SERVICE_NAME
    value: "secwalk"
  - name: OTEL_SERVICE_NAMESPACE
    value: "security"
  - name: OTEL_SERVICE_VERSION
    value: "v1.0.0"
  - name: OTEL_RESOURCE_ATTRIBUTES
    value: "service.name=secwalk,service.namespace=security,deployment.environment=production"
