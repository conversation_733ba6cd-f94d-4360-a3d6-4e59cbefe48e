// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: proto/metrics/agent_payload.proto

package gogen

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type MetricPayload_MetricType int32

const (
	MetricPayload_UNSPECIFIED MetricPayload_MetricType = 0
	MetricPayload_COUNT       MetricPayload_MetricType = 1
	MetricPayload_RATE        MetricPayload_MetricType = 2
	MetricPayload_GAUGE       MetricPayload_MetricType = 3
)

var MetricPayload_MetricType_name = map[int32]string{
	0: "UNSPECIFIED",
	1: "COUNT",
	2: "RATE",
	3: "GAUGE",
}

var MetricPayload_MetricType_value = map[string]int32{
	"UNSPECIFIED": 0,
	"COUNT":       1,
	"RATE":        2,
	"GAUGE":       3,
}

func (x MetricPayload_MetricType) String() string {
	return proto.EnumName(MetricPayload_MetricType_name, int32(x))
}

func (MetricPayload_MetricType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_0063f25d3d575515, []int{3, 0}
}

type CommonMetadata struct {
	AgentVersion         string   `protobuf:"bytes,1,opt,name=agent_version,json=agentVersion,proto3" json:"agent_version,omitempty"`
	Timezone             string   `protobuf:"bytes,2,opt,name=timezone,proto3" json:"timezone,omitempty"`
	CurrentEpoch         float64  `protobuf:"fixed64,3,opt,name=current_epoch,json=currentEpoch,proto3" json:"current_epoch,omitempty"`
	InternalIp           string   `protobuf:"bytes,4,opt,name=internal_ip,json=internalIp,proto3" json:"internal_ip,omitempty"`
	PublicIp             string   `protobuf:"bytes,5,opt,name=public_ip,json=publicIp,proto3" json:"public_ip,omitempty"`
	ApiKey               string   `protobuf:"bytes,6,opt,name=api_key,json=apiKey,proto3" json:"api_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonMetadata) Reset()         { *m = CommonMetadata{} }
func (m *CommonMetadata) String() string { return proto.CompactTextString(m) }
func (*CommonMetadata) ProtoMessage()    {}
func (*CommonMetadata) Descriptor() ([]byte, []int) {
	return fileDescriptor_0063f25d3d575515, []int{0}
}
func (m *CommonMetadata) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CommonMetadata) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CommonMetadata.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CommonMetadata) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonMetadata.Merge(m, src)
}
func (m *CommonMetadata) XXX_Size() int {
	return m.Size()
}
func (m *CommonMetadata) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonMetadata.DiscardUnknown(m)
}

var xxx_messageInfo_CommonMetadata proto.InternalMessageInfo

func (m *CommonMetadata) GetAgentVersion() string {
	if m != nil {
		return m.AgentVersion
	}
	return ""
}

func (m *CommonMetadata) GetTimezone() string {
	if m != nil {
		return m.Timezone
	}
	return ""
}

func (m *CommonMetadata) GetCurrentEpoch() float64 {
	if m != nil {
		return m.CurrentEpoch
	}
	return 0
}

func (m *CommonMetadata) GetInternalIp() string {
	if m != nil {
		return m.InternalIp
	}
	return ""
}

func (m *CommonMetadata) GetPublicIp() string {
	if m != nil {
		return m.PublicIp
	}
	return ""
}

func (m *CommonMetadata) GetApiKey() string {
	if m != nil {
		return m.ApiKey
	}
	return ""
}

type Origin struct {
	OriginProduct        uint32   `protobuf:"varint,4,opt,name=origin_product,json=originProduct,proto3" json:"origin_product,omitempty"`
	OriginCategory       uint32   `protobuf:"varint,5,opt,name=origin_category,json=originCategory,proto3" json:"origin_category,omitempty"`
	OriginService        uint32   `protobuf:"varint,6,opt,name=origin_service,json=originService,proto3" json:"origin_service,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Origin) Reset()         { *m = Origin{} }
func (m *Origin) String() string { return proto.CompactTextString(m) }
func (*Origin) ProtoMessage()    {}
func (*Origin) Descriptor() ([]byte, []int) {
	return fileDescriptor_0063f25d3d575515, []int{1}
}
func (m *Origin) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Origin) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Origin.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Origin) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Origin.Merge(m, src)
}
func (m *Origin) XXX_Size() int {
	return m.Size()
}
func (m *Origin) XXX_DiscardUnknown() {
	xxx_messageInfo_Origin.DiscardUnknown(m)
}

var xxx_messageInfo_Origin proto.InternalMessageInfo

func (m *Origin) GetOriginProduct() uint32 {
	if m != nil {
		return m.OriginProduct
	}
	return 0
}

func (m *Origin) GetOriginCategory() uint32 {
	if m != nil {
		return m.OriginCategory
	}
	return 0
}

func (m *Origin) GetOriginService() uint32 {
	if m != nil {
		return m.OriginService
	}
	return 0
}

// Metadata is used in both the MetricSeries and Sketch messages defined below.
type Metadata struct {
	Origin               *Origin  `protobuf:"bytes,1,opt,name=origin,proto3" json:"origin,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Metadata) Reset()         { *m = Metadata{} }
func (m *Metadata) String() string { return proto.CompactTextString(m) }
func (*Metadata) ProtoMessage()    {}
func (*Metadata) Descriptor() ([]byte, []int) {
	return fileDescriptor_0063f25d3d575515, []int{2}
}
func (m *Metadata) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Metadata) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Metadata.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Metadata) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Metadata.Merge(m, src)
}
func (m *Metadata) XXX_Size() int {
	return m.Size()
}
func (m *Metadata) XXX_DiscardUnknown() {
	xxx_messageInfo_Metadata.DiscardUnknown(m)
}

var xxx_messageInfo_Metadata proto.InternalMessageInfo

func (m *Metadata) GetOrigin() *Origin {
	if m != nil {
		return m.Origin
	}
	return nil
}

type MetricPayload struct {
	Series               []*MetricPayload_MetricSeries `protobuf:"bytes,1,rep,name=series,proto3" json:"series,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *MetricPayload) Reset()         { *m = MetricPayload{} }
func (m *MetricPayload) String() string { return proto.CompactTextString(m) }
func (*MetricPayload) ProtoMessage()    {}
func (*MetricPayload) Descriptor() ([]byte, []int) {
	return fileDescriptor_0063f25d3d575515, []int{3}
}
func (m *MetricPayload) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MetricPayload) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MetricPayload.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MetricPayload) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MetricPayload.Merge(m, src)
}
func (m *MetricPayload) XXX_Size() int {
	return m.Size()
}
func (m *MetricPayload) XXX_DiscardUnknown() {
	xxx_messageInfo_MetricPayload.DiscardUnknown(m)
}

var xxx_messageInfo_MetricPayload proto.InternalMessageInfo

func (m *MetricPayload) GetSeries() []*MetricPayload_MetricSeries {
	if m != nil {
		return m.Series
	}
	return nil
}

type MetricPayload_MetricPoint struct {
	// metric value
	Value float64 `protobuf:"fixed64,1,opt,name=value,proto3" json:"value,omitempty"`
	// timestamp for this value in seconds since the UNIX epoch
	Timestamp            int64    `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MetricPayload_MetricPoint) Reset()         { *m = MetricPayload_MetricPoint{} }
func (m *MetricPayload_MetricPoint) String() string { return proto.CompactTextString(m) }
func (*MetricPayload_MetricPoint) ProtoMessage()    {}
func (*MetricPayload_MetricPoint) Descriptor() ([]byte, []int) {
	return fileDescriptor_0063f25d3d575515, []int{3, 0}
}
func (m *MetricPayload_MetricPoint) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MetricPayload_MetricPoint) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MetricPayload_MetricPoint.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MetricPayload_MetricPoint) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MetricPayload_MetricPoint.Merge(m, src)
}
func (m *MetricPayload_MetricPoint) XXX_Size() int {
	return m.Size()
}
func (m *MetricPayload_MetricPoint) XXX_DiscardUnknown() {
	xxx_messageInfo_MetricPayload_MetricPoint.DiscardUnknown(m)
}

var xxx_messageInfo_MetricPayload_MetricPoint proto.InternalMessageInfo

func (m *MetricPayload_MetricPoint) GetValue() float64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *MetricPayload_MetricPoint) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

type MetricPayload_Resource struct {
	Type                 string   `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MetricPayload_Resource) Reset()         { *m = MetricPayload_Resource{} }
func (m *MetricPayload_Resource) String() string { return proto.CompactTextString(m) }
func (*MetricPayload_Resource) ProtoMessage()    {}
func (*MetricPayload_Resource) Descriptor() ([]byte, []int) {
	return fileDescriptor_0063f25d3d575515, []int{3, 1}
}
func (m *MetricPayload_Resource) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MetricPayload_Resource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MetricPayload_Resource.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MetricPayload_Resource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MetricPayload_Resource.Merge(m, src)
}
func (m *MetricPayload_Resource) XXX_Size() int {
	return m.Size()
}
func (m *MetricPayload_Resource) XXX_DiscardUnknown() {
	xxx_messageInfo_MetricPayload_Resource.DiscardUnknown(m)
}

var xxx_messageInfo_MetricPayload_Resource proto.InternalMessageInfo

func (m *MetricPayload_Resource) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *MetricPayload_Resource) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type MetricPayload_MetricSeries struct {
	// Resources this series applies to; include at least
	// { type="host", name=<hostname> }
	Resources []*MetricPayload_Resource `protobuf:"bytes,1,rep,name=resources,proto3" json:"resources,omitempty"`
	// metric name
	Metric string `protobuf:"bytes,2,opt,name=metric,proto3" json:"metric,omitempty"`
	// tags for this metric
	Tags []string `protobuf:"bytes,3,rep,name=tags,proto3" json:"tags,omitempty"`
	// data points for this metric
	Points []*MetricPayload_MetricPoint `protobuf:"bytes,4,rep,name=points,proto3" json:"points,omitempty"`
	// type of metric
	Type MetricPayload_MetricType `protobuf:"varint,5,opt,name=type,proto3,enum=datadog.agentpayload.MetricPayload_MetricType" json:"type,omitempty"`
	// metric unit name
	Unit string `protobuf:"bytes,6,opt,name=unit,proto3" json:"unit,omitempty"`
	// source of this metric (check name, etc.)
	SourceTypeName string `protobuf:"bytes,7,opt,name=source_type_name,json=sourceTypeName,proto3" json:"source_type_name,omitempty"`
	// interval, in seconds, between samples of this metric
	Interval int64 `protobuf:"varint,8,opt,name=interval,proto3" json:"interval,omitempty"`
	// Metrics origin metadata
	Metadata             *Metadata `protobuf:"bytes,9,opt,name=metadata,proto3" json:"metadata,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *MetricPayload_MetricSeries) Reset()         { *m = MetricPayload_MetricSeries{} }
func (m *MetricPayload_MetricSeries) String() string { return proto.CompactTextString(m) }
func (*MetricPayload_MetricSeries) ProtoMessage()    {}
func (*MetricPayload_MetricSeries) Descriptor() ([]byte, []int) {
	return fileDescriptor_0063f25d3d575515, []int{3, 2}
}
func (m *MetricPayload_MetricSeries) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MetricPayload_MetricSeries) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MetricPayload_MetricSeries.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MetricPayload_MetricSeries) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MetricPayload_MetricSeries.Merge(m, src)
}
func (m *MetricPayload_MetricSeries) XXX_Size() int {
	return m.Size()
}
func (m *MetricPayload_MetricSeries) XXX_DiscardUnknown() {
	xxx_messageInfo_MetricPayload_MetricSeries.DiscardUnknown(m)
}

var xxx_messageInfo_MetricPayload_MetricSeries proto.InternalMessageInfo

func (m *MetricPayload_MetricSeries) GetResources() []*MetricPayload_Resource {
	if m != nil {
		return m.Resources
	}
	return nil
}

func (m *MetricPayload_MetricSeries) GetMetric() string {
	if m != nil {
		return m.Metric
	}
	return ""
}

func (m *MetricPayload_MetricSeries) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *MetricPayload_MetricSeries) GetPoints() []*MetricPayload_MetricPoint {
	if m != nil {
		return m.Points
	}
	return nil
}

func (m *MetricPayload_MetricSeries) GetType() MetricPayload_MetricType {
	if m != nil {
		return m.Type
	}
	return MetricPayload_UNSPECIFIED
}

func (m *MetricPayload_MetricSeries) GetUnit() string {
	if m != nil {
		return m.Unit
	}
	return ""
}

func (m *MetricPayload_MetricSeries) GetSourceTypeName() string {
	if m != nil {
		return m.SourceTypeName
	}
	return ""
}

func (m *MetricPayload_MetricSeries) GetInterval() int64 {
	if m != nil {
		return m.Interval
	}
	return 0
}

func (m *MetricPayload_MetricSeries) GetMetadata() *Metadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

type EventsPayload struct {
	Events               []*EventsPayload_Event `protobuf:"bytes,1,rep,name=events,proto3" json:"events,omitempty"`
	Metadata             *CommonMetadata        `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *EventsPayload) Reset()         { *m = EventsPayload{} }
func (m *EventsPayload) String() string { return proto.CompactTextString(m) }
func (*EventsPayload) ProtoMessage()    {}
func (*EventsPayload) Descriptor() ([]byte, []int) {
	return fileDescriptor_0063f25d3d575515, []int{4}
}
func (m *EventsPayload) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EventsPayload) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EventsPayload.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *EventsPayload) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EventsPayload.Merge(m, src)
}
func (m *EventsPayload) XXX_Size() int {
	return m.Size()
}
func (m *EventsPayload) XXX_DiscardUnknown() {
	xxx_messageInfo_EventsPayload.DiscardUnknown(m)
}

var xxx_messageInfo_EventsPayload proto.InternalMessageInfo

func (m *EventsPayload) GetEvents() []*EventsPayload_Event {
	if m != nil {
		return m.Events
	}
	return nil
}

func (m *EventsPayload) GetMetadata() *CommonMetadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

type EventsPayload_Event struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Ts                   int64    `protobuf:"varint,3,opt,name=ts,proto3" json:"ts,omitempty"`
	Priority             string   `protobuf:"bytes,4,opt,name=priority,proto3" json:"priority,omitempty"`
	Host                 string   `protobuf:"bytes,5,opt,name=host,proto3" json:"host,omitempty"`
	Tags                 []string `protobuf:"bytes,6,rep,name=tags,proto3" json:"tags,omitempty"`
	AlertType            string   `protobuf:"bytes,7,opt,name=alert_type,json=alertType,proto3" json:"alert_type,omitempty"`
	AggregationKey       string   `protobuf:"bytes,8,opt,name=aggregation_key,json=aggregationKey,proto3" json:"aggregation_key,omitempty"`
	SourceTypeName       string   `protobuf:"bytes,9,opt,name=source_type_name,json=sourceTypeName,proto3" json:"source_type_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EventsPayload_Event) Reset()         { *m = EventsPayload_Event{} }
func (m *EventsPayload_Event) String() string { return proto.CompactTextString(m) }
func (*EventsPayload_Event) ProtoMessage()    {}
func (*EventsPayload_Event) Descriptor() ([]byte, []int) {
	return fileDescriptor_0063f25d3d575515, []int{4, 0}
}
func (m *EventsPayload_Event) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EventsPayload_Event) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EventsPayload_Event.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *EventsPayload_Event) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EventsPayload_Event.Merge(m, src)
}
func (m *EventsPayload_Event) XXX_Size() int {
	return m.Size()
}
func (m *EventsPayload_Event) XXX_DiscardUnknown() {
	xxx_messageInfo_EventsPayload_Event.DiscardUnknown(m)
}

var xxx_messageInfo_EventsPayload_Event proto.InternalMessageInfo

func (m *EventsPayload_Event) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *EventsPayload_Event) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *EventsPayload_Event) GetTs() int64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *EventsPayload_Event) GetPriority() string {
	if m != nil {
		return m.Priority
	}
	return ""
}

func (m *EventsPayload_Event) GetHost() string {
	if m != nil {
		return m.Host
	}
	return ""
}

func (m *EventsPayload_Event) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *EventsPayload_Event) GetAlertType() string {
	if m != nil {
		return m.AlertType
	}
	return ""
}

func (m *EventsPayload_Event) GetAggregationKey() string {
	if m != nil {
		return m.AggregationKey
	}
	return ""
}

func (m *EventsPayload_Event) GetSourceTypeName() string {
	if m != nil {
		return m.SourceTypeName
	}
	return ""
}

type SketchPayload struct {
	Sketches             []SketchPayload_Sketch `protobuf:"bytes,1,rep,name=sketches,proto3" json:"sketches"`
	Metadata             CommonMetadata         `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *SketchPayload) Reset()         { *m = SketchPayload{} }
func (m *SketchPayload) String() string { return proto.CompactTextString(m) }
func (*SketchPayload) ProtoMessage()    {}
func (*SketchPayload) Descriptor() ([]byte, []int) {
	return fileDescriptor_0063f25d3d575515, []int{5}
}
func (m *SketchPayload) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SketchPayload) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SketchPayload.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SketchPayload) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SketchPayload.Merge(m, src)
}
func (m *SketchPayload) XXX_Size() int {
	return m.Size()
}
func (m *SketchPayload) XXX_DiscardUnknown() {
	xxx_messageInfo_SketchPayload.DiscardUnknown(m)
}

var xxx_messageInfo_SketchPayload proto.InternalMessageInfo

func (m *SketchPayload) GetSketches() []SketchPayload_Sketch {
	if m != nil {
		return m.Sketches
	}
	return nil
}

func (m *SketchPayload) GetMetadata() CommonMetadata {
	if m != nil {
		return m.Metadata
	}
	return CommonMetadata{}
}

type SketchPayload_Sketch struct {
	Metric               string                              `protobuf:"bytes,1,opt,name=metric,proto3" json:"metric,omitempty"`
	Host                 string                              `protobuf:"bytes,2,opt,name=host,proto3" json:"host,omitempty"`
	Distributions        []SketchPayload_Sketch_Distribution `protobuf:"bytes,3,rep,name=distributions,proto3" json:"distributions"`
	Tags                 []string                            `protobuf:"bytes,4,rep,name=tags,proto3" json:"tags,omitempty"`
	Dogsketches          []SketchPayload_Sketch_Dogsketch    `protobuf:"bytes,7,rep,name=dogsketches,proto3" json:"dogsketches"`
	Metadata             *Metadata                           `protobuf:"bytes,8,opt,name=metadata,proto3" json:"metadata,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *SketchPayload_Sketch) Reset()         { *m = SketchPayload_Sketch{} }
func (m *SketchPayload_Sketch) String() string { return proto.CompactTextString(m) }
func (*SketchPayload_Sketch) ProtoMessage()    {}
func (*SketchPayload_Sketch) Descriptor() ([]byte, []int) {
	return fileDescriptor_0063f25d3d575515, []int{5, 0}
}
func (m *SketchPayload_Sketch) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SketchPayload_Sketch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SketchPayload_Sketch.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SketchPayload_Sketch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SketchPayload_Sketch.Merge(m, src)
}
func (m *SketchPayload_Sketch) XXX_Size() int {
	return m.Size()
}
func (m *SketchPayload_Sketch) XXX_DiscardUnknown() {
	xxx_messageInfo_SketchPayload_Sketch.DiscardUnknown(m)
}

var xxx_messageInfo_SketchPayload_Sketch proto.InternalMessageInfo

func (m *SketchPayload_Sketch) GetMetric() string {
	if m != nil {
		return m.Metric
	}
	return ""
}

func (m *SketchPayload_Sketch) GetHost() string {
	if m != nil {
		return m.Host
	}
	return ""
}

func (m *SketchPayload_Sketch) GetDistributions() []SketchPayload_Sketch_Distribution {
	if m != nil {
		return m.Distributions
	}
	return nil
}

func (m *SketchPayload_Sketch) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *SketchPayload_Sketch) GetDogsketches() []SketchPayload_Sketch_Dogsketch {
	if m != nil {
		return m.Dogsketches
	}
	return nil
}

func (m *SketchPayload_Sketch) GetMetadata() *Metadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

type SketchPayload_Sketch_Distribution struct {
	Ts                   int64     `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Cnt                  int64     `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	Min                  float64   `protobuf:"fixed64,3,opt,name=min,proto3" json:"min,omitempty"`
	Max                  float64   `protobuf:"fixed64,4,opt,name=max,proto3" json:"max,omitempty"`
	Avg                  float64   `protobuf:"fixed64,5,opt,name=avg,proto3" json:"avg,omitempty"`
	Sum                  float64   `protobuf:"fixed64,6,opt,name=sum,proto3" json:"sum,omitempty"`
	V                    []float64 `protobuf:"fixed64,7,rep,packed,name=v,proto3" json:"v,omitempty"`
	G                    []uint32  `protobuf:"varint,8,rep,packed,name=g,proto3" json:"g,omitempty"`
	Delta                []uint32  `protobuf:"varint,9,rep,packed,name=delta,proto3" json:"delta,omitempty"`
	Buf                  []float64 `protobuf:"fixed64,10,rep,packed,name=buf,proto3" json:"buf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *SketchPayload_Sketch_Distribution) Reset()         { *m = SketchPayload_Sketch_Distribution{} }
func (m *SketchPayload_Sketch_Distribution) String() string { return proto.CompactTextString(m) }
func (*SketchPayload_Sketch_Distribution) ProtoMessage()    {}
func (*SketchPayload_Sketch_Distribution) Descriptor() ([]byte, []int) {
	return fileDescriptor_0063f25d3d575515, []int{5, 0, 0}
}
func (m *SketchPayload_Sketch_Distribution) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SketchPayload_Sketch_Distribution) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SketchPayload_Sketch_Distribution.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SketchPayload_Sketch_Distribution) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SketchPayload_Sketch_Distribution.Merge(m, src)
}
func (m *SketchPayload_Sketch_Distribution) XXX_Size() int {
	return m.Size()
}
func (m *SketchPayload_Sketch_Distribution) XXX_DiscardUnknown() {
	xxx_messageInfo_SketchPayload_Sketch_Distribution.DiscardUnknown(m)
}

var xxx_messageInfo_SketchPayload_Sketch_Distribution proto.InternalMessageInfo

func (m *SketchPayload_Sketch_Distribution) GetTs() int64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *SketchPayload_Sketch_Distribution) GetCnt() int64 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

func (m *SketchPayload_Sketch_Distribution) GetMin() float64 {
	if m != nil {
		return m.Min
	}
	return 0
}

func (m *SketchPayload_Sketch_Distribution) GetMax() float64 {
	if m != nil {
		return m.Max
	}
	return 0
}

func (m *SketchPayload_Sketch_Distribution) GetAvg() float64 {
	if m != nil {
		return m.Avg
	}
	return 0
}

func (m *SketchPayload_Sketch_Distribution) GetSum() float64 {
	if m != nil {
		return m.Sum
	}
	return 0
}

func (m *SketchPayload_Sketch_Distribution) GetV() []float64 {
	if m != nil {
		return m.V
	}
	return nil
}

func (m *SketchPayload_Sketch_Distribution) GetG() []uint32 {
	if m != nil {
		return m.G
	}
	return nil
}

func (m *SketchPayload_Sketch_Distribution) GetDelta() []uint32 {
	if m != nil {
		return m.Delta
	}
	return nil
}

func (m *SketchPayload_Sketch_Distribution) GetBuf() []float64 {
	if m != nil {
		return m.Buf
	}
	return nil
}

type SketchPayload_Sketch_Dogsketch struct {
	Ts                   int64    `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Cnt                  int64    `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	Min                  float64  `protobuf:"fixed64,3,opt,name=min,proto3" json:"min,omitempty"`
	Max                  float64  `protobuf:"fixed64,4,opt,name=max,proto3" json:"max,omitempty"`
	Avg                  float64  `protobuf:"fixed64,5,opt,name=avg,proto3" json:"avg,omitempty"`
	Sum                  float64  `protobuf:"fixed64,6,opt,name=sum,proto3" json:"sum,omitempty"`
	K                    []int32  `protobuf:"zigzag32,7,rep,packed,name=k,proto3" json:"k,omitempty"`
	N                    []uint32 `protobuf:"varint,8,rep,packed,name=n,proto3" json:"n,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SketchPayload_Sketch_Dogsketch) Reset()         { *m = SketchPayload_Sketch_Dogsketch{} }
func (m *SketchPayload_Sketch_Dogsketch) String() string { return proto.CompactTextString(m) }
func (*SketchPayload_Sketch_Dogsketch) ProtoMessage()    {}
func (*SketchPayload_Sketch_Dogsketch) Descriptor() ([]byte, []int) {
	return fileDescriptor_0063f25d3d575515, []int{5, 0, 1}
}
func (m *SketchPayload_Sketch_Dogsketch) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SketchPayload_Sketch_Dogsketch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SketchPayload_Sketch_Dogsketch.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SketchPayload_Sketch_Dogsketch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SketchPayload_Sketch_Dogsketch.Merge(m, src)
}
func (m *SketchPayload_Sketch_Dogsketch) XXX_Size() int {
	return m.Size()
}
func (m *SketchPayload_Sketch_Dogsketch) XXX_DiscardUnknown() {
	xxx_messageInfo_SketchPayload_Sketch_Dogsketch.DiscardUnknown(m)
}

var xxx_messageInfo_SketchPayload_Sketch_Dogsketch proto.InternalMessageInfo

func (m *SketchPayload_Sketch_Dogsketch) GetTs() int64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *SketchPayload_Sketch_Dogsketch) GetCnt() int64 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

func (m *SketchPayload_Sketch_Dogsketch) GetMin() float64 {
	if m != nil {
		return m.Min
	}
	return 0
}

func (m *SketchPayload_Sketch_Dogsketch) GetMax() float64 {
	if m != nil {
		return m.Max
	}
	return 0
}

func (m *SketchPayload_Sketch_Dogsketch) GetAvg() float64 {
	if m != nil {
		return m.Avg
	}
	return 0
}

func (m *SketchPayload_Sketch_Dogsketch) GetSum() float64 {
	if m != nil {
		return m.Sum
	}
	return 0
}

func (m *SketchPayload_Sketch_Dogsketch) GetK() []int32 {
	if m != nil {
		return m.K
	}
	return nil
}

func (m *SketchPayload_Sketch_Dogsketch) GetN() []uint32 {
	if m != nil {
		return m.N
	}
	return nil
}

func init() {
	proto.RegisterEnum("datadog.agentpayload.MetricPayload_MetricType", MetricPayload_MetricType_name, MetricPayload_MetricType_value)
	proto.RegisterType((*CommonMetadata)(nil), "datadog.agentpayload.CommonMetadata")
	proto.RegisterType((*Origin)(nil), "datadog.agentpayload.Origin")
	proto.RegisterType((*Metadata)(nil), "datadog.agentpayload.Metadata")
	proto.RegisterType((*MetricPayload)(nil), "datadog.agentpayload.MetricPayload")
	proto.RegisterType((*MetricPayload_MetricPoint)(nil), "datadog.agentpayload.MetricPayload.MetricPoint")
	proto.RegisterType((*MetricPayload_Resource)(nil), "datadog.agentpayload.MetricPayload.Resource")
	proto.RegisterType((*MetricPayload_MetricSeries)(nil), "datadog.agentpayload.MetricPayload.MetricSeries")
	proto.RegisterType((*EventsPayload)(nil), "datadog.agentpayload.EventsPayload")
	proto.RegisterType((*EventsPayload_Event)(nil), "datadog.agentpayload.EventsPayload.Event")
	proto.RegisterType((*SketchPayload)(nil), "datadog.agentpayload.SketchPayload")
	proto.RegisterType((*SketchPayload_Sketch)(nil), "datadog.agentpayload.SketchPayload.Sketch")
	proto.RegisterType((*SketchPayload_Sketch_Distribution)(nil), "datadog.agentpayload.SketchPayload.Sketch.Distribution")
	proto.RegisterType((*SketchPayload_Sketch_Dogsketch)(nil), "datadog.agentpayload.SketchPayload.Sketch.Dogsketch")
}

func init() { proto.RegisterFile("proto/metrics/agent_payload.proto", fileDescriptor_0063f25d3d575515) }

var fileDescriptor_0063f25d3d575515 = []byte{
	// 1081 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x56, 0xdf, 0x6e, 0x1b, 0x45,
	0x17, 0xef, 0x78, 0xd7, 0x9b, 0xf5, 0x71, 0xec, 0xfa, 0x1b, 0x55, 0x1f, 0x2b, 0x53, 0xd2, 0x60,
	0x40, 0x4d, 0x11, 0xb5, 0x51, 0x28, 0xaa, 0x84, 0x84, 0xd4, 0x34, 0x71, 0x43, 0x1c, 0x9a, 0x46,
	0x9b, 0x84, 0x0b, 0x84, 0x64, 0x8d, 0xd7, 0xd3, 0xcd, 0x2a, 0xf6, 0xce, 0x6a, 0x77, 0x6c, 0xc5,
	0x3c, 0x04, 0x52, 0x1f, 0x83, 0x67, 0x80, 0x6b, 0xd4, 0x1b, 0x24, 0x9e, 0x00, 0xa1, 0x3c, 0x00,
	0xb7, 0xdc, 0xa2, 0x39, 0x33, 0xbb, 0x59, 0x57, 0x46, 0x4a, 0xb9, 0xe0, 0xee, 0x9c, 0xdf, 0xcc,
	0xf9, 0xff, 0x3b, 0xb3, 0x0b, 0xef, 0x27, 0xa9, 0x90, 0xa2, 0x37, 0xe5, 0x32, 0x8d, 0x82, 0xac,
	0xc7, 0x42, 0x1e, 0xcb, 0x61, 0xc2, 0x16, 0x13, 0xc1, 0xc6, 0x5d, 0x3c, 0xa3, 0x77, 0xc6, 0x4c,
	0xb2, 0xb1, 0x08, 0xbb, 0x78, 0x68, 0xce, 0xda, 0x0f, 0xc3, 0x48, 0x9e, 0xcf, 0x46, 0xdd, 0x40,
	0x4c, 0x7b, 0xa1, 0x08, 0x45, 0x0f, 0x2f, 0x8f, 0x66, 0x2f, 0x51, 0xd3, 0x5e, 0x95, 0xa4, 0x9d,
	0x74, 0x7e, 0x25, 0xd0, 0xdc, 0x15, 0xd3, 0xa9, 0x88, 0x9f, 0x73, 0xc9, 0x94, 0x47, 0xfa, 0x01,
	0x34, 0x74, 0xb8, 0x39, 0x4f, 0xb3, 0x48, 0xc4, 0x1e, 0xd9, 0x24, 0x5b, 0x35, 0x7f, 0x1d, 0xc1,
	0x6f, 0x34, 0x46, 0xdb, 0xe0, 0xca, 0x68, 0xca, 0xbf, 0x17, 0x31, 0xf7, 0x2a, 0x78, 0x5e, 0xe8,
	0xca, 0x41, 0x30, 0x4b, 0x53, 0xe5, 0x82, 0x27, 0x22, 0x38, 0xf7, 0xac, 0x4d, 0xb2, 0x45, 0xfc,
	0x75, 0x03, 0xf6, 0x15, 0x46, 0xef, 0x41, 0x3d, 0x8a, 0x25, 0x4f, 0x63, 0x36, 0x19, 0x46, 0x89,
	0x67, 0xa3, 0x0f, 0xc8, 0xa1, 0x83, 0x84, 0xbe, 0x0b, 0xb5, 0x64, 0x36, 0x9a, 0x44, 0x81, 0x3a,
	0xae, 0xea, 0x10, 0x1a, 0x38, 0x48, 0xe8, 0x3b, 0xb0, 0xc6, 0x92, 0x68, 0x78, 0xc1, 0x17, 0x9e,
	0x83, 0x47, 0x0e, 0x4b, 0xa2, 0x43, 0xbe, 0xe8, 0xbc, 0x22, 0xe0, 0xbc, 0x48, 0xa3, 0x30, 0x8a,
	0xe9, 0x47, 0xd0, 0x14, 0x28, 0x0d, 0x93, 0x54, 0x8c, 0x67, 0x81, 0xc4, 0x20, 0x0d, 0xbf, 0xa1,
	0xd1, 0x63, 0x0d, 0xd2, 0xfb, 0x70, 0xdb, 0x5c, 0x0b, 0x98, 0xe4, 0xa1, 0x48, 0x17, 0x18, 0xad,
	0xe1, 0x1b, 0xeb, 0x5d, 0x83, 0x96, 0xfc, 0x65, 0x3c, 0x9d, 0x47, 0x01, 0xc7, 0xd0, 0x85, 0xbf,
	0x13, 0x0d, 0x0e, 0x6c, 0x97, 0xb4, 0x2a, 0x03, 0xdb, 0xad, 0xb4, 0xac, 0x81, 0xed, 0x5a, 0x2d,
	0xbb, 0xf3, 0x04, 0xdc, 0xa2, 0xb9, 0x8f, 0xc0, 0xd1, 0xd7, 0xb1, 0xab, 0xf5, 0xed, 0xbb, 0xdd,
	0x55, 0x53, 0xec, 0xea, 0x12, 0x7c, 0x73, 0xb7, 0xf3, 0x53, 0x15, 0x1a, 0xcf, 0x91, 0x0a, 0xc7,
	0xfa, 0x02, 0xfd, 0x0a, 0x9c, 0x8c, 0xa7, 0x11, 0xcf, 0x3c, 0xb2, 0x69, 0x6d, 0xd5, 0xb7, 0x3f,
	0x5d, 0xed, 0x67, 0xc9, 0xc8, 0x68, 0x27, 0x68, 0xe7, 0x1b, 0xfb, 0xf6, 0x0e, 0xd4, 0xcd, 0x2d,
	0x11, 0xc5, 0x92, 0xde, 0x81, 0xea, 0x9c, 0x4d, 0x66, 0x1c, 0xf3, 0x23, 0xbe, 0x56, 0xe8, 0x5d,
	0xa8, 0xa9, 0xf1, 0x66, 0x92, 0x4d, 0x13, 0x9c, 0xb7, 0xe5, 0x5f, 0x03, 0xed, 0x6d, 0x70, 0x7d,
	0x9e, 0x89, 0x59, 0x1a, 0x70, 0x4a, 0xc1, 0x96, 0x8b, 0x84, 0x1b, 0xd2, 0xa0, 0xac, 0xb0, 0x98,
	0x4d, 0x73, 0xa2, 0xa0, 0xdc, 0xfe, 0xd1, 0x82, 0xf5, 0x72, 0x3e, 0x74, 0x00, 0xb5, 0xd4, 0x38,
	0xc9, 0x8b, 0xfa, 0xe4, 0x26, 0x45, 0xe5, 0x91, 0xfd, 0x6b, 0x73, 0xfa, 0x7f, 0x70, 0xf4, 0xe6,
	0x98, 0x90, 0x46, 0xc3, 0xe4, 0x58, 0x98, 0x79, 0xd6, 0xa6, 0x85, 0xc9, 0xb1, 0x30, 0xa3, 0xfb,
	0xe0, 0x24, 0xaa, 0xf2, 0xcc, 0xb3, 0x31, 0x68, 0xef, 0xe6, 0x9d, 0xc4, 0x8e, 0xf9, 0xc6, 0x9c,
	0x3e, 0x35, 0x95, 0x2b, 0xf6, 0x34, 0xb7, 0xbb, 0x37, 0x77, 0x73, 0xba, 0x48, 0xf8, 0x75, 0xa7,
	0x66, 0x71, 0x24, 0x0d, 0xa9, 0x51, 0xa6, 0x5b, 0xd0, 0xd2, 0x75, 0x0d, 0xd5, 0x95, 0x21, 0x76,
	0x72, 0x0d, 0xcf, 0x9b, 0x1a, 0x57, 0xf6, 0x47, 0x6c, 0xca, 0xd5, 0x52, 0xe2, 0x02, 0xcd, 0xd9,
	0xc4, 0x73, 0x71, 0x48, 0x85, 0x4e, 0xbf, 0x00, 0x77, 0x6a, 0x48, 0xe8, 0xd5, 0x90, 0x7a, 0x1b,
	0xff, 0x98, 0x21, 0xde, 0xf2, 0x8b, 0xfb, 0x9d, 0x2f, 0x01, 0xae, 0x33, 0xa5, 0xb7, 0xa1, 0x7e,
	0x76, 0x74, 0x72, 0xdc, 0xdf, 0x3d, 0x78, 0x76, 0xd0, 0xdf, 0x6b, 0xdd, 0xa2, 0x35, 0xa8, 0xee,
	0xbe, 0x38, 0x3b, 0x3a, 0x6d, 0x11, 0xea, 0x82, 0xed, 0xef, 0x9c, 0xf6, 0x5b, 0x15, 0x05, 0xee,
	0xef, 0x9c, 0xed, 0xf7, 0x5b, 0x56, 0xe7, 0x07, 0x0b, 0x1a, 0xfd, 0x39, 0x8f, 0x65, 0x96, 0xb3,
	0x77, 0x07, 0x1c, 0x8e, 0x80, 0x19, 0xf4, 0x83, 0xd5, 0xa9, 0x2c, 0x19, 0x69, 0xcd, 0x37, 0x86,
	0xf4, 0x49, 0xa9, 0x9e, 0x0a, 0xd6, 0xf3, 0xe1, 0x6a, 0x27, 0xcb, 0xaf, 0xdb, 0x75, 0x55, 0xed,
	0xbf, 0x08, 0x54, 0xd1, 0xa7, 0xe2, 0xbc, 0x8c, 0xe4, 0x24, 0x27, 0xad, 0x56, 0x90, 0x2c, 0xfc,
	0x52, 0xe6, 0xac, 0x55, 0x32, 0x6d, 0x42, 0x45, 0x66, 0xf8, 0x9e, 0x59, 0x7e, 0x45, 0x66, 0xaa,
	0xe3, 0x49, 0x1a, 0x89, 0x34, 0x92, 0x0b, 0xf3, 0x84, 0x15, 0xba, 0xb2, 0x3f, 0x17, 0x99, 0x34,
	0x6f, 0x17, 0xca, 0x05, 0x01, 0x9d, 0x12, 0x01, 0xdf, 0x03, 0x60, 0x13, 0x9e, 0x4a, 0x1c, 0xaf,
	0x99, 0x6c, 0x0d, 0x11, 0x6c, 0xf7, 0x7d, 0xb8, 0xcd, 0xc2, 0x30, 0xe5, 0x21, 0x93, 0x91, 0x88,
	0xf1, 0xc9, 0x73, 0xf5, 0xf4, 0x4b, 0xf0, 0x21, 0x5f, 0xac, 0xe4, 0x49, 0x6d, 0x15, 0x4f, 0x3a,
	0x7f, 0x3a, 0xd0, 0x38, 0xb9, 0xe0, 0x32, 0x38, 0xcf, 0x07, 0xf2, 0x35, 0xb8, 0x19, 0x02, 0xc5,
	0xee, 0x7d, 0xbc, 0xba, 0x9b, 0x4b, 0x66, 0x46, 0x7b, 0x6a, 0xbf, 0xfe, 0xfd, 0xde, 0x2d, 0xbf,
	0xf0, 0x40, 0x9f, 0xfd, 0xbb, 0xd9, 0xe4, 0x7e, 0x8a, 0x09, 0xfd, 0x52, 0x05, 0x47, 0x87, 0x28,
	0x6d, 0x34, 0x79, 0x73, 0xa3, 0xb1, 0xc9, 0x95, 0x52, 0x93, 0x03, 0x68, 0x8c, 0xa3, 0x4c, 0xa6,
	0xd1, 0x68, 0xa6, 0x7a, 0xa3, 0xd7, 0xbd, 0xbe, 0xfd, 0xf8, 0xe6, 0x15, 0x75, 0xf7, 0x4a, 0xf6,
	0x26, 0xad, 0x65, 0x9f, 0xc5, 0x24, 0xed, 0xd2, 0x24, 0xbf, 0x83, 0xfa, 0x58, 0x84, 0x45, 0x23,
	0xd7, 0x30, 0xec, 0xa3, 0xb7, 0x09, 0x9b, 0x5b, 0x9b, 0x98, 0x65, 0x77, 0x4b, 0x1b, 0xec, 0xbe,
	0xdd, 0x06, 0xb7, 0x7f, 0x26, 0xb0, 0x5e, 0xae, 0xc9, 0x10, 0x99, 0x14, 0x44, 0x6e, 0x81, 0x15,
	0xc4, 0xd2, 0x3c, 0xed, 0x4a, 0x54, 0xc8, 0x34, 0x8a, 0xcd, 0xb7, 0x5b, 0x89, 0x88, 0xb0, 0x4b,
	0xe4, 0xb9, 0x42, 0xd8, 0xa5, 0x42, 0xd8, 0x3c, 0x44, 0x86, 0x13, 0x5f, 0x89, 0x0a, 0xc9, 0x66,
	0x53, 0x7c, 0xbf, 0x88, 0xaf, 0x44, 0xba, 0x0e, 0x64, 0x8e, 0xad, 0x20, 0x3e, 0x99, 0x2b, 0x2d,
	0xf4, 0xdc, 0x4d, 0x6b, 0xab, 0xe1, 0x93, 0x50, 0x2d, 0xde, 0x98, 0x4f, 0xf0, 0x45, 0x52, 0x88,
	0x56, 0x94, 0x8f, 0xd1, 0xec, 0xa5, 0x07, 0x68, 0xa3, 0xc4, 0xf6, 0x2b, 0x02, 0xb5, 0xa2, 0x37,
	0xff, 0x6d, 0xee, 0x17, 0x98, 0xfb, 0xff, 0x7c, 0x72, 0xa1, 0xb4, 0x38, 0xcf, 0x3d, 0x1e, 0xd8,
	0x6e, 0xb5, 0xe5, 0x0c, 0x6c, 0xd7, 0x69, 0xad, 0xf9, 0xcd, 0x25, 0x6e, 0x1c, 0xbe, 0xa1, 0xef,
	0x3e, 0x7d, 0xfc, 0xfa, 0x6a, 0x83, 0xfc, 0x76, 0xb5, 0x41, 0xfe, 0xb8, 0xda, 0x20, 0xdf, 0x3e,
	0x28, 0xfd, 0xa2, 0xed, 0x31, 0xc9, 0xf6, 0x44, 0xa8, 0x7f, 0xf0, 0x1e, 0x9a, 0x09, 0xf6, 0xe6,
	0x9f, 0xab, 0x7f, 0x34, 0x1e, 0x8f, 0x1c, 0xfc, 0x4b, 0xfb, 0xec, 0xef, 0x00, 0x00, 0x00, 0xff,
	0xff, 0x10, 0x3f, 0x56, 0xf3, 0x0f, 0x0a, 0x00, 0x00,
}

func (m *CommonMetadata) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommonMetadata) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CommonMetadata) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.ApiKey) > 0 {
		i -= len(m.ApiKey)
		copy(dAtA[i:], m.ApiKey)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.ApiKey)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.PublicIp) > 0 {
		i -= len(m.PublicIp)
		copy(dAtA[i:], m.PublicIp)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.PublicIp)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.InternalIp) > 0 {
		i -= len(m.InternalIp)
		copy(dAtA[i:], m.InternalIp)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.InternalIp)))
		i--
		dAtA[i] = 0x22
	}
	if m.CurrentEpoch != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CurrentEpoch))))
		i--
		dAtA[i] = 0x19
	}
	if len(m.Timezone) > 0 {
		i -= len(m.Timezone)
		copy(dAtA[i:], m.Timezone)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.Timezone)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.AgentVersion) > 0 {
		i -= len(m.AgentVersion)
		copy(dAtA[i:], m.AgentVersion)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.AgentVersion)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Origin) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Origin) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Origin) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.OriginService != 0 {
		i = encodeVarintAgentPayload(dAtA, i, uint64(m.OriginService))
		i--
		dAtA[i] = 0x30
	}
	if m.OriginCategory != 0 {
		i = encodeVarintAgentPayload(dAtA, i, uint64(m.OriginCategory))
		i--
		dAtA[i] = 0x28
	}
	if m.OriginProduct != 0 {
		i = encodeVarintAgentPayload(dAtA, i, uint64(m.OriginProduct))
		i--
		dAtA[i] = 0x20
	}
	return len(dAtA) - i, nil
}

func (m *Metadata) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Metadata) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Metadata) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Origin != nil {
		{
			size, err := m.Origin.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintAgentPayload(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *MetricPayload) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MetricPayload) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricPayload) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Series) > 0 {
		for iNdEx := len(m.Series) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Series[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintAgentPayload(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *MetricPayload_MetricPoint) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MetricPayload_MetricPoint) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricPayload_MetricPoint) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Timestamp != 0 {
		i = encodeVarintAgentPayload(dAtA, i, uint64(m.Timestamp))
		i--
		dAtA[i] = 0x10
	}
	if m.Value != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Value))))
		i--
		dAtA[i] = 0x9
	}
	return len(dAtA) - i, nil
}

func (m *MetricPayload_Resource) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MetricPayload_Resource) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricPayload_Resource) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Type) > 0 {
		i -= len(m.Type)
		copy(dAtA[i:], m.Type)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.Type)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *MetricPayload_MetricSeries) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MetricPayload_MetricSeries) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricPayload_MetricSeries) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Metadata != nil {
		{
			size, err := m.Metadata.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintAgentPayload(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	if m.Interval != 0 {
		i = encodeVarintAgentPayload(dAtA, i, uint64(m.Interval))
		i--
		dAtA[i] = 0x40
	}
	if len(m.SourceTypeName) > 0 {
		i -= len(m.SourceTypeName)
		copy(dAtA[i:], m.SourceTypeName)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.SourceTypeName)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.Unit) > 0 {
		i -= len(m.Unit)
		copy(dAtA[i:], m.Unit)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.Unit)))
		i--
		dAtA[i] = 0x32
	}
	if m.Type != 0 {
		i = encodeVarintAgentPayload(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x28
	}
	if len(m.Points) > 0 {
		for iNdEx := len(m.Points) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Points[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintAgentPayload(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.Tags) > 0 {
		for iNdEx := len(m.Tags) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Tags[iNdEx])
			copy(dAtA[i:], m.Tags[iNdEx])
			i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.Tags[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.Metric) > 0 {
		i -= len(m.Metric)
		copy(dAtA[i:], m.Metric)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.Metric)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Resources) > 0 {
		for iNdEx := len(m.Resources) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Resources[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintAgentPayload(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *EventsPayload) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EventsPayload) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EventsPayload) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Metadata != nil {
		{
			size, err := m.Metadata.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintAgentPayload(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.Events) > 0 {
		for iNdEx := len(m.Events) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Events[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintAgentPayload(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *EventsPayload_Event) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EventsPayload_Event) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EventsPayload_Event) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.SourceTypeName) > 0 {
		i -= len(m.SourceTypeName)
		copy(dAtA[i:], m.SourceTypeName)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.SourceTypeName)))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.AggregationKey) > 0 {
		i -= len(m.AggregationKey)
		copy(dAtA[i:], m.AggregationKey)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.AggregationKey)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.AlertType) > 0 {
		i -= len(m.AlertType)
		copy(dAtA[i:], m.AlertType)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.AlertType)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.Tags) > 0 {
		for iNdEx := len(m.Tags) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Tags[iNdEx])
			copy(dAtA[i:], m.Tags[iNdEx])
			i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.Tags[iNdEx])))
			i--
			dAtA[i] = 0x32
		}
	}
	if len(m.Host) > 0 {
		i -= len(m.Host)
		copy(dAtA[i:], m.Host)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.Host)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.Priority) > 0 {
		i -= len(m.Priority)
		copy(dAtA[i:], m.Priority)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.Priority)))
		i--
		dAtA[i] = 0x22
	}
	if m.Ts != 0 {
		i = encodeVarintAgentPayload(dAtA, i, uint64(m.Ts))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Text) > 0 {
		i -= len(m.Text)
		copy(dAtA[i:], m.Text)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.Text)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Title) > 0 {
		i -= len(m.Title)
		copy(dAtA[i:], m.Title)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.Title)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SketchPayload) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SketchPayload) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SketchPayload) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	{
		size, err := m.Metadata.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintAgentPayload(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	if len(m.Sketches) > 0 {
		for iNdEx := len(m.Sketches) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Sketches[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintAgentPayload(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *SketchPayload_Sketch) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SketchPayload_Sketch) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SketchPayload_Sketch) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Metadata != nil {
		{
			size, err := m.Metadata.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintAgentPayload(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	if len(m.Dogsketches) > 0 {
		for iNdEx := len(m.Dogsketches) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Dogsketches[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintAgentPayload(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x3a
		}
	}
	if len(m.Tags) > 0 {
		for iNdEx := len(m.Tags) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Tags[iNdEx])
			copy(dAtA[i:], m.Tags[iNdEx])
			i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.Tags[iNdEx])))
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.Distributions) > 0 {
		for iNdEx := len(m.Distributions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Distributions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintAgentPayload(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.Host) > 0 {
		i -= len(m.Host)
		copy(dAtA[i:], m.Host)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.Host)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Metric) > 0 {
		i -= len(m.Metric)
		copy(dAtA[i:], m.Metric)
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.Metric)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SketchPayload_Sketch_Distribution) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SketchPayload_Sketch_Distribution) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SketchPayload_Sketch_Distribution) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Buf) > 0 {
		for iNdEx := len(m.Buf) - 1; iNdEx >= 0; iNdEx-- {
			f6 := math.Float64bits(float64(m.Buf[iNdEx]))
			i -= 8
			encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(f6))
		}
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.Buf)*8))
		i--
		dAtA[i] = 0x52
	}
	if len(m.Delta) > 0 {
		dAtA8 := make([]byte, len(m.Delta)*10)
		var j7 int
		for _, num := range m.Delta {
			for num >= 1<<7 {
				dAtA8[j7] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j7++
			}
			dAtA8[j7] = uint8(num)
			j7++
		}
		i -= j7
		copy(dAtA[i:], dAtA8[:j7])
		i = encodeVarintAgentPayload(dAtA, i, uint64(j7))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.G) > 0 {
		dAtA10 := make([]byte, len(m.G)*10)
		var j9 int
		for _, num := range m.G {
			for num >= 1<<7 {
				dAtA10[j9] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j9++
			}
			dAtA10[j9] = uint8(num)
			j9++
		}
		i -= j9
		copy(dAtA[i:], dAtA10[:j9])
		i = encodeVarintAgentPayload(dAtA, i, uint64(j9))
		i--
		dAtA[i] = 0x42
	}
	if len(m.V) > 0 {
		for iNdEx := len(m.V) - 1; iNdEx >= 0; iNdEx-- {
			f11 := math.Float64bits(float64(m.V[iNdEx]))
			i -= 8
			encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(f11))
		}
		i = encodeVarintAgentPayload(dAtA, i, uint64(len(m.V)*8))
		i--
		dAtA[i] = 0x3a
	}
	if m.Sum != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Sum))))
		i--
		dAtA[i] = 0x31
	}
	if m.Avg != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Avg))))
		i--
		dAtA[i] = 0x29
	}
	if m.Max != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Max))))
		i--
		dAtA[i] = 0x21
	}
	if m.Min != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Min))))
		i--
		dAtA[i] = 0x19
	}
	if m.Cnt != 0 {
		i = encodeVarintAgentPayload(dAtA, i, uint64(m.Cnt))
		i--
		dAtA[i] = 0x10
	}
	if m.Ts != 0 {
		i = encodeVarintAgentPayload(dAtA, i, uint64(m.Ts))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *SketchPayload_Sketch_Dogsketch) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SketchPayload_Sketch_Dogsketch) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SketchPayload_Sketch_Dogsketch) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.N) > 0 {
		dAtA13 := make([]byte, len(m.N)*10)
		var j12 int
		for _, num := range m.N {
			for num >= 1<<7 {
				dAtA13[j12] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j12++
			}
			dAtA13[j12] = uint8(num)
			j12++
		}
		i -= j12
		copy(dAtA[i:], dAtA13[:j12])
		i = encodeVarintAgentPayload(dAtA, i, uint64(j12))
		i--
		dAtA[i] = 0x42
	}
	if len(m.K) > 0 {
		dAtA14 := make([]byte, len(m.K)*5)
		var j15 int
		for _, num := range m.K {
			x16 := (uint32(num) << 1) ^ uint32((num >> 31))
			for x16 >= 1<<7 {
				dAtA14[j15] = uint8(uint64(x16)&0x7f | 0x80)
				j15++
				x16 >>= 7
			}
			dAtA14[j15] = uint8(x16)
			j15++
		}
		i -= j15
		copy(dAtA[i:], dAtA14[:j15])
		i = encodeVarintAgentPayload(dAtA, i, uint64(j15))
		i--
		dAtA[i] = 0x3a
	}
	if m.Sum != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Sum))))
		i--
		dAtA[i] = 0x31
	}
	if m.Avg != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Avg))))
		i--
		dAtA[i] = 0x29
	}
	if m.Max != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Max))))
		i--
		dAtA[i] = 0x21
	}
	if m.Min != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Min))))
		i--
		dAtA[i] = 0x19
	}
	if m.Cnt != 0 {
		i = encodeVarintAgentPayload(dAtA, i, uint64(m.Cnt))
		i--
		dAtA[i] = 0x10
	}
	if m.Ts != 0 {
		i = encodeVarintAgentPayload(dAtA, i, uint64(m.Ts))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintAgentPayload(dAtA []byte, offset int, v uint64) int {
	offset -= sovAgentPayload(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *CommonMetadata) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.AgentVersion)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	l = len(m.Timezone)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	if m.CurrentEpoch != 0 {
		n += 9
	}
	l = len(m.InternalIp)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	l = len(m.PublicIp)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	l = len(m.ApiKey)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Origin) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.OriginProduct != 0 {
		n += 1 + sovAgentPayload(uint64(m.OriginProduct))
	}
	if m.OriginCategory != 0 {
		n += 1 + sovAgentPayload(uint64(m.OriginCategory))
	}
	if m.OriginService != 0 {
		n += 1 + sovAgentPayload(uint64(m.OriginService))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Metadata) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Origin != nil {
		l = m.Origin.Size()
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MetricPayload) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Series) > 0 {
		for _, e := range m.Series {
			l = e.Size()
			n += 1 + l + sovAgentPayload(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MetricPayload_MetricPoint) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Value != 0 {
		n += 9
	}
	if m.Timestamp != 0 {
		n += 1 + sovAgentPayload(uint64(m.Timestamp))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MetricPayload_Resource) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Type)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MetricPayload_MetricSeries) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Resources) > 0 {
		for _, e := range m.Resources {
			l = e.Size()
			n += 1 + l + sovAgentPayload(uint64(l))
		}
	}
	l = len(m.Metric)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	if len(m.Tags) > 0 {
		for _, s := range m.Tags {
			l = len(s)
			n += 1 + l + sovAgentPayload(uint64(l))
		}
	}
	if len(m.Points) > 0 {
		for _, e := range m.Points {
			l = e.Size()
			n += 1 + l + sovAgentPayload(uint64(l))
		}
	}
	if m.Type != 0 {
		n += 1 + sovAgentPayload(uint64(m.Type))
	}
	l = len(m.Unit)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	l = len(m.SourceTypeName)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	if m.Interval != 0 {
		n += 1 + sovAgentPayload(uint64(m.Interval))
	}
	if m.Metadata != nil {
		l = m.Metadata.Size()
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *EventsPayload) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Events) > 0 {
		for _, e := range m.Events {
			l = e.Size()
			n += 1 + l + sovAgentPayload(uint64(l))
		}
	}
	if m.Metadata != nil {
		l = m.Metadata.Size()
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *EventsPayload_Event) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Title)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	l = len(m.Text)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	if m.Ts != 0 {
		n += 1 + sovAgentPayload(uint64(m.Ts))
	}
	l = len(m.Priority)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	l = len(m.Host)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	if len(m.Tags) > 0 {
		for _, s := range m.Tags {
			l = len(s)
			n += 1 + l + sovAgentPayload(uint64(l))
		}
	}
	l = len(m.AlertType)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	l = len(m.AggregationKey)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	l = len(m.SourceTypeName)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SketchPayload) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Sketches) > 0 {
		for _, e := range m.Sketches {
			l = e.Size()
			n += 1 + l + sovAgentPayload(uint64(l))
		}
	}
	l = m.Metadata.Size()
	n += 1 + l + sovAgentPayload(uint64(l))
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SketchPayload_Sketch) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Metric)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	l = len(m.Host)
	if l > 0 {
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	if len(m.Distributions) > 0 {
		for _, e := range m.Distributions {
			l = e.Size()
			n += 1 + l + sovAgentPayload(uint64(l))
		}
	}
	if len(m.Tags) > 0 {
		for _, s := range m.Tags {
			l = len(s)
			n += 1 + l + sovAgentPayload(uint64(l))
		}
	}
	if len(m.Dogsketches) > 0 {
		for _, e := range m.Dogsketches {
			l = e.Size()
			n += 1 + l + sovAgentPayload(uint64(l))
		}
	}
	if m.Metadata != nil {
		l = m.Metadata.Size()
		n += 1 + l + sovAgentPayload(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SketchPayload_Sketch_Distribution) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Ts != 0 {
		n += 1 + sovAgentPayload(uint64(m.Ts))
	}
	if m.Cnt != 0 {
		n += 1 + sovAgentPayload(uint64(m.Cnt))
	}
	if m.Min != 0 {
		n += 9
	}
	if m.Max != 0 {
		n += 9
	}
	if m.Avg != 0 {
		n += 9
	}
	if m.Sum != 0 {
		n += 9
	}
	if len(m.V) > 0 {
		n += 1 + sovAgentPayload(uint64(len(m.V)*8)) + len(m.V)*8
	}
	if len(m.G) > 0 {
		l = 0
		for _, e := range m.G {
			l += sovAgentPayload(uint64(e))
		}
		n += 1 + sovAgentPayload(uint64(l)) + l
	}
	if len(m.Delta) > 0 {
		l = 0
		for _, e := range m.Delta {
			l += sovAgentPayload(uint64(e))
		}
		n += 1 + sovAgentPayload(uint64(l)) + l
	}
	if len(m.Buf) > 0 {
		n += 1 + sovAgentPayload(uint64(len(m.Buf)*8)) + len(m.Buf)*8
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SketchPayload_Sketch_Dogsketch) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Ts != 0 {
		n += 1 + sovAgentPayload(uint64(m.Ts))
	}
	if m.Cnt != 0 {
		n += 1 + sovAgentPayload(uint64(m.Cnt))
	}
	if m.Min != 0 {
		n += 9
	}
	if m.Max != 0 {
		n += 9
	}
	if m.Avg != 0 {
		n += 9
	}
	if m.Sum != 0 {
		n += 9
	}
	if len(m.K) > 0 {
		l = 0
		for _, e := range m.K {
			l += sozAgentPayload(uint64(e))
		}
		n += 1 + sovAgentPayload(uint64(l)) + l
	}
	if len(m.N) > 0 {
		l = 0
		for _, e := range m.N {
			l += sovAgentPayload(uint64(e))
		}
		n += 1 + sovAgentPayload(uint64(l)) + l
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovAgentPayload(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozAgentPayload(x uint64) (n int) {
	return sovAgentPayload(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *CommonMetadata) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAgentPayload
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CommonMetadata: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CommonMetadata: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AgentVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AgentVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timezone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Timezone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CurrentEpoch", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CurrentEpoch = float64(math.Float64frombits(v))
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InternalIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InternalIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PublicIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PublicIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApiKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ApiKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAgentPayload(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Origin) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAgentPayload
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Origin: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Origin: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OriginProduct", wireType)
			}
			m.OriginProduct = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OriginProduct |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OriginCategory", wireType)
			}
			m.OriginCategory = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OriginCategory |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OriginService", wireType)
			}
			m.OriginService = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OriginService |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAgentPayload(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Metadata) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAgentPayload
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Metadata: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Metadata: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Origin", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Origin == nil {
				m.Origin = &Origin{}
			}
			if err := m.Origin.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAgentPayload(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MetricPayload) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAgentPayload
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MetricPayload: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MetricPayload: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Series", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Series = append(m.Series, &MetricPayload_MetricSeries{})
			if err := m.Series[len(m.Series)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAgentPayload(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MetricPayload_MetricPoint) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAgentPayload
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MetricPoint: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MetricPoint: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Value = float64(math.Float64frombits(v))
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAgentPayload(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MetricPayload_Resource) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAgentPayload
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Resource: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Resource: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAgentPayload(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MetricPayload_MetricSeries) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAgentPayload
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MetricSeries: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MetricSeries: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Resources", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Resources = append(m.Resources, &MetricPayload_Resource{})
			if err := m.Resources[len(m.Resources)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Metric", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Metric = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tags", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tags = append(m.Tags, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Points", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Points = append(m.Points, &MetricPayload_MetricPoint{})
			if err := m.Points[len(m.Points)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= MetricPayload_MetricType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Unit", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Unit = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceTypeName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceTypeName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Interval", wireType)
			}
			m.Interval = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Interval |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Metadata", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Metadata == nil {
				m.Metadata = &Metadata{}
			}
			if err := m.Metadata.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAgentPayload(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EventsPayload) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAgentPayload
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EventsPayload: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EventsPayload: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Events", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Events = append(m.Events, &EventsPayload_Event{})
			if err := m.Events[len(m.Events)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Metadata", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Metadata == nil {
				m.Metadata = &CommonMetadata{}
			}
			if err := m.Metadata.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAgentPayload(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EventsPayload_Event) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAgentPayload
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Event: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Event: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Text", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Text = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ts", wireType)
			}
			m.Ts = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ts |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Priority", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Priority = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Host", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Host = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tags", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tags = append(m.Tags, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AlertType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AlertType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AggregationKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AggregationKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceTypeName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceTypeName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAgentPayload(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SketchPayload) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAgentPayload
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SketchPayload: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SketchPayload: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Sketches", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Sketches = append(m.Sketches, SketchPayload_Sketch{})
			if err := m.Sketches[len(m.Sketches)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Metadata", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Metadata.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAgentPayload(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SketchPayload_Sketch) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAgentPayload
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Sketch: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Sketch: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Metric", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Metric = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Host", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Host = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Distributions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Distributions = append(m.Distributions, SketchPayload_Sketch_Distribution{})
			if err := m.Distributions[len(m.Distributions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tags", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tags = append(m.Tags, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dogsketches", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Dogsketches = append(m.Dogsketches, SketchPayload_Sketch_Dogsketch{})
			if err := m.Dogsketches[len(m.Dogsketches)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Metadata", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAgentPayload
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Metadata == nil {
				m.Metadata = &Metadata{}
			}
			if err := m.Metadata.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAgentPayload(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SketchPayload_Sketch_Distribution) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAgentPayload
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Distribution: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Distribution: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ts", wireType)
			}
			m.Ts = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ts |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cnt", wireType)
			}
			m.Cnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cnt |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Min", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Min = float64(math.Float64frombits(v))
		case 4:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Max", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Max = float64(math.Float64frombits(v))
		case 5:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Avg", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Avg = float64(math.Float64frombits(v))
		case 6:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Sum", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Sum = float64(math.Float64frombits(v))
		case 7:
			if wireType == 1 {
				var v uint64
				if (iNdEx + 8) > l {
					return io.ErrUnexpectedEOF
				}
				v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
				iNdEx += 8
				v2 := float64(math.Float64frombits(v))
				m.V = append(m.V, v2)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAgentPayload
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAgentPayload
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthAgentPayload
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen / 8
				if elementCount != 0 && len(m.V) == 0 {
					m.V = make([]float64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					if (iNdEx + 8) > l {
						return io.ErrUnexpectedEOF
					}
					v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
					iNdEx += 8
					v2 := float64(math.Float64frombits(v))
					m.V = append(m.V, v2)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field V", wireType)
			}
		case 8:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAgentPayload
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.G = append(m.G, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAgentPayload
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAgentPayload
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthAgentPayload
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.G) == 0 {
					m.G = make([]uint32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAgentPayload
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.G = append(m.G, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field G", wireType)
			}
		case 9:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAgentPayload
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Delta = append(m.Delta, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAgentPayload
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAgentPayload
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthAgentPayload
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.Delta) == 0 {
					m.Delta = make([]uint32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAgentPayload
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Delta = append(m.Delta, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Delta", wireType)
			}
		case 10:
			if wireType == 1 {
				var v uint64
				if (iNdEx + 8) > l {
					return io.ErrUnexpectedEOF
				}
				v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
				iNdEx += 8
				v2 := float64(math.Float64frombits(v))
				m.Buf = append(m.Buf, v2)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAgentPayload
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAgentPayload
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthAgentPayload
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen / 8
				if elementCount != 0 && len(m.Buf) == 0 {
					m.Buf = make([]float64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					if (iNdEx + 8) > l {
						return io.ErrUnexpectedEOF
					}
					v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
					iNdEx += 8
					v2 := float64(math.Float64frombits(v))
					m.Buf = append(m.Buf, v2)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Buf", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAgentPayload(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SketchPayload_Sketch_Dogsketch) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAgentPayload
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Dogsketch: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Dogsketch: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ts", wireType)
			}
			m.Ts = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ts |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cnt", wireType)
			}
			m.Cnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cnt |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Min", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Min = float64(math.Float64frombits(v))
		case 4:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Max", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Max = float64(math.Float64frombits(v))
		case 5:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Avg", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Avg = float64(math.Float64frombits(v))
		case 6:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Sum", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Sum = float64(math.Float64frombits(v))
		case 7:
			if wireType == 0 {
				var v int32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAgentPayload
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
				m.K = append(m.K, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAgentPayload
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAgentPayload
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthAgentPayload
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.K) == 0 {
					m.K = make([]int32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAgentPayload
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
					m.K = append(m.K, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field K", wireType)
			}
		case 8:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAgentPayload
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.N = append(m.N, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAgentPayload
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAgentPayload
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthAgentPayload
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.N) == 0 {
					m.N = make([]uint32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAgentPayload
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.N = append(m.N, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field N", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAgentPayload(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAgentPayload
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipAgentPayload(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAgentPayload
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAgentPayload
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthAgentPayload
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupAgentPayload
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthAgentPayload
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthAgentPayload        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAgentPayload          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupAgentPayload = fmt.Errorf("proto: unexpected end of group")
)
