// Code generated by "stringer -type=AuthorizeType"; DO NOT EDIT.

package authority

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[ATUnknown-0]
	_ = x[ATUsernamePassword-1]
	_ = x[ATWindowsIntegrated-2]
	_ = x[ATAuthCode-3]
	_ = x[ATInteractive-4]
	_ = x[ATClientCredentials-5]
	_ = x[ATDeviceCode-6]
	_ = x[ATRefreshToken-7]
}

const _AuthorizeType_name = "ATUnknownATUsernamePasswordATWindowsIntegratedATAuthCodeATInteractiveATClientCredentialsATDeviceCodeATRefreshToken"

var _AuthorizeType_index = [...]uint8{0, 9, 27, 46, 56, 69, 88, 100, 114}

func (i AuthorizeType) String() string {
	if i < 0 || i >= AuthorizeType(len(_AuthorizeType_index)-1) {
		return "AuthorizeType(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _AuthorizeType_name[_AuthorizeType_index[i]:_AuthorizeType_index[i+1]]
}
