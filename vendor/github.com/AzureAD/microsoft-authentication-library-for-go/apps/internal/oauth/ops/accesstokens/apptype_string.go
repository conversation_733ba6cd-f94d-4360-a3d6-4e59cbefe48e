// Code generated by "stringer -type=AppType"; DO NOT EDIT.

package accesstokens

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[ATUnknown-0]
	_ = x[ATPublic-1]
	_ = x[ATConfidential-2]
}

const _AppType_name = "ATUnknownATPublicATConfidential"

var _AppType_index = [...]uint8{0, 9, 17, 31}

func (i AppType) String() string {
	if i < 0 || i >= AppType(len(_AppType_index)-1) {
		return "AppType(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _AppType_name[_AppType_index[i]:_AppType_index[i+1]]
}
