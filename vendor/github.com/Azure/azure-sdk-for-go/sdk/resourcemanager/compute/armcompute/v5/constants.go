//go:build go1.18
// +build go1.18

// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License. See License.txt in the project root for license information.
// Code generated by Microsoft (R) AutoRest Code Generator. DO NOT EDIT.
// Changes may cause incorrect behavior and will be lost if the code is regenerated.

package armcompute

const (
	moduleName    = "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/compute/armcompute"
	moduleVersion = "v5.7.0"
)

type AccessLevel string

const (
	AccessLevelNone  AccessLevel = "None"
	AccessLevelRead  AccessLevel = "Read"
	AccessLevelWrite AccessLevel = "Write"
)

// PossibleAccessLevelValues returns the possible values for the AccessLevel const type.
func PossibleAccessLevelValues() []AccessLevel {
	return []AccessLevel{
		AccessLevelNone,
		AccessLevelRead,
		AccessLevelWrite,
	}
}

// AggregatedReplicationState - This is the aggregated replication status based on all the regional replication status flags.
type AggregatedReplicationState string

const (
	AggregatedReplicationStateCompleted  AggregatedReplicationState = "Completed"
	AggregatedReplicationStateFailed     AggregatedReplicationState = "Failed"
	AggregatedReplicationStateInProgress AggregatedReplicationState = "InProgress"
	AggregatedReplicationStateUnknown    AggregatedReplicationState = "Unknown"
)

// PossibleAggregatedReplicationStateValues returns the possible values for the AggregatedReplicationState const type.
func PossibleAggregatedReplicationStateValues() []AggregatedReplicationState {
	return []AggregatedReplicationState{
		AggregatedReplicationStateCompleted,
		AggregatedReplicationStateFailed,
		AggregatedReplicationStateInProgress,
		AggregatedReplicationStateUnknown,
	}
}

// AlternativeType - Describes the type of the alternative option.
type AlternativeType string

const (
	AlternativeTypeNone  AlternativeType = "None"
	AlternativeTypeOffer AlternativeType = "Offer"
	AlternativeTypePlan  AlternativeType = "Plan"
)

// PossibleAlternativeTypeValues returns the possible values for the AlternativeType const type.
func PossibleAlternativeTypeValues() []AlternativeType {
	return []AlternativeType{
		AlternativeTypeNone,
		AlternativeTypeOffer,
		AlternativeTypePlan,
	}
}

// Architecture - The architecture of the image. Applicable to OS disks only.
type Architecture string

const (
	ArchitectureArm64 Architecture = "Arm64"
	ArchitectureX64   Architecture = "x64"
)

// PossibleArchitectureValues returns the possible values for the Architecture const type.
func PossibleArchitectureValues() []Architecture {
	return []Architecture{
		ArchitectureArm64,
		ArchitectureX64,
	}
}

// ArchitectureTypes - Specifies the Architecture Type
type ArchitectureTypes string

const (
	ArchitectureTypesArm64 ArchitectureTypes = "Arm64"
	ArchitectureTypesX64   ArchitectureTypes = "x64"
)

// PossibleArchitectureTypesValues returns the possible values for the ArchitectureTypes const type.
func PossibleArchitectureTypesValues() []ArchitectureTypes {
	return []ArchitectureTypes{
		ArchitectureTypesArm64,
		ArchitectureTypesX64,
	}
}

// AvailabilitySetSKUTypes - Specifies the sku of an Availability Set. Use 'Aligned' for virtual machines with managed disks
// and 'Classic' for virtual machines with unmanaged disks. Default value is 'Classic'.
type AvailabilitySetSKUTypes string

const (
	AvailabilitySetSKUTypesAligned AvailabilitySetSKUTypes = "Aligned"
	AvailabilitySetSKUTypesClassic AvailabilitySetSKUTypes = "Classic"
)

// PossibleAvailabilitySetSKUTypesValues returns the possible values for the AvailabilitySetSKUTypes const type.
func PossibleAvailabilitySetSKUTypesValues() []AvailabilitySetSKUTypes {
	return []AvailabilitySetSKUTypes{
		AvailabilitySetSKUTypesAligned,
		AvailabilitySetSKUTypesClassic,
	}
}

// CachingTypes - Specifies the caching requirements. Possible values are: None, ReadOnly, ReadWrite. The default values are:
// None for Standard storage. ReadOnly for Premium storage
type CachingTypes string

const (
	CachingTypesNone      CachingTypes = "None"
	CachingTypesReadOnly  CachingTypes = "ReadOnly"
	CachingTypesReadWrite CachingTypes = "ReadWrite"
)

// PossibleCachingTypesValues returns the possible values for the CachingTypes const type.
func PossibleCachingTypesValues() []CachingTypes {
	return []CachingTypes{
		CachingTypesNone,
		CachingTypesReadOnly,
		CachingTypesReadWrite,
	}
}

type CapacityReservationGroupInstanceViewTypes string

const (
	CapacityReservationGroupInstanceViewTypesInstanceView CapacityReservationGroupInstanceViewTypes = "instanceView"
)

// PossibleCapacityReservationGroupInstanceViewTypesValues returns the possible values for the CapacityReservationGroupInstanceViewTypes const type.
func PossibleCapacityReservationGroupInstanceViewTypesValues() []CapacityReservationGroupInstanceViewTypes {
	return []CapacityReservationGroupInstanceViewTypes{
		CapacityReservationGroupInstanceViewTypesInstanceView,
	}
}

type CapacityReservationInstanceViewTypes string

const (
	CapacityReservationInstanceViewTypesInstanceView CapacityReservationInstanceViewTypes = "instanceView"
)

// PossibleCapacityReservationInstanceViewTypesValues returns the possible values for the CapacityReservationInstanceViewTypes const type.
func PossibleCapacityReservationInstanceViewTypesValues() []CapacityReservationInstanceViewTypes {
	return []CapacityReservationInstanceViewTypes{
		CapacityReservationInstanceViewTypesInstanceView,
	}
}

// CloudServiceSlotType - Slot type for the cloud service. Possible values are
// Production
// Staging
// If not specified, the default value is Production.
type CloudServiceSlotType string

const (
	CloudServiceSlotTypeProduction CloudServiceSlotType = "Production"
	CloudServiceSlotTypeStaging    CloudServiceSlotType = "Staging"
)

// PossibleCloudServiceSlotTypeValues returns the possible values for the CloudServiceSlotType const type.
func PossibleCloudServiceSlotTypeValues() []CloudServiceSlotType {
	return []CloudServiceSlotType{
		CloudServiceSlotTypeProduction,
		CloudServiceSlotTypeStaging,
	}
}

// CloudServiceUpgradeMode - Update mode for the cloud service. Role instances are allocated to update domains when the service
// is deployed. Updates can be initiated manually in each update domain or initiated automatically in
// all update domains. Possible Values are
// Auto
// Manual
// Simultaneous
// If not specified, the default value is Auto. If set to Manual, PUT UpdateDomain must be called to apply the update. If
// set to Auto, the update is automatically applied to each update domain in
// sequence.
type CloudServiceUpgradeMode string

const (
	CloudServiceUpgradeModeAuto         CloudServiceUpgradeMode = "Auto"
	CloudServiceUpgradeModeManual       CloudServiceUpgradeMode = "Manual"
	CloudServiceUpgradeModeSimultaneous CloudServiceUpgradeMode = "Simultaneous"
)

// PossibleCloudServiceUpgradeModeValues returns the possible values for the CloudServiceUpgradeMode const type.
func PossibleCloudServiceUpgradeModeValues() []CloudServiceUpgradeMode {
	return []CloudServiceUpgradeMode{
		CloudServiceUpgradeModeAuto,
		CloudServiceUpgradeModeManual,
		CloudServiceUpgradeModeSimultaneous,
	}
}

// ConfidentialVMEncryptionType - confidential VM encryption types
type ConfidentialVMEncryptionType string

const (
	ConfidentialVMEncryptionTypeEncryptedVMGuestStateOnlyWithPmk ConfidentialVMEncryptionType = "EncryptedVMGuestStateOnlyWithPmk"
	ConfidentialVMEncryptionTypeEncryptedWithCmk                 ConfidentialVMEncryptionType = "EncryptedWithCmk"
	ConfidentialVMEncryptionTypeEncryptedWithPmk                 ConfidentialVMEncryptionType = "EncryptedWithPmk"
	ConfidentialVMEncryptionTypeNonPersistedTPM                  ConfidentialVMEncryptionType = "NonPersistedTPM"
)

// PossibleConfidentialVMEncryptionTypeValues returns the possible values for the ConfidentialVMEncryptionType const type.
func PossibleConfidentialVMEncryptionTypeValues() []ConfidentialVMEncryptionType {
	return []ConfidentialVMEncryptionType{
		ConfidentialVMEncryptionTypeEncryptedVMGuestStateOnlyWithPmk,
		ConfidentialVMEncryptionTypeEncryptedWithCmk,
		ConfidentialVMEncryptionTypeEncryptedWithPmk,
		ConfidentialVMEncryptionTypeNonPersistedTPM,
	}
}

// ConsistencyModeTypes - ConsistencyMode of the RestorePoint. Can be specified in the input while creating a restore point.
// For now, only CrashConsistent is accepted as a valid input. Please refer to
// https://aka.ms/RestorePoints for more details.
type ConsistencyModeTypes string

const (
	ConsistencyModeTypesApplicationConsistent ConsistencyModeTypes = "ApplicationConsistent"
	ConsistencyModeTypesCrashConsistent       ConsistencyModeTypes = "CrashConsistent"
	ConsistencyModeTypesFileSystemConsistent  ConsistencyModeTypes = "FileSystemConsistent"
)

// PossibleConsistencyModeTypesValues returns the possible values for the ConsistencyModeTypes const type.
func PossibleConsistencyModeTypesValues() []ConsistencyModeTypes {
	return []ConsistencyModeTypes{
		ConsistencyModeTypesApplicationConsistent,
		ConsistencyModeTypesCrashConsistent,
		ConsistencyModeTypesFileSystemConsistent,
	}
}

// CopyCompletionErrorReason - Indicates the error code if the background copy of a resource created via the CopyStart operation
// fails.
type CopyCompletionErrorReason string

const (
	// CopyCompletionErrorReasonCopySourceNotFound - Indicates that the source snapshot was deleted while the background copy
	// of the resource created via CopyStart operation was in progress.
	CopyCompletionErrorReasonCopySourceNotFound CopyCompletionErrorReason = "CopySourceNotFound"
)

// PossibleCopyCompletionErrorReasonValues returns the possible values for the CopyCompletionErrorReason const type.
func PossibleCopyCompletionErrorReasonValues() []CopyCompletionErrorReason {
	return []CopyCompletionErrorReason{
		CopyCompletionErrorReasonCopySourceNotFound,
	}
}

// DataAccessAuthMode - Additional authentication requirements when exporting or uploading to a disk or snapshot.
type DataAccessAuthMode string

const (
	// DataAccessAuthModeAzureActiveDirectory - When export/upload URL is used, the system checks if the user has an identity
	// in Azure Active Directory and has necessary permissions to export/upload the data. Please refer to aka.ms/DisksAzureADAuth.
	DataAccessAuthModeAzureActiveDirectory DataAccessAuthMode = "AzureActiveDirectory"
	// DataAccessAuthModeNone - No additional authentication would be performed when accessing export/upload URL.
	DataAccessAuthModeNone DataAccessAuthMode = "None"
)

// PossibleDataAccessAuthModeValues returns the possible values for the DataAccessAuthMode const type.
func PossibleDataAccessAuthModeValues() []DataAccessAuthMode {
	return []DataAccessAuthMode{
		DataAccessAuthModeAzureActiveDirectory,
		DataAccessAuthModeNone,
	}
}

// DedicatedHostLicenseTypes - Specifies the software license type that will be applied to the VMs deployed on the dedicated
// host. Possible values are: None, WindowsServerHybrid, WindowsServerPerpetual. The default value is: None.
type DedicatedHostLicenseTypes string

const (
	DedicatedHostLicenseTypesNone                   DedicatedHostLicenseTypes = "None"
	DedicatedHostLicenseTypesWindowsServerHybrid    DedicatedHostLicenseTypes = "Windows_Server_Hybrid"
	DedicatedHostLicenseTypesWindowsServerPerpetual DedicatedHostLicenseTypes = "Windows_Server_Perpetual"
)

// PossibleDedicatedHostLicenseTypesValues returns the possible values for the DedicatedHostLicenseTypes const type.
func PossibleDedicatedHostLicenseTypesValues() []DedicatedHostLicenseTypes {
	return []DedicatedHostLicenseTypes{
		DedicatedHostLicenseTypesNone,
		DedicatedHostLicenseTypesWindowsServerHybrid,
		DedicatedHostLicenseTypesWindowsServerPerpetual,
	}
}

// DeleteOptions - Specify what happens to the network interface when the VM is deleted
type DeleteOptions string

const (
	DeleteOptionsDelete DeleteOptions = "Delete"
	DeleteOptionsDetach DeleteOptions = "Detach"
)

// PossibleDeleteOptionsValues returns the possible values for the DeleteOptions const type.
func PossibleDeleteOptionsValues() []DeleteOptions {
	return []DeleteOptions{
		DeleteOptionsDelete,
		DeleteOptionsDetach,
	}
}

// DiffDiskOptions - Specifies the ephemeral disk option for operating system disk.
type DiffDiskOptions string

const (
	DiffDiskOptionsLocal DiffDiskOptions = "Local"
)

// PossibleDiffDiskOptionsValues returns the possible values for the DiffDiskOptions const type.
func PossibleDiffDiskOptionsValues() []DiffDiskOptions {
	return []DiffDiskOptions{
		DiffDiskOptionsLocal,
	}
}

// DiffDiskPlacement - Specifies the ephemeral disk placement for operating system disk. This property can be used by user
// in the request to choose the location i.e, cache disk, resource disk or nvme disk space for
// Ephemeral OS disk provisioning. For more information on Ephemeral OS disk size requirements, please refer Ephemeral OS
// disk size requirements for Windows VM at
// https://docs.microsoft.com/azure/virtual-machines/windows/ephemeral-os-disks#size-requirements and Linux VM at
// https://docs.microsoft.com/azure/virtual-machines/linux/ephemeral-os-disks#size-requirements. Minimum api-version for NvmeDisk:
// 2024-03-01.
type DiffDiskPlacement string

const (
	DiffDiskPlacementCacheDisk    DiffDiskPlacement = "CacheDisk"
	DiffDiskPlacementNvmeDisk     DiffDiskPlacement = "NvmeDisk"
	DiffDiskPlacementResourceDisk DiffDiskPlacement = "ResourceDisk"
)

// PossibleDiffDiskPlacementValues returns the possible values for the DiffDiskPlacement const type.
func PossibleDiffDiskPlacementValues() []DiffDiskPlacement {
	return []DiffDiskPlacement{
		DiffDiskPlacementCacheDisk,
		DiffDiskPlacementNvmeDisk,
		DiffDiskPlacementResourceDisk,
	}
}

// DiskControllerTypes - Specifies the disk controller type configured for the VM and VirtualMachineScaleSet. This property
// is only supported for virtual machines whose operating system disk and VM sku supports Generation 2
// (https://docs.microsoft.com/en-us/azure/virtual-machines/generation-2), please check the HyperVGenerations capability returned
// as part of VM sku capabilities in the response of Microsoft.Compute SKUs
// api for the region contains V2 (https://docs.microsoft.com/rest/api/compute/resourceskus/list). For more information about
// Disk Controller Types supported please refer to
// https://aka.ms/azure-diskcontrollertypes.
type DiskControllerTypes string

const (
	DiskControllerTypesNVMe DiskControllerTypes = "NVMe"
	DiskControllerTypesSCSI DiskControllerTypes = "SCSI"
)

// PossibleDiskControllerTypesValues returns the possible values for the DiskControllerTypes const type.
func PossibleDiskControllerTypesValues() []DiskControllerTypes {
	return []DiskControllerTypes{
		DiskControllerTypesNVMe,
		DiskControllerTypesSCSI,
	}
}

// DiskCreateOption - This enumerates the possible sources of a disk's creation.
type DiskCreateOption string

const (
	// DiskCreateOptionAttach - Disk will be attached to a VM.
	DiskCreateOptionAttach DiskCreateOption = "Attach"
	// DiskCreateOptionCopy - Create a new disk or snapshot by copying from a disk or snapshot specified by the given sourceResourceId.
	DiskCreateOptionCopy DiskCreateOption = "Copy"
	// DiskCreateOptionCopyFromSanSnapshot - Create a new disk by exporting from elastic san volume snapshot
	DiskCreateOptionCopyFromSanSnapshot DiskCreateOption = "CopyFromSanSnapshot"
	// DiskCreateOptionCopyStart - Create a new disk by using a deep copy process, where the resource creation is considered complete
	// only after all data has been copied from the source.
	DiskCreateOptionCopyStart DiskCreateOption = "CopyStart"
	// DiskCreateOptionEmpty - Create an empty data disk of a size given by diskSizeGB.
	DiskCreateOptionEmpty DiskCreateOption = "Empty"
	// DiskCreateOptionFromImage - Create a new disk from a platform image specified by the given imageReference or galleryImageReference.
	DiskCreateOptionFromImage DiskCreateOption = "FromImage"
	// DiskCreateOptionImport - Create a disk by importing from a blob specified by a sourceUri in a storage account specified
	// by storageAccountId.
	DiskCreateOptionImport DiskCreateOption = "Import"
	// DiskCreateOptionImportSecure - Similar to Import create option. Create a new Trusted Launch VM or Confidential VM supported
	// disk by importing additional blob for VM guest state specified by securityDataUri in storage account specified by storageAccountId
	DiskCreateOptionImportSecure DiskCreateOption = "ImportSecure"
	// DiskCreateOptionRestore - Create a new disk by copying from a backup recovery point.
	DiskCreateOptionRestore DiskCreateOption = "Restore"
	// DiskCreateOptionUpload - Create a new disk by obtaining a write token and using it to directly upload the contents of the
	// disk.
	DiskCreateOptionUpload DiskCreateOption = "Upload"
	// DiskCreateOptionUploadPreparedSecure - Similar to Upload create option. Create a new Trusted Launch VM or Confidential
	// VM supported disk and upload using write token in both disk and VM guest state
	DiskCreateOptionUploadPreparedSecure DiskCreateOption = "UploadPreparedSecure"
)

// PossibleDiskCreateOptionValues returns the possible values for the DiskCreateOption const type.
func PossibleDiskCreateOptionValues() []DiskCreateOption {
	return []DiskCreateOption{
		DiskCreateOptionAttach,
		DiskCreateOptionCopy,
		DiskCreateOptionCopyFromSanSnapshot,
		DiskCreateOptionCopyStart,
		DiskCreateOptionEmpty,
		DiskCreateOptionFromImage,
		DiskCreateOptionImport,
		DiskCreateOptionImportSecure,
		DiskCreateOptionRestore,
		DiskCreateOptionUpload,
		DiskCreateOptionUploadPreparedSecure,
	}
}

// DiskCreateOptionTypes - Specifies how the virtual machine disk should be created. Possible values are Attach: This value
// is used when you are using a specialized disk to create the virtual machine. FromImage: This value is
// used when you are using an image to create the virtual machine. If you are using a platform image, you should also use
// the imageReference element described above. If you are using a marketplace image,
// you should also use the plan element previously described. Empty: This value is used when creating an empty data disk.
// Copy: This value is used to create a data disk from a snapshot or another disk.
// Restore: This value is used to create a data disk from a disk restore point.
type DiskCreateOptionTypes string

const (
	DiskCreateOptionTypesAttach    DiskCreateOptionTypes = "Attach"
	DiskCreateOptionTypesCopy      DiskCreateOptionTypes = "Copy"
	DiskCreateOptionTypesEmpty     DiskCreateOptionTypes = "Empty"
	DiskCreateOptionTypesFromImage DiskCreateOptionTypes = "FromImage"
	DiskCreateOptionTypesRestore   DiskCreateOptionTypes = "Restore"
)

// PossibleDiskCreateOptionTypesValues returns the possible values for the DiskCreateOptionTypes const type.
func PossibleDiskCreateOptionTypesValues() []DiskCreateOptionTypes {
	return []DiskCreateOptionTypes{
		DiskCreateOptionTypesAttach,
		DiskCreateOptionTypesCopy,
		DiskCreateOptionTypesEmpty,
		DiskCreateOptionTypesFromImage,
		DiskCreateOptionTypesRestore,
	}
}

// DiskDeleteOptionTypes - Specifies the behavior of the managed disk when the VM gets deleted, for example whether the managed
// disk is deleted or detached. Supported values are: Delete. If this value is used, the managed disk
// is deleted when VM gets deleted. Detach. If this value is used, the managed disk is retained after VM gets deleted. Minimum
// api-version: 2021-03-01.
type DiskDeleteOptionTypes string

const (
	DiskDeleteOptionTypesDelete DiskDeleteOptionTypes = "Delete"
	DiskDeleteOptionTypesDetach DiskDeleteOptionTypes = "Detach"
)

// PossibleDiskDeleteOptionTypesValues returns the possible values for the DiskDeleteOptionTypes const type.
func PossibleDiskDeleteOptionTypesValues() []DiskDeleteOptionTypes {
	return []DiskDeleteOptionTypes{
		DiskDeleteOptionTypesDelete,
		DiskDeleteOptionTypesDetach,
	}
}

// DiskDetachOptionTypes - Specifies the detach behavior to be used while detaching a disk or which is already in the process
// of detachment from the virtual machine. Supported values are: ForceDetach. detachOption: ForceDetach
// is applicable only for managed data disks. If a previous detachment attempt of the data disk did not complete due to an
// unexpected failure from the virtual machine and the disk is still not released
// then use force-detach as a last resort option to detach the disk forcibly from the VM. All writes might not have been flushed
// when using this detach behavior. This feature is still in preview mode and
// is not supported for VirtualMachineScaleSet. To force-detach a data disk update toBeDetached to 'true' along with setting
// detachOption: 'ForceDetach'.
type DiskDetachOptionTypes string

const (
	DiskDetachOptionTypesForceDetach DiskDetachOptionTypes = "ForceDetach"
)

// PossibleDiskDetachOptionTypesValues returns the possible values for the DiskDetachOptionTypes const type.
func PossibleDiskDetachOptionTypesValues() []DiskDetachOptionTypes {
	return []DiskDetachOptionTypes{
		DiskDetachOptionTypesForceDetach,
	}
}

// DiskEncryptionSetIdentityType - The type of Managed Identity used by the DiskEncryptionSet. Only SystemAssigned is supported
// for new creations. Disk Encryption Sets can be updated with Identity type None during migration of
// subscription to a new Azure Active Directory tenant; it will cause the encrypted resources to lose access to the keys.
type DiskEncryptionSetIdentityType string

const (
	DiskEncryptionSetIdentityTypeNone                       DiskEncryptionSetIdentityType = "None"
	DiskEncryptionSetIdentityTypeSystemAssigned             DiskEncryptionSetIdentityType = "SystemAssigned"
	DiskEncryptionSetIdentityTypeSystemAssignedUserAssigned DiskEncryptionSetIdentityType = "SystemAssigned, UserAssigned"
	DiskEncryptionSetIdentityTypeUserAssigned               DiskEncryptionSetIdentityType = "UserAssigned"
)

// PossibleDiskEncryptionSetIdentityTypeValues returns the possible values for the DiskEncryptionSetIdentityType const type.
func PossibleDiskEncryptionSetIdentityTypeValues() []DiskEncryptionSetIdentityType {
	return []DiskEncryptionSetIdentityType{
		DiskEncryptionSetIdentityTypeNone,
		DiskEncryptionSetIdentityTypeSystemAssigned,
		DiskEncryptionSetIdentityTypeSystemAssignedUserAssigned,
		DiskEncryptionSetIdentityTypeUserAssigned,
	}
}

// DiskEncryptionSetType - The type of key used to encrypt the data of the disk.
type DiskEncryptionSetType string

const (
	// DiskEncryptionSetTypeConfidentialVMEncryptedWithCustomerKey - Confidential VM supported disk and VM guest state would be
	// encrypted with customer managed key.
	DiskEncryptionSetTypeConfidentialVMEncryptedWithCustomerKey DiskEncryptionSetType = "ConfidentialVmEncryptedWithCustomerKey"
	// DiskEncryptionSetTypeEncryptionAtRestWithCustomerKey - Resource using diskEncryptionSet would be encrypted at rest with
	// Customer managed key that can be changed and revoked by a customer.
	DiskEncryptionSetTypeEncryptionAtRestWithCustomerKey DiskEncryptionSetType = "EncryptionAtRestWithCustomerKey"
	// DiskEncryptionSetTypeEncryptionAtRestWithPlatformAndCustomerKeys - Resource using diskEncryptionSet would be encrypted
	// at rest with two layers of encryption. One of the keys is Customer managed and the other key is Platform managed.
	DiskEncryptionSetTypeEncryptionAtRestWithPlatformAndCustomerKeys DiskEncryptionSetType = "EncryptionAtRestWithPlatformAndCustomerKeys"
)

// PossibleDiskEncryptionSetTypeValues returns the possible values for the DiskEncryptionSetType const type.
func PossibleDiskEncryptionSetTypeValues() []DiskEncryptionSetType {
	return []DiskEncryptionSetType{
		DiskEncryptionSetTypeConfidentialVMEncryptedWithCustomerKey,
		DiskEncryptionSetTypeEncryptionAtRestWithCustomerKey,
		DiskEncryptionSetTypeEncryptionAtRestWithPlatformAndCustomerKeys,
	}
}

// DiskSecurityTypes - Specifies the SecurityType of the VM. Applicable for OS disks only.
type DiskSecurityTypes string

const (
	// DiskSecurityTypesConfidentialVMDiskEncryptedWithCustomerKey - Indicates Confidential VM disk with both OS disk and VM guest
	// state encrypted with a customer managed key
	DiskSecurityTypesConfidentialVMDiskEncryptedWithCustomerKey DiskSecurityTypes = "ConfidentialVM_DiskEncryptedWithCustomerKey"
	// DiskSecurityTypesConfidentialVMDiskEncryptedWithPlatformKey - Indicates Confidential VM disk with both OS disk and VM guest
	// state encrypted with a platform managed key
	DiskSecurityTypesConfidentialVMDiskEncryptedWithPlatformKey DiskSecurityTypes = "ConfidentialVM_DiskEncryptedWithPlatformKey"
	// DiskSecurityTypesConfidentialVMNonPersistedTPM - Indicates Confidential VM disk with a ephemeral vTPM. vTPM state is not
	// persisted across VM reboots.
	DiskSecurityTypesConfidentialVMNonPersistedTPM DiskSecurityTypes = "ConfidentialVM_NonPersistedTPM"
	// DiskSecurityTypesConfidentialVMVmguestStateOnlyEncryptedWithPlatformKey - Indicates Confidential VM disk with only VM guest
	// state encrypted
	DiskSecurityTypesConfidentialVMVmguestStateOnlyEncryptedWithPlatformKey DiskSecurityTypes = "ConfidentialVM_VMGuestStateOnlyEncryptedWithPlatformKey"
	// DiskSecurityTypesTrustedLaunch - Trusted Launch provides security features such as secure boot and virtual Trusted Platform
	// Module (vTPM)
	DiskSecurityTypesTrustedLaunch DiskSecurityTypes = "TrustedLaunch"
)

// PossibleDiskSecurityTypesValues returns the possible values for the DiskSecurityTypes const type.
func PossibleDiskSecurityTypesValues() []DiskSecurityTypes {
	return []DiskSecurityTypes{
		DiskSecurityTypesConfidentialVMDiskEncryptedWithCustomerKey,
		DiskSecurityTypesConfidentialVMDiskEncryptedWithPlatformKey,
		DiskSecurityTypesConfidentialVMNonPersistedTPM,
		DiskSecurityTypesConfidentialVMVmguestStateOnlyEncryptedWithPlatformKey,
		DiskSecurityTypesTrustedLaunch,
	}
}

// DiskState - This enumerates the possible state of the disk.
type DiskState string

const (
	// DiskStateActiveSAS - The disk currently has an Active SAS Uri associated with it.
	DiskStateActiveSAS DiskState = "ActiveSAS"
	// DiskStateActiveSASFrozen - The disk is attached to a VM in hibernated state and has an active SAS URI associated with it.
	DiskStateActiveSASFrozen DiskState = "ActiveSASFrozen"
	// DiskStateActiveUpload - A disk is created for upload and a write token has been issued for uploading to it.
	DiskStateActiveUpload DiskState = "ActiveUpload"
	// DiskStateAttached - The disk is currently attached to a running VM.
	DiskStateAttached DiskState = "Attached"
	// DiskStateFrozen - The disk is attached to a VM which is in hibernated state.
	DiskStateFrozen DiskState = "Frozen"
	// DiskStateReadyToUpload - A disk is ready to be created by upload by requesting a write token.
	DiskStateReadyToUpload DiskState = "ReadyToUpload"
	// DiskStateReserved - The disk is attached to a stopped-deallocated VM.
	DiskStateReserved DiskState = "Reserved"
	// DiskStateUnattached - The disk is not being used and can be attached to a VM.
	DiskStateUnattached DiskState = "Unattached"
)

// PossibleDiskStateValues returns the possible values for the DiskState const type.
func PossibleDiskStateValues() []DiskState {
	return []DiskState{
		DiskStateActiveSAS,
		DiskStateActiveSASFrozen,
		DiskStateActiveUpload,
		DiskStateAttached,
		DiskStateFrozen,
		DiskStateReadyToUpload,
		DiskStateReserved,
		DiskStateUnattached,
	}
}

// DiskStorageAccountTypes - The sku name.
type DiskStorageAccountTypes string

const (
	// DiskStorageAccountTypesPremiumLRS - Premium SSD locally redundant storage. Best for production and performance sensitive
	// workloads.
	DiskStorageAccountTypesPremiumLRS DiskStorageAccountTypes = "Premium_LRS"
	// DiskStorageAccountTypesPremiumV2LRS - Premium SSD v2 locally redundant storage. Best for production and performance-sensitive
	// workloads that consistently require low latency and high IOPS and throughput.
	DiskStorageAccountTypesPremiumV2LRS DiskStorageAccountTypes = "PremiumV2_LRS"
	// DiskStorageAccountTypesPremiumZRS - Premium SSD zone redundant storage. Best for the production workloads that need storage
	// resiliency against zone failures.
	DiskStorageAccountTypesPremiumZRS DiskStorageAccountTypes = "Premium_ZRS"
	// DiskStorageAccountTypesStandardLRS - Standard HDD locally redundant storage. Best for backup, non-critical, and infrequent
	// access.
	DiskStorageAccountTypesStandardLRS DiskStorageAccountTypes = "Standard_LRS"
	// DiskStorageAccountTypesStandardSSDLRS - Standard SSD locally redundant storage. Best for web servers, lightly used enterprise
	// applications and dev/test.
	DiskStorageAccountTypesStandardSSDLRS DiskStorageAccountTypes = "StandardSSD_LRS"
	// DiskStorageAccountTypesStandardSSDZRS - Standard SSD zone redundant storage. Best for web servers, lightly used enterprise
	// applications and dev/test that need storage resiliency against zone failures.
	DiskStorageAccountTypesStandardSSDZRS DiskStorageAccountTypes = "StandardSSD_ZRS"
	// DiskStorageAccountTypesUltraSSDLRS - Ultra SSD locally redundant storage. Best for IO-intensive workloads such as SAP HANA,
	// top tier databases (for example, SQL, Oracle), and other transaction-heavy workloads.
	DiskStorageAccountTypesUltraSSDLRS DiskStorageAccountTypes = "UltraSSD_LRS"
)

// PossibleDiskStorageAccountTypesValues returns the possible values for the DiskStorageAccountTypes const type.
func PossibleDiskStorageAccountTypesValues() []DiskStorageAccountTypes {
	return []DiskStorageAccountTypes{
		DiskStorageAccountTypesPremiumLRS,
		DiskStorageAccountTypesPremiumV2LRS,
		DiskStorageAccountTypesPremiumZRS,
		DiskStorageAccountTypesStandardLRS,
		DiskStorageAccountTypesStandardSSDLRS,
		DiskStorageAccountTypesStandardSSDZRS,
		DiskStorageAccountTypesUltraSSDLRS,
	}
}

// DomainNameLabelScopeTypes - The Domain name label scope.The concatenation of the hashed domain name label that generated
// according to the policy from domain name label scope and vm index will be the domain name labels of the
// PublicIPAddress resources that will be created
type DomainNameLabelScopeTypes string

const (
	DomainNameLabelScopeTypesNoReuse            DomainNameLabelScopeTypes = "NoReuse"
	DomainNameLabelScopeTypesResourceGroupReuse DomainNameLabelScopeTypes = "ResourceGroupReuse"
	DomainNameLabelScopeTypesSubscriptionReuse  DomainNameLabelScopeTypes = "SubscriptionReuse"
	DomainNameLabelScopeTypesTenantReuse        DomainNameLabelScopeTypes = "TenantReuse"
)

// PossibleDomainNameLabelScopeTypesValues returns the possible values for the DomainNameLabelScopeTypes const type.
func PossibleDomainNameLabelScopeTypesValues() []DomainNameLabelScopeTypes {
	return []DomainNameLabelScopeTypes{
		DomainNameLabelScopeTypesNoReuse,
		DomainNameLabelScopeTypesResourceGroupReuse,
		DomainNameLabelScopeTypesSubscriptionReuse,
		DomainNameLabelScopeTypesTenantReuse,
	}
}

// EdgeZoneStorageAccountType - Specifies the storage account type to be used to store the image. This property is not updatable.
type EdgeZoneStorageAccountType string

const (
	EdgeZoneStorageAccountTypePremiumLRS     EdgeZoneStorageAccountType = "Premium_LRS"
	EdgeZoneStorageAccountTypeStandardLRS    EdgeZoneStorageAccountType = "Standard_LRS"
	EdgeZoneStorageAccountTypeStandardSSDLRS EdgeZoneStorageAccountType = "StandardSSD_LRS"
	EdgeZoneStorageAccountTypeStandardZRS    EdgeZoneStorageAccountType = "Standard_ZRS"
)

// PossibleEdgeZoneStorageAccountTypeValues returns the possible values for the EdgeZoneStorageAccountType const type.
func PossibleEdgeZoneStorageAccountTypeValues() []EdgeZoneStorageAccountType {
	return []EdgeZoneStorageAccountType{
		EdgeZoneStorageAccountTypePremiumLRS,
		EdgeZoneStorageAccountTypeStandardLRS,
		EdgeZoneStorageAccountTypeStandardSSDLRS,
		EdgeZoneStorageAccountTypeStandardZRS,
	}
}

// EncryptionType - The type of key used to encrypt the data of the disk.
type EncryptionType string

const (
	// EncryptionTypeEncryptionAtRestWithCustomerKey - Disk is encrypted at rest with Customer managed key that can be changed
	// and revoked by a customer.
	EncryptionTypeEncryptionAtRestWithCustomerKey EncryptionType = "EncryptionAtRestWithCustomerKey"
	// EncryptionTypeEncryptionAtRestWithPlatformAndCustomerKeys - Disk is encrypted at rest with 2 layers of encryption. One
	// of the keys is Customer managed and the other key is Platform managed.
	EncryptionTypeEncryptionAtRestWithPlatformAndCustomerKeys EncryptionType = "EncryptionAtRestWithPlatformAndCustomerKeys"
	// EncryptionTypeEncryptionAtRestWithPlatformKey - Disk is encrypted at rest with Platform managed key. It is the default
	// encryption type. This is not a valid encryption type for disk encryption sets.
	EncryptionTypeEncryptionAtRestWithPlatformKey EncryptionType = "EncryptionAtRestWithPlatformKey"
)

// PossibleEncryptionTypeValues returns the possible values for the EncryptionType const type.
func PossibleEncryptionTypeValues() []EncryptionType {
	return []EncryptionType{
		EncryptionTypeEncryptionAtRestWithCustomerKey,
		EncryptionTypeEncryptionAtRestWithPlatformAndCustomerKeys,
		EncryptionTypeEncryptionAtRestWithPlatformKey,
	}
}

// ExecutionState - Script execution status.
type ExecutionState string

const (
	ExecutionStateCanceled  ExecutionState = "Canceled"
	ExecutionStateFailed    ExecutionState = "Failed"
	ExecutionStatePending   ExecutionState = "Pending"
	ExecutionStateRunning   ExecutionState = "Running"
	ExecutionStateSucceeded ExecutionState = "Succeeded"
	ExecutionStateTimedOut  ExecutionState = "TimedOut"
	ExecutionStateUnknown   ExecutionState = "Unknown"
)

// PossibleExecutionStateValues returns the possible values for the ExecutionState const type.
func PossibleExecutionStateValues() []ExecutionState {
	return []ExecutionState{
		ExecutionStateCanceled,
		ExecutionStateFailed,
		ExecutionStatePending,
		ExecutionStateRunning,
		ExecutionStateSucceeded,
		ExecutionStateTimedOut,
		ExecutionStateUnknown,
	}
}

type ExpandTypeForListVMs string

const (
	ExpandTypeForListVMsInstanceView ExpandTypeForListVMs = "instanceView"
)

// PossibleExpandTypeForListVMsValues returns the possible values for the ExpandTypeForListVMs const type.
func PossibleExpandTypeForListVMsValues() []ExpandTypeForListVMs {
	return []ExpandTypeForListVMs{
		ExpandTypeForListVMsInstanceView,
	}
}

type ExpandTypesForGetCapacityReservationGroups string

const (
	ExpandTypesForGetCapacityReservationGroupsVirtualMachineScaleSetVMsRef ExpandTypesForGetCapacityReservationGroups = "virtualMachineScaleSetVMs/$ref"
	ExpandTypesForGetCapacityReservationGroupsVirtualMachinesRef           ExpandTypesForGetCapacityReservationGroups = "virtualMachines/$ref"
)

// PossibleExpandTypesForGetCapacityReservationGroupsValues returns the possible values for the ExpandTypesForGetCapacityReservationGroups const type.
func PossibleExpandTypesForGetCapacityReservationGroupsValues() []ExpandTypesForGetCapacityReservationGroups {
	return []ExpandTypesForGetCapacityReservationGroups{
		ExpandTypesForGetCapacityReservationGroupsVirtualMachineScaleSetVMsRef,
		ExpandTypesForGetCapacityReservationGroupsVirtualMachinesRef,
	}
}

type ExpandTypesForGetVMScaleSets string

const (
	ExpandTypesForGetVMScaleSetsUserData ExpandTypesForGetVMScaleSets = "userData"
)

// PossibleExpandTypesForGetVMScaleSetsValues returns the possible values for the ExpandTypesForGetVMScaleSets const type.
func PossibleExpandTypesForGetVMScaleSetsValues() []ExpandTypesForGetVMScaleSets {
	return []ExpandTypesForGetVMScaleSets{
		ExpandTypesForGetVMScaleSetsUserData,
	}
}

type ExpandTypesForListVMs string

const (
	ExpandTypesForListVMsInstanceView ExpandTypesForListVMs = "instanceView"
)

// PossibleExpandTypesForListVMsValues returns the possible values for the ExpandTypesForListVMs const type.
func PossibleExpandTypesForListVMsValues() []ExpandTypesForListVMs {
	return []ExpandTypesForListVMs{
		ExpandTypesForListVMsInstanceView,
	}
}

// ExtendedLocationType - The type of the extended location.
type ExtendedLocationType string

const (
	ExtendedLocationTypeEdgeZone ExtendedLocationType = "EdgeZone"
)

// PossibleExtendedLocationTypeValues returns the possible values for the ExtendedLocationType const type.
func PossibleExtendedLocationTypeValues() []ExtendedLocationType {
	return []ExtendedLocationType{
		ExtendedLocationTypeEdgeZone,
	}
}

// ExtendedLocationTypes - The type of extendedLocation.
type ExtendedLocationTypes string

const (
	ExtendedLocationTypesEdgeZone ExtendedLocationTypes = "EdgeZone"
)

// PossibleExtendedLocationTypesValues returns the possible values for the ExtendedLocationTypes const type.
func PossibleExtendedLocationTypesValues() []ExtendedLocationTypes {
	return []ExtendedLocationTypes{
		ExtendedLocationTypesEdgeZone,
	}
}

// FileFormat - Used to specify the file format when making request for SAS on a VHDX file format snapshot
type FileFormat string

const (
	// FileFormatVHD - A VHD file is a disk image file in the Virtual Hard Disk file format.
	FileFormatVHD FileFormat = "VHD"
	// FileFormatVHDX - A VHDX file is a disk image file in the Virtual Hard Disk v2 file format.
	FileFormatVHDX FileFormat = "VHDX"
)

// PossibleFileFormatValues returns the possible values for the FileFormat const type.
func PossibleFileFormatValues() []FileFormat {
	return []FileFormat{
		FileFormatVHD,
		FileFormatVHDX,
	}
}

// GalleryApplicationCustomActionParameterType - Specifies the type of the custom action parameter. Possible values are: String,
// ConfigurationDataBlob or LogOutputBlob
type GalleryApplicationCustomActionParameterType string

const (
	GalleryApplicationCustomActionParameterTypeConfigurationDataBlob GalleryApplicationCustomActionParameterType = "ConfigurationDataBlob"
	GalleryApplicationCustomActionParameterTypeLogOutputBlob         GalleryApplicationCustomActionParameterType = "LogOutputBlob"
	GalleryApplicationCustomActionParameterTypeString                GalleryApplicationCustomActionParameterType = "String"
)

// PossibleGalleryApplicationCustomActionParameterTypeValues returns the possible values for the GalleryApplicationCustomActionParameterType const type.
func PossibleGalleryApplicationCustomActionParameterTypeValues() []GalleryApplicationCustomActionParameterType {
	return []GalleryApplicationCustomActionParameterType{
		GalleryApplicationCustomActionParameterTypeConfigurationDataBlob,
		GalleryApplicationCustomActionParameterTypeLogOutputBlob,
		GalleryApplicationCustomActionParameterTypeString,
	}
}

type GalleryExpandParams string

const (
	GalleryExpandParamsSharingProfileGroups GalleryExpandParams = "SharingProfile/Groups"
)

// PossibleGalleryExpandParamsValues returns the possible values for the GalleryExpandParams const type.
func PossibleGalleryExpandParamsValues() []GalleryExpandParams {
	return []GalleryExpandParams{
		GalleryExpandParamsSharingProfileGroups,
	}
}

// GalleryExtendedLocationType - It is type of the extended location.
type GalleryExtendedLocationType string

const (
	GalleryExtendedLocationTypeEdgeZone GalleryExtendedLocationType = "EdgeZone"
	GalleryExtendedLocationTypeUnknown  GalleryExtendedLocationType = "Unknown"
)

// PossibleGalleryExtendedLocationTypeValues returns the possible values for the GalleryExtendedLocationType const type.
func PossibleGalleryExtendedLocationTypeValues() []GalleryExtendedLocationType {
	return []GalleryExtendedLocationType{
		GalleryExtendedLocationTypeEdgeZone,
		GalleryExtendedLocationTypeUnknown,
	}
}

// GalleryProvisioningState - The provisioning state, which only appears in the response.
type GalleryProvisioningState string

const (
	GalleryProvisioningStateCreating  GalleryProvisioningState = "Creating"
	GalleryProvisioningStateDeleting  GalleryProvisioningState = "Deleting"
	GalleryProvisioningStateFailed    GalleryProvisioningState = "Failed"
	GalleryProvisioningStateMigrating GalleryProvisioningState = "Migrating"
	GalleryProvisioningStateSucceeded GalleryProvisioningState = "Succeeded"
	GalleryProvisioningStateUpdating  GalleryProvisioningState = "Updating"
)

// PossibleGalleryProvisioningStateValues returns the possible values for the GalleryProvisioningState const type.
func PossibleGalleryProvisioningStateValues() []GalleryProvisioningState {
	return []GalleryProvisioningState{
		GalleryProvisioningStateCreating,
		GalleryProvisioningStateDeleting,
		GalleryProvisioningStateFailed,
		GalleryProvisioningStateMigrating,
		GalleryProvisioningStateSucceeded,
		GalleryProvisioningStateUpdating,
	}
}

// GallerySharingPermissionTypes - This property allows you to specify the permission of sharing gallery. Possible values
// are: Private, Groups, Community.
type GallerySharingPermissionTypes string

const (
	GallerySharingPermissionTypesCommunity GallerySharingPermissionTypes = "Community"
	GallerySharingPermissionTypesGroups    GallerySharingPermissionTypes = "Groups"
	GallerySharingPermissionTypesPrivate   GallerySharingPermissionTypes = "Private"
)

// PossibleGallerySharingPermissionTypesValues returns the possible values for the GallerySharingPermissionTypes const type.
func PossibleGallerySharingPermissionTypesValues() []GallerySharingPermissionTypes {
	return []GallerySharingPermissionTypes{
		GallerySharingPermissionTypesCommunity,
		GallerySharingPermissionTypesGroups,
		GallerySharingPermissionTypesPrivate,
	}
}

// HostCaching - The host caching of the disk. Valid values are 'None', 'ReadOnly', and 'ReadWrite'
type HostCaching string

const (
	HostCachingNone      HostCaching = "None"
	HostCachingReadOnly  HostCaching = "ReadOnly"
	HostCachingReadWrite HostCaching = "ReadWrite"
)

// PossibleHostCachingValues returns the possible values for the HostCaching const type.
func PossibleHostCachingValues() []HostCaching {
	return []HostCaching{
		HostCachingNone,
		HostCachingReadOnly,
		HostCachingReadWrite,
	}
}

// HyperVGeneration - The hypervisor generation of the Virtual Machine. Applicable to OS disks only.
type HyperVGeneration string

const (
	HyperVGenerationV1 HyperVGeneration = "V1"
	HyperVGenerationV2 HyperVGeneration = "V2"
)

// PossibleHyperVGenerationValues returns the possible values for the HyperVGeneration const type.
func PossibleHyperVGenerationValues() []HyperVGeneration {
	return []HyperVGeneration{
		HyperVGenerationV1,
		HyperVGenerationV2,
	}
}

// HyperVGenerationType - Specifies the HyperVGeneration Type associated with a resource
type HyperVGenerationType string

const (
	HyperVGenerationTypeV1 HyperVGenerationType = "V1"
	HyperVGenerationTypeV2 HyperVGenerationType = "V2"
)

// PossibleHyperVGenerationTypeValues returns the possible values for the HyperVGenerationType const type.
func PossibleHyperVGenerationTypeValues() []HyperVGenerationType {
	return []HyperVGenerationType{
		HyperVGenerationTypeV1,
		HyperVGenerationTypeV2,
	}
}

// HyperVGenerationTypes - Specifies the HyperVGeneration Type
type HyperVGenerationTypes string

const (
	HyperVGenerationTypesV1 HyperVGenerationTypes = "V1"
	HyperVGenerationTypesV2 HyperVGenerationTypes = "V2"
)

// PossibleHyperVGenerationTypesValues returns the possible values for the HyperVGenerationTypes const type.
func PossibleHyperVGenerationTypesValues() []HyperVGenerationTypes {
	return []HyperVGenerationTypes{
		HyperVGenerationTypesV1,
		HyperVGenerationTypesV2,
	}
}

// IPVersion - Available from Api-Version 2017-03-30 onwards, it represents whether the specific ipconfiguration is IPv4 or
// IPv6. Default is taken as IPv4. Possible values are: 'IPv4' and 'IPv6'.
type IPVersion string

const (
	IPVersionIPv4 IPVersion = "IPv4"
	IPVersionIPv6 IPVersion = "IPv6"
)

// PossibleIPVersionValues returns the possible values for the IPVersion const type.
func PossibleIPVersionValues() []IPVersion {
	return []IPVersion{
		IPVersionIPv4,
		IPVersionIPv6,
	}
}

// IPVersions - Available from Api-Version 2019-07-01 onwards, it represents whether the specific ipconfiguration is IPv4
// or IPv6. Default is taken as IPv4. Possible values are: 'IPv4' and 'IPv6'.
type IPVersions string

const (
	IPVersionsIPv4 IPVersions = "IPv4"
	IPVersionsIPv6 IPVersions = "IPv6"
)

// PossibleIPVersionsValues returns the possible values for the IPVersions const type.
func PossibleIPVersionsValues() []IPVersions {
	return []IPVersions{
		IPVersionsIPv4,
		IPVersionsIPv6,
	}
}

// ImageState - Describes the state of the image.
type ImageState string

const (
	ImageStateActive                  ImageState = "Active"
	ImageStateDeprecated              ImageState = "Deprecated"
	ImageStateScheduledForDeprecation ImageState = "ScheduledForDeprecation"
)

// PossibleImageStateValues returns the possible values for the ImageState const type.
func PossibleImageStateValues() []ImageState {
	return []ImageState{
		ImageStateActive,
		ImageStateDeprecated,
		ImageStateScheduledForDeprecation,
	}
}

type InstanceViewTypes string

const (
	InstanceViewTypesInstanceView InstanceViewTypes = "instanceView"
	InstanceViewTypesUserData     InstanceViewTypes = "userData"
)

// PossibleInstanceViewTypesValues returns the possible values for the InstanceViewTypes const type.
func PossibleInstanceViewTypesValues() []InstanceViewTypes {
	return []InstanceViewTypes{
		InstanceViewTypesInstanceView,
		InstanceViewTypesUserData,
	}
}

// IntervalInMins - Interval value in minutes used to create LogAnalytics call rate logs.
type IntervalInMins string

const (
	IntervalInMinsFiveMins   IntervalInMins = "FiveMins"
	IntervalInMinsSixtyMins  IntervalInMins = "SixtyMins"
	IntervalInMinsThirtyMins IntervalInMins = "ThirtyMins"
	IntervalInMinsThreeMins  IntervalInMins = "ThreeMins"
)

// PossibleIntervalInMinsValues returns the possible values for the IntervalInMins const type.
func PossibleIntervalInMinsValues() []IntervalInMins {
	return []IntervalInMins{
		IntervalInMinsFiveMins,
		IntervalInMinsSixtyMins,
		IntervalInMinsThirtyMins,
		IntervalInMinsThreeMins,
	}
}

// LinuxPatchAssessmentMode - Specifies the mode of VM Guest Patch Assessment for the IaaS virtual machine.
// Possible values are:
// ImageDefault - You control the timing of patch assessments on a virtual machine.
// AutomaticByPlatform - The platform will trigger periodic patch assessments. The property provisionVMAgent must be true.
type LinuxPatchAssessmentMode string

const (
	LinuxPatchAssessmentModeAutomaticByPlatform LinuxPatchAssessmentMode = "AutomaticByPlatform"
	LinuxPatchAssessmentModeImageDefault        LinuxPatchAssessmentMode = "ImageDefault"
)

// PossibleLinuxPatchAssessmentModeValues returns the possible values for the LinuxPatchAssessmentMode const type.
func PossibleLinuxPatchAssessmentModeValues() []LinuxPatchAssessmentMode {
	return []LinuxPatchAssessmentMode{
		LinuxPatchAssessmentModeAutomaticByPlatform,
		LinuxPatchAssessmentModeImageDefault,
	}
}

// LinuxVMGuestPatchAutomaticByPlatformRebootSetting - Specifies the reboot setting for all AutomaticByPlatform patch installation
// operations.
type LinuxVMGuestPatchAutomaticByPlatformRebootSetting string

const (
	LinuxVMGuestPatchAutomaticByPlatformRebootSettingAlways     LinuxVMGuestPatchAutomaticByPlatformRebootSetting = "Always"
	LinuxVMGuestPatchAutomaticByPlatformRebootSettingIfRequired LinuxVMGuestPatchAutomaticByPlatformRebootSetting = "IfRequired"
	LinuxVMGuestPatchAutomaticByPlatformRebootSettingNever      LinuxVMGuestPatchAutomaticByPlatformRebootSetting = "Never"
	LinuxVMGuestPatchAutomaticByPlatformRebootSettingUnknown    LinuxVMGuestPatchAutomaticByPlatformRebootSetting = "Unknown"
)

// PossibleLinuxVMGuestPatchAutomaticByPlatformRebootSettingValues returns the possible values for the LinuxVMGuestPatchAutomaticByPlatformRebootSetting const type.
func PossibleLinuxVMGuestPatchAutomaticByPlatformRebootSettingValues() []LinuxVMGuestPatchAutomaticByPlatformRebootSetting {
	return []LinuxVMGuestPatchAutomaticByPlatformRebootSetting{
		LinuxVMGuestPatchAutomaticByPlatformRebootSettingAlways,
		LinuxVMGuestPatchAutomaticByPlatformRebootSettingIfRequired,
		LinuxVMGuestPatchAutomaticByPlatformRebootSettingNever,
		LinuxVMGuestPatchAutomaticByPlatformRebootSettingUnknown,
	}
}

// LinuxVMGuestPatchMode - Specifies the mode of VM Guest Patching to IaaS virtual machine or virtual machines associated
// to virtual machine scale set with OrchestrationMode as Flexible.
// Possible values are:
// ImageDefault - The virtual machine's default patching configuration is used.
// AutomaticByPlatform - The virtual machine will be automatically updated by the platform. The property provisionVMAgent
// must be true
type LinuxVMGuestPatchMode string

const (
	LinuxVMGuestPatchModeAutomaticByPlatform LinuxVMGuestPatchMode = "AutomaticByPlatform"
	LinuxVMGuestPatchModeImageDefault        LinuxVMGuestPatchMode = "ImageDefault"
)

// PossibleLinuxVMGuestPatchModeValues returns the possible values for the LinuxVMGuestPatchMode const type.
func PossibleLinuxVMGuestPatchModeValues() []LinuxVMGuestPatchMode {
	return []LinuxVMGuestPatchMode{
		LinuxVMGuestPatchModeAutomaticByPlatform,
		LinuxVMGuestPatchModeImageDefault,
	}
}

// MaintenanceOperationResultCodeTypes - The Last Maintenance Operation Result Code.
type MaintenanceOperationResultCodeTypes string

const (
	MaintenanceOperationResultCodeTypesMaintenanceAborted   MaintenanceOperationResultCodeTypes = "MaintenanceAborted"
	MaintenanceOperationResultCodeTypesMaintenanceCompleted MaintenanceOperationResultCodeTypes = "MaintenanceCompleted"
	MaintenanceOperationResultCodeTypesNone                 MaintenanceOperationResultCodeTypes = "None"
	MaintenanceOperationResultCodeTypesRetryLater           MaintenanceOperationResultCodeTypes = "RetryLater"
)

// PossibleMaintenanceOperationResultCodeTypesValues returns the possible values for the MaintenanceOperationResultCodeTypes const type.
func PossibleMaintenanceOperationResultCodeTypesValues() []MaintenanceOperationResultCodeTypes {
	return []MaintenanceOperationResultCodeTypes{
		MaintenanceOperationResultCodeTypesMaintenanceAborted,
		MaintenanceOperationResultCodeTypesMaintenanceCompleted,
		MaintenanceOperationResultCodeTypesNone,
		MaintenanceOperationResultCodeTypesRetryLater,
	}
}

// Mode - Specifies the mode that ProxyAgent will execute on if the feature is enabled. ProxyAgent will start to audit or
// monitor but not enforce access control over requests to host endpoints in Audit mode,
// while in Enforce mode it will enforce access control. The default value is Enforce mode.
type Mode string

const (
	ModeAudit   Mode = "Audit"
	ModeEnforce Mode = "Enforce"
)

// PossibleModeValues returns the possible values for the Mode const type.
func PossibleModeValues() []Mode {
	return []Mode{
		ModeAudit,
		ModeEnforce,
	}
}

// NetworkAPIVersion - specifies the Microsoft.Network API version used when creating networking resources in the Network
// Interface Configurations
type NetworkAPIVersion string

const (
	NetworkAPIVersionTwoThousandTwenty1101 NetworkAPIVersion = "2020-11-01"
)

// PossibleNetworkAPIVersionValues returns the possible values for the NetworkAPIVersion const type.
func PossibleNetworkAPIVersionValues() []NetworkAPIVersion {
	return []NetworkAPIVersion{
		NetworkAPIVersionTwoThousandTwenty1101,
	}
}

// NetworkAccessPolicy - Policy for accessing the disk via network.
type NetworkAccessPolicy string

const (
	// NetworkAccessPolicyAllowAll - The disk can be exported or uploaded to from any network.
	NetworkAccessPolicyAllowAll NetworkAccessPolicy = "AllowAll"
	// NetworkAccessPolicyAllowPrivate - The disk can be exported or uploaded to using a DiskAccess resource's private endpoints.
	NetworkAccessPolicyAllowPrivate NetworkAccessPolicy = "AllowPrivate"
	// NetworkAccessPolicyDenyAll - The disk cannot be exported.
	NetworkAccessPolicyDenyAll NetworkAccessPolicy = "DenyAll"
)

// PossibleNetworkAccessPolicyValues returns the possible values for the NetworkAccessPolicy const type.
func PossibleNetworkAccessPolicyValues() []NetworkAccessPolicy {
	return []NetworkAccessPolicy{
		NetworkAccessPolicyAllowAll,
		NetworkAccessPolicyAllowPrivate,
		NetworkAccessPolicyDenyAll,
	}
}

// NetworkInterfaceAuxiliaryMode - Specifies whether the Auxiliary mode is enabled for the Network Interface resource.
type NetworkInterfaceAuxiliaryMode string

const (
	NetworkInterfaceAuxiliaryModeAcceleratedConnections NetworkInterfaceAuxiliaryMode = "AcceleratedConnections"
	NetworkInterfaceAuxiliaryModeFloating               NetworkInterfaceAuxiliaryMode = "Floating"
	NetworkInterfaceAuxiliaryModeNone                   NetworkInterfaceAuxiliaryMode = "None"
)

// PossibleNetworkInterfaceAuxiliaryModeValues returns the possible values for the NetworkInterfaceAuxiliaryMode const type.
func PossibleNetworkInterfaceAuxiliaryModeValues() []NetworkInterfaceAuxiliaryMode {
	return []NetworkInterfaceAuxiliaryMode{
		NetworkInterfaceAuxiliaryModeAcceleratedConnections,
		NetworkInterfaceAuxiliaryModeFloating,
		NetworkInterfaceAuxiliaryModeNone,
	}
}

// NetworkInterfaceAuxiliarySKU - Specifies whether the Auxiliary sku is enabled for the Network Interface resource.
type NetworkInterfaceAuxiliarySKU string

const (
	NetworkInterfaceAuxiliarySKUA1   NetworkInterfaceAuxiliarySKU = "A1"
	NetworkInterfaceAuxiliarySKUA2   NetworkInterfaceAuxiliarySKU = "A2"
	NetworkInterfaceAuxiliarySKUA4   NetworkInterfaceAuxiliarySKU = "A4"
	NetworkInterfaceAuxiliarySKUA8   NetworkInterfaceAuxiliarySKU = "A8"
	NetworkInterfaceAuxiliarySKUNone NetworkInterfaceAuxiliarySKU = "None"
)

// PossibleNetworkInterfaceAuxiliarySKUValues returns the possible values for the NetworkInterfaceAuxiliarySKU const type.
func PossibleNetworkInterfaceAuxiliarySKUValues() []NetworkInterfaceAuxiliarySKU {
	return []NetworkInterfaceAuxiliarySKU{
		NetworkInterfaceAuxiliarySKUA1,
		NetworkInterfaceAuxiliarySKUA2,
		NetworkInterfaceAuxiliarySKUA4,
		NetworkInterfaceAuxiliarySKUA8,
		NetworkInterfaceAuxiliarySKUNone,
	}
}

// OperatingSystemStateTypes - This property allows the user to specify whether the virtual machines created under this image
// are 'Generalized' or 'Specialized'.
type OperatingSystemStateTypes string

const (
	OperatingSystemStateTypesGeneralized OperatingSystemStateTypes = "Generalized"
	OperatingSystemStateTypesSpecialized OperatingSystemStateTypes = "Specialized"
)

// PossibleOperatingSystemStateTypesValues returns the possible values for the OperatingSystemStateTypes const type.
func PossibleOperatingSystemStateTypesValues() []OperatingSystemStateTypes {
	return []OperatingSystemStateTypes{
		OperatingSystemStateTypesGeneralized,
		OperatingSystemStateTypesSpecialized,
	}
}

// OperatingSystemType - Gets the Operating System type.
type OperatingSystemType string

const (
	OperatingSystemTypeLinux   OperatingSystemType = "Linux"
	OperatingSystemTypeWindows OperatingSystemType = "Windows"
)

// PossibleOperatingSystemTypeValues returns the possible values for the OperatingSystemType const type.
func PossibleOperatingSystemTypeValues() []OperatingSystemType {
	return []OperatingSystemType{
		OperatingSystemTypeLinux,
		OperatingSystemTypeWindows,
	}
}

// OperatingSystemTypes - This property allows you to specify the supported type of the OS that application is built for.
// Possible values are: Windows, Linux.
type OperatingSystemTypes string

const (
	OperatingSystemTypesLinux   OperatingSystemTypes = "Linux"
	OperatingSystemTypesWindows OperatingSystemTypes = "Windows"
)

// PossibleOperatingSystemTypesValues returns the possible values for the OperatingSystemTypes const type.
func PossibleOperatingSystemTypesValues() []OperatingSystemTypes {
	return []OperatingSystemTypes{
		OperatingSystemTypesLinux,
		OperatingSystemTypesWindows,
	}
}

// OrchestrationMode - Specifies the orchestration mode for the virtual machine scale set.
type OrchestrationMode string

const (
	OrchestrationModeFlexible OrchestrationMode = "Flexible"
	OrchestrationModeUniform  OrchestrationMode = "Uniform"
)

// PossibleOrchestrationModeValues returns the possible values for the OrchestrationMode const type.
func PossibleOrchestrationModeValues() []OrchestrationMode {
	return []OrchestrationMode{
		OrchestrationModeFlexible,
		OrchestrationModeUniform,
	}
}

// OrchestrationServiceNames - The name of the service.
type OrchestrationServiceNames string

const (
	OrchestrationServiceNamesAutomaticRepairs OrchestrationServiceNames = "AutomaticRepairs"
)

// PossibleOrchestrationServiceNamesValues returns the possible values for the OrchestrationServiceNames const type.
func PossibleOrchestrationServiceNamesValues() []OrchestrationServiceNames {
	return []OrchestrationServiceNames{
		OrchestrationServiceNamesAutomaticRepairs,
	}
}

// OrchestrationServiceState - The current state of the service.
type OrchestrationServiceState string

const (
	OrchestrationServiceStateNotRunning OrchestrationServiceState = "NotRunning"
	OrchestrationServiceStateRunning    OrchestrationServiceState = "Running"
	OrchestrationServiceStateSuspended  OrchestrationServiceState = "Suspended"
)

// PossibleOrchestrationServiceStateValues returns the possible values for the OrchestrationServiceState const type.
func PossibleOrchestrationServiceStateValues() []OrchestrationServiceState {
	return []OrchestrationServiceState{
		OrchestrationServiceStateNotRunning,
		OrchestrationServiceStateRunning,
		OrchestrationServiceStateSuspended,
	}
}

// OrchestrationServiceStateAction - The action to be performed.
type OrchestrationServiceStateAction string

const (
	OrchestrationServiceStateActionResume  OrchestrationServiceStateAction = "Resume"
	OrchestrationServiceStateActionSuspend OrchestrationServiceStateAction = "Suspend"
)

// PossibleOrchestrationServiceStateActionValues returns the possible values for the OrchestrationServiceStateAction const type.
func PossibleOrchestrationServiceStateActionValues() []OrchestrationServiceStateAction {
	return []OrchestrationServiceStateAction{
		OrchestrationServiceStateActionResume,
		OrchestrationServiceStateActionSuspend,
	}
}

// PatchAssessmentState - Describes the availability of a given patch.
type PatchAssessmentState string

const (
	PatchAssessmentStateAvailable PatchAssessmentState = "Available"
	PatchAssessmentStateUnknown   PatchAssessmentState = "Unknown"
)

// PossiblePatchAssessmentStateValues returns the possible values for the PatchAssessmentState const type.
func PossiblePatchAssessmentStateValues() []PatchAssessmentState {
	return []PatchAssessmentState{
		PatchAssessmentStateAvailable,
		PatchAssessmentStateUnknown,
	}
}

// PatchInstallationState - The state of the patch after the installation operation completed.
type PatchInstallationState string

const (
	PatchInstallationStateExcluded    PatchInstallationState = "Excluded"
	PatchInstallationStateFailed      PatchInstallationState = "Failed"
	PatchInstallationStateInstalled   PatchInstallationState = "Installed"
	PatchInstallationStateNotSelected PatchInstallationState = "NotSelected"
	PatchInstallationStatePending     PatchInstallationState = "Pending"
	PatchInstallationStateUnknown     PatchInstallationState = "Unknown"
)

// PossiblePatchInstallationStateValues returns the possible values for the PatchInstallationState const type.
func PossiblePatchInstallationStateValues() []PatchInstallationState {
	return []PatchInstallationState{
		PatchInstallationStateExcluded,
		PatchInstallationStateFailed,
		PatchInstallationStateInstalled,
		PatchInstallationStateNotSelected,
		PatchInstallationStatePending,
		PatchInstallationStateUnknown,
	}
}

// PatchOperationStatus - The overall success or failure status of the operation. It remains "InProgress" until the operation
// completes. At that point it will become "Unknown", "Failed", "Succeeded", or
// "CompletedWithWarnings."
type PatchOperationStatus string

const (
	PatchOperationStatusCompletedWithWarnings PatchOperationStatus = "CompletedWithWarnings"
	PatchOperationStatusFailed                PatchOperationStatus = "Failed"
	PatchOperationStatusInProgress            PatchOperationStatus = "InProgress"
	PatchOperationStatusSucceeded             PatchOperationStatus = "Succeeded"
	PatchOperationStatusUnknown               PatchOperationStatus = "Unknown"
)

// PossiblePatchOperationStatusValues returns the possible values for the PatchOperationStatus const type.
func PossiblePatchOperationStatusValues() []PatchOperationStatus {
	return []PatchOperationStatus{
		PatchOperationStatusCompletedWithWarnings,
		PatchOperationStatusFailed,
		PatchOperationStatusInProgress,
		PatchOperationStatusSucceeded,
		PatchOperationStatusUnknown,
	}
}

// PolicyViolationCategory - Describes the nature of the policy violation.
type PolicyViolationCategory string

const (
	PolicyViolationCategoryCopyrightValidation PolicyViolationCategory = "CopyrightValidation"
	PolicyViolationCategoryIPTheft             PolicyViolationCategory = "IpTheft"
	PolicyViolationCategoryImageFlaggedUnsafe  PolicyViolationCategory = "ImageFlaggedUnsafe"
	PolicyViolationCategoryOther               PolicyViolationCategory = "Other"
)

// PossiblePolicyViolationCategoryValues returns the possible values for the PolicyViolationCategory const type.
func PossiblePolicyViolationCategoryValues() []PolicyViolationCategory {
	return []PolicyViolationCategory{
		PolicyViolationCategoryCopyrightValidation,
		PolicyViolationCategoryIPTheft,
		PolicyViolationCategoryImageFlaggedUnsafe,
		PolicyViolationCategoryOther,
	}
}

// PrivateEndpointConnectionProvisioningState - The current provisioning state.
type PrivateEndpointConnectionProvisioningState string

const (
	PrivateEndpointConnectionProvisioningStateCreating  PrivateEndpointConnectionProvisioningState = "Creating"
	PrivateEndpointConnectionProvisioningStateDeleting  PrivateEndpointConnectionProvisioningState = "Deleting"
	PrivateEndpointConnectionProvisioningStateFailed    PrivateEndpointConnectionProvisioningState = "Failed"
	PrivateEndpointConnectionProvisioningStateSucceeded PrivateEndpointConnectionProvisioningState = "Succeeded"
)

// PossiblePrivateEndpointConnectionProvisioningStateValues returns the possible values for the PrivateEndpointConnectionProvisioningState const type.
func PossiblePrivateEndpointConnectionProvisioningStateValues() []PrivateEndpointConnectionProvisioningState {
	return []PrivateEndpointConnectionProvisioningState{
		PrivateEndpointConnectionProvisioningStateCreating,
		PrivateEndpointConnectionProvisioningStateDeleting,
		PrivateEndpointConnectionProvisioningStateFailed,
		PrivateEndpointConnectionProvisioningStateSucceeded,
	}
}

// PrivateEndpointServiceConnectionStatus - The private endpoint connection status.
type PrivateEndpointServiceConnectionStatus string

const (
	PrivateEndpointServiceConnectionStatusApproved PrivateEndpointServiceConnectionStatus = "Approved"
	PrivateEndpointServiceConnectionStatusPending  PrivateEndpointServiceConnectionStatus = "Pending"
	PrivateEndpointServiceConnectionStatusRejected PrivateEndpointServiceConnectionStatus = "Rejected"
)

// PossiblePrivateEndpointServiceConnectionStatusValues returns the possible values for the PrivateEndpointServiceConnectionStatus const type.
func PossiblePrivateEndpointServiceConnectionStatusValues() []PrivateEndpointServiceConnectionStatus {
	return []PrivateEndpointServiceConnectionStatus{
		PrivateEndpointServiceConnectionStatusApproved,
		PrivateEndpointServiceConnectionStatusPending,
		PrivateEndpointServiceConnectionStatusRejected,
	}
}

// ProtocolTypes - Specifies the protocol of WinRM listener. Possible values are: http, https.
type ProtocolTypes string

const (
	ProtocolTypesHTTP  ProtocolTypes = "Http"
	ProtocolTypesHTTPS ProtocolTypes = "Https"
)

// PossibleProtocolTypesValues returns the possible values for the ProtocolTypes const type.
func PossibleProtocolTypesValues() []ProtocolTypes {
	return []ProtocolTypes{
		ProtocolTypesHTTP,
		ProtocolTypesHTTPS,
	}
}

// ProvisionedBandwidthCopyOption - If this field is set on a snapshot and createOption is CopyStart, the snapshot will be
// copied at a quicker speed.
type ProvisionedBandwidthCopyOption string

const (
	ProvisionedBandwidthCopyOptionEnhanced ProvisionedBandwidthCopyOption = "Enhanced"
	ProvisionedBandwidthCopyOptionNone     ProvisionedBandwidthCopyOption = "None"
)

// PossibleProvisionedBandwidthCopyOptionValues returns the possible values for the ProvisionedBandwidthCopyOption const type.
func PossibleProvisionedBandwidthCopyOptionValues() []ProvisionedBandwidthCopyOption {
	return []ProvisionedBandwidthCopyOption{
		ProvisionedBandwidthCopyOptionEnhanced,
		ProvisionedBandwidthCopyOptionNone,
	}
}

// ProximityPlacementGroupType - Specifies the type of the proximity placement group. Possible values are: Standard : Co-locate
// resources within an Azure region or Availability Zone. Ultra : For future use.
type ProximityPlacementGroupType string

const (
	ProximityPlacementGroupTypeStandard ProximityPlacementGroupType = "Standard"
	ProximityPlacementGroupTypeUltra    ProximityPlacementGroupType = "Ultra"
)

// PossibleProximityPlacementGroupTypeValues returns the possible values for the ProximityPlacementGroupType const type.
func PossibleProximityPlacementGroupTypeValues() []ProximityPlacementGroupType {
	return []ProximityPlacementGroupType{
		ProximityPlacementGroupTypeStandard,
		ProximityPlacementGroupTypeUltra,
	}
}

// PublicIPAddressSKUName - Specify public IP sku name
type PublicIPAddressSKUName string

const (
	PublicIPAddressSKUNameBasic    PublicIPAddressSKUName = "Basic"
	PublicIPAddressSKUNameStandard PublicIPAddressSKUName = "Standard"
)

// PossiblePublicIPAddressSKUNameValues returns the possible values for the PublicIPAddressSKUName const type.
func PossiblePublicIPAddressSKUNameValues() []PublicIPAddressSKUName {
	return []PublicIPAddressSKUName{
		PublicIPAddressSKUNameBasic,
		PublicIPAddressSKUNameStandard,
	}
}

// PublicIPAddressSKUTier - Specify public IP sku tier
type PublicIPAddressSKUTier string

const (
	PublicIPAddressSKUTierGlobal   PublicIPAddressSKUTier = "Global"
	PublicIPAddressSKUTierRegional PublicIPAddressSKUTier = "Regional"
)

// PossiblePublicIPAddressSKUTierValues returns the possible values for the PublicIPAddressSKUTier const type.
func PossiblePublicIPAddressSKUTierValues() []PublicIPAddressSKUTier {
	return []PublicIPAddressSKUTier{
		PublicIPAddressSKUTierGlobal,
		PublicIPAddressSKUTierRegional,
	}
}

// PublicIPAllocationMethod - Specify the public IP allocation type
type PublicIPAllocationMethod string

const (
	PublicIPAllocationMethodDynamic PublicIPAllocationMethod = "Dynamic"
	PublicIPAllocationMethodStatic  PublicIPAllocationMethod = "Static"
)

// PossiblePublicIPAllocationMethodValues returns the possible values for the PublicIPAllocationMethod const type.
func PossiblePublicIPAllocationMethodValues() []PublicIPAllocationMethod {
	return []PublicIPAllocationMethod{
		PublicIPAllocationMethodDynamic,
		PublicIPAllocationMethodStatic,
	}
}

// PublicNetworkAccess - Policy for controlling export on the disk.
type PublicNetworkAccess string

const (
	// PublicNetworkAccessDisabled - You cannot access the underlying data of the disk publicly on the internet even when NetworkAccessPolicy
	// is set to AllowAll. You can access the data via the SAS URI only from your trusted Azure VNET when NetworkAccessPolicy
	// is set to AllowPrivate.
	PublicNetworkAccessDisabled PublicNetworkAccess = "Disabled"
	// PublicNetworkAccessEnabled - You can generate a SAS URI to access the underlying data of the disk publicly on the internet
	// when NetworkAccessPolicy is set to AllowAll. You can access the data via the SAS URI only from your trusted Azure VNET
	// when NetworkAccessPolicy is set to AllowPrivate.
	PublicNetworkAccessEnabled PublicNetworkAccess = "Enabled"
)

// PossiblePublicNetworkAccessValues returns the possible values for the PublicNetworkAccess const type.
func PossiblePublicNetworkAccessValues() []PublicNetworkAccess {
	return []PublicNetworkAccess{
		PublicNetworkAccessDisabled,
		PublicNetworkAccessEnabled,
	}
}

// RepairAction - Type of repair action (replace, restart, reimage) that will be used for repairing unhealthy virtual machines
// in the scale set. Default value is replace.
type RepairAction string

const (
	RepairActionReimage RepairAction = "Reimage"
	RepairActionReplace RepairAction = "Replace"
	RepairActionRestart RepairAction = "Restart"
)

// PossibleRepairActionValues returns the possible values for the RepairAction const type.
func PossibleRepairActionValues() []RepairAction {
	return []RepairAction{
		RepairActionReimage,
		RepairActionReplace,
		RepairActionRestart,
	}
}

// ReplicationMode - Optional parameter which specifies the mode to be used for replication. This property is not updatable.
type ReplicationMode string

const (
	ReplicationModeFull    ReplicationMode = "Full"
	ReplicationModeShallow ReplicationMode = "Shallow"
)

// PossibleReplicationModeValues returns the possible values for the ReplicationMode const type.
func PossibleReplicationModeValues() []ReplicationMode {
	return []ReplicationMode{
		ReplicationModeFull,
		ReplicationModeShallow,
	}
}

// ReplicationState - This is the regional replication state.
type ReplicationState string

const (
	ReplicationStateCompleted   ReplicationState = "Completed"
	ReplicationStateFailed      ReplicationState = "Failed"
	ReplicationStateReplicating ReplicationState = "Replicating"
	ReplicationStateUnknown     ReplicationState = "Unknown"
)

// PossibleReplicationStateValues returns the possible values for the ReplicationState const type.
func PossibleReplicationStateValues() []ReplicationState {
	return []ReplicationState{
		ReplicationStateCompleted,
		ReplicationStateFailed,
		ReplicationStateReplicating,
		ReplicationStateUnknown,
	}
}

type ReplicationStatusTypes string

const (
	ReplicationStatusTypesReplicationStatus ReplicationStatusTypes = "ReplicationStatus"
	ReplicationStatusTypesUefiSettings      ReplicationStatusTypes = "UefiSettings"
)

// PossibleReplicationStatusTypesValues returns the possible values for the ReplicationStatusTypes const type.
func PossibleReplicationStatusTypesValues() []ReplicationStatusTypes {
	return []ReplicationStatusTypes{
		ReplicationStatusTypesReplicationStatus,
		ReplicationStatusTypesUefiSettings,
	}
}

type ResourceIDOptionsForGetCapacityReservationGroups string

const (
	ResourceIDOptionsForGetCapacityReservationGroupsAll                    ResourceIDOptionsForGetCapacityReservationGroups = "All"
	ResourceIDOptionsForGetCapacityReservationGroupsCreatedInSubscription  ResourceIDOptionsForGetCapacityReservationGroups = "CreatedInSubscription"
	ResourceIDOptionsForGetCapacityReservationGroupsSharedWithSubscription ResourceIDOptionsForGetCapacityReservationGroups = "SharedWithSubscription"
)

// PossibleResourceIDOptionsForGetCapacityReservationGroupsValues returns the possible values for the ResourceIDOptionsForGetCapacityReservationGroups const type.
func PossibleResourceIDOptionsForGetCapacityReservationGroupsValues() []ResourceIDOptionsForGetCapacityReservationGroups {
	return []ResourceIDOptionsForGetCapacityReservationGroups{
		ResourceIDOptionsForGetCapacityReservationGroupsAll,
		ResourceIDOptionsForGetCapacityReservationGroupsCreatedInSubscription,
		ResourceIDOptionsForGetCapacityReservationGroupsSharedWithSubscription,
	}
}

// ResourceIdentityType - The type of identity used for the virtual machine scale set. The type 'SystemAssigned, UserAssigned'
// includes both an implicitly created identity and a set of user assigned identities. The type 'None'
// will remove any identities from the virtual machine scale set.
type ResourceIdentityType string

const (
	ResourceIdentityTypeNone                       ResourceIdentityType = "None"
	ResourceIdentityTypeSystemAssigned             ResourceIdentityType = "SystemAssigned"
	ResourceIdentityTypeSystemAssignedUserAssigned ResourceIdentityType = "SystemAssigned, UserAssigned"
	ResourceIdentityTypeUserAssigned               ResourceIdentityType = "UserAssigned"
)

// PossibleResourceIdentityTypeValues returns the possible values for the ResourceIdentityType const type.
func PossibleResourceIdentityTypeValues() []ResourceIdentityType {
	return []ResourceIdentityType{
		ResourceIdentityTypeNone,
		ResourceIdentityTypeSystemAssigned,
		ResourceIdentityTypeSystemAssignedUserAssigned,
		ResourceIdentityTypeUserAssigned,
	}
}

// ResourceSKUCapacityScaleType - The scale type applicable to the sku.
type ResourceSKUCapacityScaleType string

const (
	ResourceSKUCapacityScaleTypeAutomatic ResourceSKUCapacityScaleType = "Automatic"
	ResourceSKUCapacityScaleTypeManual    ResourceSKUCapacityScaleType = "Manual"
	ResourceSKUCapacityScaleTypeNone      ResourceSKUCapacityScaleType = "None"
)

// PossibleResourceSKUCapacityScaleTypeValues returns the possible values for the ResourceSKUCapacityScaleType const type.
func PossibleResourceSKUCapacityScaleTypeValues() []ResourceSKUCapacityScaleType {
	return []ResourceSKUCapacityScaleType{
		ResourceSKUCapacityScaleTypeAutomatic,
		ResourceSKUCapacityScaleTypeManual,
		ResourceSKUCapacityScaleTypeNone,
	}
}

// ResourceSKURestrictionsReasonCode - The reason for restriction.
type ResourceSKURestrictionsReasonCode string

const (
	ResourceSKURestrictionsReasonCodeNotAvailableForSubscription ResourceSKURestrictionsReasonCode = "NotAvailableForSubscription"
	ResourceSKURestrictionsReasonCodeQuotaID                     ResourceSKURestrictionsReasonCode = "QuotaId"
)

// PossibleResourceSKURestrictionsReasonCodeValues returns the possible values for the ResourceSKURestrictionsReasonCode const type.
func PossibleResourceSKURestrictionsReasonCodeValues() []ResourceSKURestrictionsReasonCode {
	return []ResourceSKURestrictionsReasonCode{
		ResourceSKURestrictionsReasonCodeNotAvailableForSubscription,
		ResourceSKURestrictionsReasonCodeQuotaID,
	}
}

// ResourceSKURestrictionsType - The type of restrictions.
type ResourceSKURestrictionsType string

const (
	ResourceSKURestrictionsTypeLocation ResourceSKURestrictionsType = "Location"
	ResourceSKURestrictionsTypeZone     ResourceSKURestrictionsType = "Zone"
)

// PossibleResourceSKURestrictionsTypeValues returns the possible values for the ResourceSKURestrictionsType const type.
func PossibleResourceSKURestrictionsTypeValues() []ResourceSKURestrictionsType {
	return []ResourceSKURestrictionsType{
		ResourceSKURestrictionsTypeLocation,
		ResourceSKURestrictionsTypeZone,
	}
}

type RestorePointCollectionExpandOptions string

const (
	RestorePointCollectionExpandOptionsRestorePoints RestorePointCollectionExpandOptions = "restorePoints"
)

// PossibleRestorePointCollectionExpandOptionsValues returns the possible values for the RestorePointCollectionExpandOptions const type.
func PossibleRestorePointCollectionExpandOptionsValues() []RestorePointCollectionExpandOptions {
	return []RestorePointCollectionExpandOptions{
		RestorePointCollectionExpandOptionsRestorePoints,
	}
}

// RestorePointEncryptionType - The type of key used to encrypt the data of the disk restore point.
type RestorePointEncryptionType string

const (
	// RestorePointEncryptionTypeEncryptionAtRestWithCustomerKey - Disk Restore Point is encrypted at rest with Customer managed
	// key that can be changed and revoked by a customer.
	RestorePointEncryptionTypeEncryptionAtRestWithCustomerKey RestorePointEncryptionType = "EncryptionAtRestWithCustomerKey"
	// RestorePointEncryptionTypeEncryptionAtRestWithPlatformAndCustomerKeys - Disk Restore Point is encrypted at rest with 2
	// layers of encryption. One of the keys is Customer managed and the other key is Platform managed.
	RestorePointEncryptionTypeEncryptionAtRestWithPlatformAndCustomerKeys RestorePointEncryptionType = "EncryptionAtRestWithPlatformAndCustomerKeys"
	// RestorePointEncryptionTypeEncryptionAtRestWithPlatformKey - Disk Restore Point is encrypted at rest with Platform managed
	// key.
	RestorePointEncryptionTypeEncryptionAtRestWithPlatformKey RestorePointEncryptionType = "EncryptionAtRestWithPlatformKey"
)

// PossibleRestorePointEncryptionTypeValues returns the possible values for the RestorePointEncryptionType const type.
func PossibleRestorePointEncryptionTypeValues() []RestorePointEncryptionType {
	return []RestorePointEncryptionType{
		RestorePointEncryptionTypeEncryptionAtRestWithCustomerKey,
		RestorePointEncryptionTypeEncryptionAtRestWithPlatformAndCustomerKeys,
		RestorePointEncryptionTypeEncryptionAtRestWithPlatformKey,
	}
}

type RestorePointExpandOptions string

const (
	RestorePointExpandOptionsInstanceView RestorePointExpandOptions = "instanceView"
)

// PossibleRestorePointExpandOptionsValues returns the possible values for the RestorePointExpandOptions const type.
func PossibleRestorePointExpandOptionsValues() []RestorePointExpandOptions {
	return []RestorePointExpandOptions{
		RestorePointExpandOptionsInstanceView,
	}
}

// RollingUpgradeActionType - The last action performed on the rolling upgrade.
type RollingUpgradeActionType string

const (
	RollingUpgradeActionTypeCancel RollingUpgradeActionType = "Cancel"
	RollingUpgradeActionTypeStart  RollingUpgradeActionType = "Start"
)

// PossibleRollingUpgradeActionTypeValues returns the possible values for the RollingUpgradeActionType const type.
func PossibleRollingUpgradeActionTypeValues() []RollingUpgradeActionType {
	return []RollingUpgradeActionType{
		RollingUpgradeActionTypeCancel,
		RollingUpgradeActionTypeStart,
	}
}

// RollingUpgradeStatusCode - Code indicating the current status of the upgrade.
type RollingUpgradeStatusCode string

const (
	RollingUpgradeStatusCodeCancelled      RollingUpgradeStatusCode = "Cancelled"
	RollingUpgradeStatusCodeCompleted      RollingUpgradeStatusCode = "Completed"
	RollingUpgradeStatusCodeFaulted        RollingUpgradeStatusCode = "Faulted"
	RollingUpgradeStatusCodeRollingForward RollingUpgradeStatusCode = "RollingForward"
)

// PossibleRollingUpgradeStatusCodeValues returns the possible values for the RollingUpgradeStatusCode const type.
func PossibleRollingUpgradeStatusCodeValues() []RollingUpgradeStatusCode {
	return []RollingUpgradeStatusCode{
		RollingUpgradeStatusCodeCancelled,
		RollingUpgradeStatusCodeCompleted,
		RollingUpgradeStatusCodeFaulted,
		RollingUpgradeStatusCodeRollingForward,
	}
}

// SSHEncryptionTypes - The encryption type of the SSH keys to be generated. See SshEncryptionTypes for possible set of values.
// If not provided, will default to RSA
type SSHEncryptionTypes string

const (
	SSHEncryptionTypesEd25519 SSHEncryptionTypes = "Ed25519"
	SSHEncryptionTypesRSA     SSHEncryptionTypes = "RSA"
)

// PossibleSSHEncryptionTypesValues returns the possible values for the SSHEncryptionTypes const type.
func PossibleSSHEncryptionTypesValues() []SSHEncryptionTypes {
	return []SSHEncryptionTypes{
		SSHEncryptionTypesEd25519,
		SSHEncryptionTypesRSA,
	}
}

// SecurityEncryptionTypes - Specifies the EncryptionType of the managed disk. It is set to DiskWithVMGuestState for encryption
// of the managed disk along with VMGuestState blob, VMGuestStateOnly for encryption of just the
// VMGuestState blob, and NonPersistedTPM for not persisting firmware state in the VMGuestState blob.. Note: It can be set
// for only Confidential VMs.
type SecurityEncryptionTypes string

const (
	SecurityEncryptionTypesDiskWithVMGuestState SecurityEncryptionTypes = "DiskWithVMGuestState"
	SecurityEncryptionTypesNonPersistedTPM      SecurityEncryptionTypes = "NonPersistedTPM"
	SecurityEncryptionTypesVMGuestStateOnly     SecurityEncryptionTypes = "VMGuestStateOnly"
)

// PossibleSecurityEncryptionTypesValues returns the possible values for the SecurityEncryptionTypes const type.
func PossibleSecurityEncryptionTypesValues() []SecurityEncryptionTypes {
	return []SecurityEncryptionTypes{
		SecurityEncryptionTypesDiskWithVMGuestState,
		SecurityEncryptionTypesNonPersistedTPM,
		SecurityEncryptionTypesVMGuestStateOnly,
	}
}

// SecurityTypes - Specifies the SecurityType of the virtual machine. It has to be set to any specified value to enable UefiSettings.
// The default behavior is: UefiSettings will not be enabled unless this property is
// set.
type SecurityTypes string

const (
	SecurityTypesConfidentialVM SecurityTypes = "ConfidentialVM"
	SecurityTypesTrustedLaunch  SecurityTypes = "TrustedLaunch"
)

// PossibleSecurityTypesValues returns the possible values for the SecurityTypes const type.
func PossibleSecurityTypesValues() []SecurityTypes {
	return []SecurityTypes{
		SecurityTypesConfidentialVM,
		SecurityTypesTrustedLaunch,
	}
}

type SelectPermissions string

const (
	SelectPermissionsPermissions SelectPermissions = "Permissions"
)

// PossibleSelectPermissionsValues returns the possible values for the SelectPermissions const type.
func PossibleSelectPermissionsValues() []SelectPermissions {
	return []SelectPermissions{
		SelectPermissionsPermissions,
	}
}

// SettingNames - Specifies the name of the setting to which the content applies. Possible values are: FirstLogonCommands
// and AutoLogon.
type SettingNames string

const (
	SettingNamesAutoLogon          SettingNames = "AutoLogon"
	SettingNamesFirstLogonCommands SettingNames = "FirstLogonCommands"
)

// PossibleSettingNamesValues returns the possible values for the SettingNames const type.
func PossibleSettingNamesValues() []SettingNames {
	return []SettingNames{
		SettingNamesAutoLogon,
		SettingNamesFirstLogonCommands,
	}
}

// SharedGalleryHostCaching - The host caching of the disk. Valid values are 'None', 'ReadOnly', and 'ReadWrite'
type SharedGalleryHostCaching string

const (
	SharedGalleryHostCachingNone      SharedGalleryHostCaching = "None"
	SharedGalleryHostCachingReadOnly  SharedGalleryHostCaching = "ReadOnly"
	SharedGalleryHostCachingReadWrite SharedGalleryHostCaching = "ReadWrite"
)

// PossibleSharedGalleryHostCachingValues returns the possible values for the SharedGalleryHostCaching const type.
func PossibleSharedGalleryHostCachingValues() []SharedGalleryHostCaching {
	return []SharedGalleryHostCaching{
		SharedGalleryHostCachingNone,
		SharedGalleryHostCachingReadOnly,
		SharedGalleryHostCachingReadWrite,
	}
}

type SharedToValues string

const (
	SharedToValuesTenant SharedToValues = "tenant"
)

// PossibleSharedToValuesValues returns the possible values for the SharedToValues const type.
func PossibleSharedToValuesValues() []SharedToValues {
	return []SharedToValues{
		SharedToValuesTenant,
	}
}

// SharingProfileGroupTypes - This property allows you to specify the type of sharing group. Possible values are: Subscriptions,
// AADTenants.
type SharingProfileGroupTypes string

const (
	SharingProfileGroupTypesAADTenants    SharingProfileGroupTypes = "AADTenants"
	SharingProfileGroupTypesSubscriptions SharingProfileGroupTypes = "Subscriptions"
)

// PossibleSharingProfileGroupTypesValues returns the possible values for the SharingProfileGroupTypes const type.
func PossibleSharingProfileGroupTypesValues() []SharingProfileGroupTypes {
	return []SharingProfileGroupTypes{
		SharingProfileGroupTypesAADTenants,
		SharingProfileGroupTypesSubscriptions,
	}
}

// SharingState - The sharing state of the gallery, which only appears in the response.
type SharingState string

const (
	SharingStateFailed     SharingState = "Failed"
	SharingStateInProgress SharingState = "InProgress"
	SharingStateSucceeded  SharingState = "Succeeded"
	SharingStateUnknown    SharingState = "Unknown"
)

// PossibleSharingStateValues returns the possible values for the SharingState const type.
func PossibleSharingStateValues() []SharingState {
	return []SharingState{
		SharingStateFailed,
		SharingStateInProgress,
		SharingStateSucceeded,
		SharingStateUnknown,
	}
}

// SharingUpdateOperationTypes - This property allows you to specify the operation type of gallery sharing update. Possible
// values are: Add, Remove, Reset.
type SharingUpdateOperationTypes string

const (
	SharingUpdateOperationTypesAdd             SharingUpdateOperationTypes = "Add"
	SharingUpdateOperationTypesEnableCommunity SharingUpdateOperationTypes = "EnableCommunity"
	SharingUpdateOperationTypesRemove          SharingUpdateOperationTypes = "Remove"
	SharingUpdateOperationTypesReset           SharingUpdateOperationTypes = "Reset"
)

// PossibleSharingUpdateOperationTypesValues returns the possible values for the SharingUpdateOperationTypes const type.
func PossibleSharingUpdateOperationTypesValues() []SharingUpdateOperationTypes {
	return []SharingUpdateOperationTypes{
		SharingUpdateOperationTypesAdd,
		SharingUpdateOperationTypesEnableCommunity,
		SharingUpdateOperationTypesRemove,
		SharingUpdateOperationTypesReset,
	}
}

// SnapshotStorageAccountTypes - The sku name.
type SnapshotStorageAccountTypes string

const (
	// SnapshotStorageAccountTypesPremiumLRS - Premium SSD locally redundant storage
	SnapshotStorageAccountTypesPremiumLRS SnapshotStorageAccountTypes = "Premium_LRS"
	// SnapshotStorageAccountTypesStandardLRS - Standard HDD locally redundant storage
	SnapshotStorageAccountTypesStandardLRS SnapshotStorageAccountTypes = "Standard_LRS"
	// SnapshotStorageAccountTypesStandardZRS - Standard zone redundant storage
	SnapshotStorageAccountTypesStandardZRS SnapshotStorageAccountTypes = "Standard_ZRS"
)

// PossibleSnapshotStorageAccountTypesValues returns the possible values for the SnapshotStorageAccountTypes const type.
func PossibleSnapshotStorageAccountTypesValues() []SnapshotStorageAccountTypes {
	return []SnapshotStorageAccountTypes{
		SnapshotStorageAccountTypesPremiumLRS,
		SnapshotStorageAccountTypesStandardLRS,
		SnapshotStorageAccountTypesStandardZRS,
	}
}

// StatusLevelTypes - The level code.
type StatusLevelTypes string

const (
	StatusLevelTypesError   StatusLevelTypes = "Error"
	StatusLevelTypesInfo    StatusLevelTypes = "Info"
	StatusLevelTypesWarning StatusLevelTypes = "Warning"
)

// PossibleStatusLevelTypesValues returns the possible values for the StatusLevelTypes const type.
func PossibleStatusLevelTypesValues() []StatusLevelTypes {
	return []StatusLevelTypes{
		StatusLevelTypesError,
		StatusLevelTypesInfo,
		StatusLevelTypesWarning,
	}
}

// StorageAccountType - Specifies the storage account type to be used to store the image. This property is not updatable.
type StorageAccountType string

const (
	StorageAccountTypePremiumLRS  StorageAccountType = "Premium_LRS"
	StorageAccountTypeStandardLRS StorageAccountType = "Standard_LRS"
	StorageAccountTypeStandardZRS StorageAccountType = "Standard_ZRS"
)

// PossibleStorageAccountTypeValues returns the possible values for the StorageAccountType const type.
func PossibleStorageAccountTypeValues() []StorageAccountType {
	return []StorageAccountType{
		StorageAccountTypePremiumLRS,
		StorageAccountTypeStandardLRS,
		StorageAccountTypeStandardZRS,
	}
}

// StorageAccountTypes - Specifies the storage account type for the managed disk. Managed OS disk storage account type can
// only be set when you create the scale set. NOTE: UltraSSDLRS can only be used with data disks. It
// cannot be used with OS Disk. StandardLRS uses Standard HDD. StandardSSDLRS uses Standard SSD. PremiumLRS uses Premium SSD.
// UltraSSDLRS uses Ultra disk. PremiumZRS uses Premium SSD zone redundant
// storage. StandardSSD_ZRS uses Standard SSD zone redundant storage. For more information regarding disks supported for Windows
// Virtual Machines, refer to
// https://docs.microsoft.com/azure/virtual-machines/windows/disks-types and, for Linux Virtual Machines, refer to https://docs.microsoft.com/azure/virtual-machines/linux/disks-types
type StorageAccountTypes string

const (
	StorageAccountTypesPremiumLRS     StorageAccountTypes = "Premium_LRS"
	StorageAccountTypesPremiumV2LRS   StorageAccountTypes = "PremiumV2_LRS"
	StorageAccountTypesPremiumZRS     StorageAccountTypes = "Premium_ZRS"
	StorageAccountTypesStandardLRS    StorageAccountTypes = "Standard_LRS"
	StorageAccountTypesStandardSSDLRS StorageAccountTypes = "StandardSSD_LRS"
	StorageAccountTypesStandardSSDZRS StorageAccountTypes = "StandardSSD_ZRS"
	StorageAccountTypesUltraSSDLRS    StorageAccountTypes = "UltraSSD_LRS"
)

// PossibleStorageAccountTypesValues returns the possible values for the StorageAccountTypes const type.
func PossibleStorageAccountTypesValues() []StorageAccountTypes {
	return []StorageAccountTypes{
		StorageAccountTypesPremiumLRS,
		StorageAccountTypesPremiumV2LRS,
		StorageAccountTypesPremiumZRS,
		StorageAccountTypesStandardLRS,
		StorageAccountTypesStandardSSDLRS,
		StorageAccountTypesStandardSSDZRS,
		StorageAccountTypesUltraSSDLRS,
	}
}

// UefiKeyType - The type of key signature.
type UefiKeyType string

const (
	UefiKeyTypeSHA256 UefiKeyType = "sha256"
	UefiKeyTypeX509   UefiKeyType = "x509"
)

// PossibleUefiKeyTypeValues returns the possible values for the UefiKeyType const type.
func PossibleUefiKeyTypeValues() []UefiKeyType {
	return []UefiKeyType{
		UefiKeyTypeSHA256,
		UefiKeyTypeX509,
	}
}

// UefiSignatureTemplateName - The name of the signature template that contains default UEFI keys.
type UefiSignatureTemplateName string

const (
	UefiSignatureTemplateNameMicrosoftUefiCertificateAuthorityTemplate UefiSignatureTemplateName = "MicrosoftUefiCertificateAuthorityTemplate"
	UefiSignatureTemplateNameMicrosoftWindowsTemplate                  UefiSignatureTemplateName = "MicrosoftWindowsTemplate"
	UefiSignatureTemplateNameNoSignatureTemplate                       UefiSignatureTemplateName = "NoSignatureTemplate"
)

// PossibleUefiSignatureTemplateNameValues returns the possible values for the UefiSignatureTemplateName const type.
func PossibleUefiSignatureTemplateNameValues() []UefiSignatureTemplateName {
	return []UefiSignatureTemplateName{
		UefiSignatureTemplateNameMicrosoftUefiCertificateAuthorityTemplate,
		UefiSignatureTemplateNameMicrosoftWindowsTemplate,
		UefiSignatureTemplateNameNoSignatureTemplate,
	}
}

// UpgradeMode - Specifies the mode of an upgrade to virtual machines in the scale set.
// Possible values are:
// Manual - You control the application of updates to virtual machines in the scale set. You do this by using the manualUpgrade
// action.
// Automatic - All virtual machines in the scale set are automatically updated at the same time.
type UpgradeMode string

const (
	UpgradeModeAutomatic UpgradeMode = "Automatic"
	UpgradeModeManual    UpgradeMode = "Manual"
	UpgradeModeRolling   UpgradeMode = "Rolling"
)

// PossibleUpgradeModeValues returns the possible values for the UpgradeMode const type.
func PossibleUpgradeModeValues() []UpgradeMode {
	return []UpgradeMode{
		UpgradeModeAutomatic,
		UpgradeModeManual,
		UpgradeModeRolling,
	}
}

// UpgradeOperationInvoker - Invoker of the Upgrade Operation
type UpgradeOperationInvoker string

const (
	UpgradeOperationInvokerPlatform UpgradeOperationInvoker = "Platform"
	UpgradeOperationInvokerUnknown  UpgradeOperationInvoker = "Unknown"
	UpgradeOperationInvokerUser     UpgradeOperationInvoker = "User"
)

// PossibleUpgradeOperationInvokerValues returns the possible values for the UpgradeOperationInvoker const type.
func PossibleUpgradeOperationInvokerValues() []UpgradeOperationInvoker {
	return []UpgradeOperationInvoker{
		UpgradeOperationInvokerPlatform,
		UpgradeOperationInvokerUnknown,
		UpgradeOperationInvokerUser,
	}
}

// UpgradeState - Code indicating the current status of the upgrade.
type UpgradeState string

const (
	UpgradeStateCancelled      UpgradeState = "Cancelled"
	UpgradeStateCompleted      UpgradeState = "Completed"
	UpgradeStateFaulted        UpgradeState = "Faulted"
	UpgradeStateRollingForward UpgradeState = "RollingForward"
)

// PossibleUpgradeStateValues returns the possible values for the UpgradeState const type.
func PossibleUpgradeStateValues() []UpgradeState {
	return []UpgradeState{
		UpgradeStateCancelled,
		UpgradeStateCompleted,
		UpgradeStateFaulted,
		UpgradeStateRollingForward,
	}
}

// VMDiskTypes - VM disk types which are disallowed.
type VMDiskTypes string

const (
	VMDiskTypesNone      VMDiskTypes = "None"
	VMDiskTypesUnmanaged VMDiskTypes = "Unmanaged"
)

// PossibleVMDiskTypesValues returns the possible values for the VMDiskTypes const type.
func PossibleVMDiskTypesValues() []VMDiskTypes {
	return []VMDiskTypes{
		VMDiskTypesNone,
		VMDiskTypesUnmanaged,
	}
}

type VMGuestPatchClassificationLinux string

const (
	VMGuestPatchClassificationLinuxCritical VMGuestPatchClassificationLinux = "Critical"
	VMGuestPatchClassificationLinuxOther    VMGuestPatchClassificationLinux = "Other"
	VMGuestPatchClassificationLinuxSecurity VMGuestPatchClassificationLinux = "Security"
)

// PossibleVMGuestPatchClassificationLinuxValues returns the possible values for the VMGuestPatchClassificationLinux const type.
func PossibleVMGuestPatchClassificationLinuxValues() []VMGuestPatchClassificationLinux {
	return []VMGuestPatchClassificationLinux{
		VMGuestPatchClassificationLinuxCritical,
		VMGuestPatchClassificationLinuxOther,
		VMGuestPatchClassificationLinuxSecurity,
	}
}

type VMGuestPatchClassificationWindows string

const (
	VMGuestPatchClassificationWindowsCritical     VMGuestPatchClassificationWindows = "Critical"
	VMGuestPatchClassificationWindowsDefinition   VMGuestPatchClassificationWindows = "Definition"
	VMGuestPatchClassificationWindowsFeaturePack  VMGuestPatchClassificationWindows = "FeaturePack"
	VMGuestPatchClassificationWindowsSecurity     VMGuestPatchClassificationWindows = "Security"
	VMGuestPatchClassificationWindowsServicePack  VMGuestPatchClassificationWindows = "ServicePack"
	VMGuestPatchClassificationWindowsTools        VMGuestPatchClassificationWindows = "Tools"
	VMGuestPatchClassificationWindowsUpdateRollUp VMGuestPatchClassificationWindows = "UpdateRollUp"
	VMGuestPatchClassificationWindowsUpdates      VMGuestPatchClassificationWindows = "Updates"
)

// PossibleVMGuestPatchClassificationWindowsValues returns the possible values for the VMGuestPatchClassificationWindows const type.
func PossibleVMGuestPatchClassificationWindowsValues() []VMGuestPatchClassificationWindows {
	return []VMGuestPatchClassificationWindows{
		VMGuestPatchClassificationWindowsCritical,
		VMGuestPatchClassificationWindowsDefinition,
		VMGuestPatchClassificationWindowsFeaturePack,
		VMGuestPatchClassificationWindowsSecurity,
		VMGuestPatchClassificationWindowsServicePack,
		VMGuestPatchClassificationWindowsTools,
		VMGuestPatchClassificationWindowsUpdateRollUp,
		VMGuestPatchClassificationWindowsUpdates,
	}
}

// VMGuestPatchRebootBehavior - Describes the reboot requirements of the patch.
type VMGuestPatchRebootBehavior string

const (
	VMGuestPatchRebootBehaviorAlwaysRequiresReboot VMGuestPatchRebootBehavior = "AlwaysRequiresReboot"
	VMGuestPatchRebootBehaviorCanRequestReboot     VMGuestPatchRebootBehavior = "CanRequestReboot"
	VMGuestPatchRebootBehaviorNeverReboots         VMGuestPatchRebootBehavior = "NeverReboots"
	VMGuestPatchRebootBehaviorUnknown              VMGuestPatchRebootBehavior = "Unknown"
)

// PossibleVMGuestPatchRebootBehaviorValues returns the possible values for the VMGuestPatchRebootBehavior const type.
func PossibleVMGuestPatchRebootBehaviorValues() []VMGuestPatchRebootBehavior {
	return []VMGuestPatchRebootBehavior{
		VMGuestPatchRebootBehaviorAlwaysRequiresReboot,
		VMGuestPatchRebootBehaviorCanRequestReboot,
		VMGuestPatchRebootBehaviorNeverReboots,
		VMGuestPatchRebootBehaviorUnknown,
	}
}

// VMGuestPatchRebootSetting - Defines when it is acceptable to reboot a VM during a software update operation.
type VMGuestPatchRebootSetting string

const (
	VMGuestPatchRebootSettingAlways     VMGuestPatchRebootSetting = "Always"
	VMGuestPatchRebootSettingIfRequired VMGuestPatchRebootSetting = "IfRequired"
	VMGuestPatchRebootSettingNever      VMGuestPatchRebootSetting = "Never"
)

// PossibleVMGuestPatchRebootSettingValues returns the possible values for the VMGuestPatchRebootSetting const type.
func PossibleVMGuestPatchRebootSettingValues() []VMGuestPatchRebootSetting {
	return []VMGuestPatchRebootSetting{
		VMGuestPatchRebootSettingAlways,
		VMGuestPatchRebootSettingIfRequired,
		VMGuestPatchRebootSettingNever,
	}
}

// VMGuestPatchRebootStatus - The reboot state of the VM following completion of the operation.
type VMGuestPatchRebootStatus string

const (
	VMGuestPatchRebootStatusCompleted VMGuestPatchRebootStatus = "Completed"
	VMGuestPatchRebootStatusFailed    VMGuestPatchRebootStatus = "Failed"
	VMGuestPatchRebootStatusNotNeeded VMGuestPatchRebootStatus = "NotNeeded"
	VMGuestPatchRebootStatusRequired  VMGuestPatchRebootStatus = "Required"
	VMGuestPatchRebootStatusStarted   VMGuestPatchRebootStatus = "Started"
	VMGuestPatchRebootStatusUnknown   VMGuestPatchRebootStatus = "Unknown"
)

// PossibleVMGuestPatchRebootStatusValues returns the possible values for the VMGuestPatchRebootStatus const type.
func PossibleVMGuestPatchRebootStatusValues() []VMGuestPatchRebootStatus {
	return []VMGuestPatchRebootStatus{
		VMGuestPatchRebootStatusCompleted,
		VMGuestPatchRebootStatusFailed,
		VMGuestPatchRebootStatusNotNeeded,
		VMGuestPatchRebootStatusRequired,
		VMGuestPatchRebootStatusStarted,
		VMGuestPatchRebootStatusUnknown,
	}
}

// VirtualMachineEvictionPolicyTypes - Specifies the eviction policy for the Azure Spot VM/VMSS
type VirtualMachineEvictionPolicyTypes string

const (
	VirtualMachineEvictionPolicyTypesDeallocate VirtualMachineEvictionPolicyTypes = "Deallocate"
	VirtualMachineEvictionPolicyTypesDelete     VirtualMachineEvictionPolicyTypes = "Delete"
)

// PossibleVirtualMachineEvictionPolicyTypesValues returns the possible values for the VirtualMachineEvictionPolicyTypes const type.
func PossibleVirtualMachineEvictionPolicyTypesValues() []VirtualMachineEvictionPolicyTypes {
	return []VirtualMachineEvictionPolicyTypes{
		VirtualMachineEvictionPolicyTypesDeallocate,
		VirtualMachineEvictionPolicyTypesDelete,
	}
}

// VirtualMachinePriorityTypes - Specifies the priority for a standalone virtual machine or the virtual machines in the scale
// set. 'Low' enum will be deprecated in the future, please use 'Spot' as the enum to deploy Azure Spot
// VM/VMSS.
type VirtualMachinePriorityTypes string

const (
	VirtualMachinePriorityTypesLow     VirtualMachinePriorityTypes = "Low"
	VirtualMachinePriorityTypesRegular VirtualMachinePriorityTypes = "Regular"
	VirtualMachinePriorityTypesSpot    VirtualMachinePriorityTypes = "Spot"
)

// PossibleVirtualMachinePriorityTypesValues returns the possible values for the VirtualMachinePriorityTypes const type.
func PossibleVirtualMachinePriorityTypesValues() []VirtualMachinePriorityTypes {
	return []VirtualMachinePriorityTypes{
		VirtualMachinePriorityTypesLow,
		VirtualMachinePriorityTypesRegular,
		VirtualMachinePriorityTypesSpot,
	}
}

// VirtualMachineScaleSetSKUScaleType - The scale type applicable to the sku.
type VirtualMachineScaleSetSKUScaleType string

const (
	VirtualMachineScaleSetSKUScaleTypeAutomatic VirtualMachineScaleSetSKUScaleType = "Automatic"
	VirtualMachineScaleSetSKUScaleTypeNone      VirtualMachineScaleSetSKUScaleType = "None"
)

// PossibleVirtualMachineScaleSetSKUScaleTypeValues returns the possible values for the VirtualMachineScaleSetSKUScaleType const type.
func PossibleVirtualMachineScaleSetSKUScaleTypeValues() []VirtualMachineScaleSetSKUScaleType {
	return []VirtualMachineScaleSetSKUScaleType{
		VirtualMachineScaleSetSKUScaleTypeAutomatic,
		VirtualMachineScaleSetSKUScaleTypeNone,
	}
}

type VirtualMachineScaleSetScaleInRules string

const (
	VirtualMachineScaleSetScaleInRulesDefault  VirtualMachineScaleSetScaleInRules = "Default"
	VirtualMachineScaleSetScaleInRulesNewestVM VirtualMachineScaleSetScaleInRules = "NewestVM"
	VirtualMachineScaleSetScaleInRulesOldestVM VirtualMachineScaleSetScaleInRules = "OldestVM"
)

// PossibleVirtualMachineScaleSetScaleInRulesValues returns the possible values for the VirtualMachineScaleSetScaleInRules const type.
func PossibleVirtualMachineScaleSetScaleInRulesValues() []VirtualMachineScaleSetScaleInRules {
	return []VirtualMachineScaleSetScaleInRules{
		VirtualMachineScaleSetScaleInRulesDefault,
		VirtualMachineScaleSetScaleInRulesNewestVM,
		VirtualMachineScaleSetScaleInRulesOldestVM,
	}
}

// VirtualMachineSizeTypes - Specifies the size of the virtual machine. The enum data type is currently deprecated and will
// be removed by December 23rd 2023. The recommended way to get the list of available sizes is using these
// APIs: List all available virtual machine sizes in an availability set [https://docs.microsoft.com/rest/api/compute/availabilitysets/listavailablesizes],
// List all available virtual machine sizes in a
// region [https://docs.microsoft.com/rest/api/compute/resourceskus/list], List all available virtual machine sizes for resizing
// [https://docs.microsoft.com/rest/api/compute/virtualmachines/listavailablesizes]. For more information about virtual machine
// sizes, see Sizes for virtual machines
// [https://docs.microsoft.com/azure/virtual-machines/sizes]. The available VM sizes depend on region and availability set.
type VirtualMachineSizeTypes string

const (
	VirtualMachineSizeTypesBasicA0          VirtualMachineSizeTypes = "Basic_A0"
	VirtualMachineSizeTypesBasicA1          VirtualMachineSizeTypes = "Basic_A1"
	VirtualMachineSizeTypesBasicA2          VirtualMachineSizeTypes = "Basic_A2"
	VirtualMachineSizeTypesBasicA3          VirtualMachineSizeTypes = "Basic_A3"
	VirtualMachineSizeTypesBasicA4          VirtualMachineSizeTypes = "Basic_A4"
	VirtualMachineSizeTypesStandardA0       VirtualMachineSizeTypes = "Standard_A0"
	VirtualMachineSizeTypesStandardA1       VirtualMachineSizeTypes = "Standard_A1"
	VirtualMachineSizeTypesStandardA10      VirtualMachineSizeTypes = "Standard_A10"
	VirtualMachineSizeTypesStandardA11      VirtualMachineSizeTypes = "Standard_A11"
	VirtualMachineSizeTypesStandardA1V2     VirtualMachineSizeTypes = "Standard_A1_v2"
	VirtualMachineSizeTypesStandardA2       VirtualMachineSizeTypes = "Standard_A2"
	VirtualMachineSizeTypesStandardA2MV2    VirtualMachineSizeTypes = "Standard_A2m_v2"
	VirtualMachineSizeTypesStandardA2V2     VirtualMachineSizeTypes = "Standard_A2_v2"
	VirtualMachineSizeTypesStandardA3       VirtualMachineSizeTypes = "Standard_A3"
	VirtualMachineSizeTypesStandardA4       VirtualMachineSizeTypes = "Standard_A4"
	VirtualMachineSizeTypesStandardA4MV2    VirtualMachineSizeTypes = "Standard_A4m_v2"
	VirtualMachineSizeTypesStandardA4V2     VirtualMachineSizeTypes = "Standard_A4_v2"
	VirtualMachineSizeTypesStandardA5       VirtualMachineSizeTypes = "Standard_A5"
	VirtualMachineSizeTypesStandardA6       VirtualMachineSizeTypes = "Standard_A6"
	VirtualMachineSizeTypesStandardA7       VirtualMachineSizeTypes = "Standard_A7"
	VirtualMachineSizeTypesStandardA8       VirtualMachineSizeTypes = "Standard_A8"
	VirtualMachineSizeTypesStandardA8MV2    VirtualMachineSizeTypes = "Standard_A8m_v2"
	VirtualMachineSizeTypesStandardA8V2     VirtualMachineSizeTypes = "Standard_A8_v2"
	VirtualMachineSizeTypesStandardA9       VirtualMachineSizeTypes = "Standard_A9"
	VirtualMachineSizeTypesStandardB1Ms     VirtualMachineSizeTypes = "Standard_B1ms"
	VirtualMachineSizeTypesStandardB1S      VirtualMachineSizeTypes = "Standard_B1s"
	VirtualMachineSizeTypesStandardB2Ms     VirtualMachineSizeTypes = "Standard_B2ms"
	VirtualMachineSizeTypesStandardB2S      VirtualMachineSizeTypes = "Standard_B2s"
	VirtualMachineSizeTypesStandardB4Ms     VirtualMachineSizeTypes = "Standard_B4ms"
	VirtualMachineSizeTypesStandardB8Ms     VirtualMachineSizeTypes = "Standard_B8ms"
	VirtualMachineSizeTypesStandardD1       VirtualMachineSizeTypes = "Standard_D1"
	VirtualMachineSizeTypesStandardD11      VirtualMachineSizeTypes = "Standard_D11"
	VirtualMachineSizeTypesStandardD11V2    VirtualMachineSizeTypes = "Standard_D11_v2"
	VirtualMachineSizeTypesStandardD12      VirtualMachineSizeTypes = "Standard_D12"
	VirtualMachineSizeTypesStandardD12V2    VirtualMachineSizeTypes = "Standard_D12_v2"
	VirtualMachineSizeTypesStandardD13      VirtualMachineSizeTypes = "Standard_D13"
	VirtualMachineSizeTypesStandardD13V2    VirtualMachineSizeTypes = "Standard_D13_v2"
	VirtualMachineSizeTypesStandardD14      VirtualMachineSizeTypes = "Standard_D14"
	VirtualMachineSizeTypesStandardD14V2    VirtualMachineSizeTypes = "Standard_D14_v2"
	VirtualMachineSizeTypesStandardD15V2    VirtualMachineSizeTypes = "Standard_D15_v2"
	VirtualMachineSizeTypesStandardD16SV3   VirtualMachineSizeTypes = "Standard_D16s_v3"
	VirtualMachineSizeTypesStandardD16V3    VirtualMachineSizeTypes = "Standard_D16_v3"
	VirtualMachineSizeTypesStandardD1V2     VirtualMachineSizeTypes = "Standard_D1_v2"
	VirtualMachineSizeTypesStandardD2       VirtualMachineSizeTypes = "Standard_D2"
	VirtualMachineSizeTypesStandardD2SV3    VirtualMachineSizeTypes = "Standard_D2s_v3"
	VirtualMachineSizeTypesStandardD2V2     VirtualMachineSizeTypes = "Standard_D2_v2"
	VirtualMachineSizeTypesStandardD2V3     VirtualMachineSizeTypes = "Standard_D2_v3"
	VirtualMachineSizeTypesStandardD3       VirtualMachineSizeTypes = "Standard_D3"
	VirtualMachineSizeTypesStandardD32SV3   VirtualMachineSizeTypes = "Standard_D32s_v3"
	VirtualMachineSizeTypesStandardD32V3    VirtualMachineSizeTypes = "Standard_D32_v3"
	VirtualMachineSizeTypesStandardD3V2     VirtualMachineSizeTypes = "Standard_D3_v2"
	VirtualMachineSizeTypesStandardD4       VirtualMachineSizeTypes = "Standard_D4"
	VirtualMachineSizeTypesStandardD4SV3    VirtualMachineSizeTypes = "Standard_D4s_v3"
	VirtualMachineSizeTypesStandardD4V2     VirtualMachineSizeTypes = "Standard_D4_v2"
	VirtualMachineSizeTypesStandardD4V3     VirtualMachineSizeTypes = "Standard_D4_v3"
	VirtualMachineSizeTypesStandardD5V2     VirtualMachineSizeTypes = "Standard_D5_v2"
	VirtualMachineSizeTypesStandardD64SV3   VirtualMachineSizeTypes = "Standard_D64s_v3"
	VirtualMachineSizeTypesStandardD64V3    VirtualMachineSizeTypes = "Standard_D64_v3"
	VirtualMachineSizeTypesStandardD8SV3    VirtualMachineSizeTypes = "Standard_D8s_v3"
	VirtualMachineSizeTypesStandardD8V3     VirtualMachineSizeTypes = "Standard_D8_v3"
	VirtualMachineSizeTypesStandardDS1      VirtualMachineSizeTypes = "Standard_DS1"
	VirtualMachineSizeTypesStandardDS11     VirtualMachineSizeTypes = "Standard_DS11"
	VirtualMachineSizeTypesStandardDS11V2   VirtualMachineSizeTypes = "Standard_DS11_v2"
	VirtualMachineSizeTypesStandardDS12     VirtualMachineSizeTypes = "Standard_DS12"
	VirtualMachineSizeTypesStandardDS12V2   VirtualMachineSizeTypes = "Standard_DS12_v2"
	VirtualMachineSizeTypesStandardDS13     VirtualMachineSizeTypes = "Standard_DS13"
	VirtualMachineSizeTypesStandardDS132V2  VirtualMachineSizeTypes = "Standard_DS13-2_v2"
	VirtualMachineSizeTypesStandardDS134V2  VirtualMachineSizeTypes = "Standard_DS13-4_v2"
	VirtualMachineSizeTypesStandardDS13V2   VirtualMachineSizeTypes = "Standard_DS13_v2"
	VirtualMachineSizeTypesStandardDS14     VirtualMachineSizeTypes = "Standard_DS14"
	VirtualMachineSizeTypesStandardDS144V2  VirtualMachineSizeTypes = "Standard_DS14-4_v2"
	VirtualMachineSizeTypesStandardDS148V2  VirtualMachineSizeTypes = "Standard_DS14-8_v2"
	VirtualMachineSizeTypesStandardDS14V2   VirtualMachineSizeTypes = "Standard_DS14_v2"
	VirtualMachineSizeTypesStandardDS15V2   VirtualMachineSizeTypes = "Standard_DS15_v2"
	VirtualMachineSizeTypesStandardDS1V2    VirtualMachineSizeTypes = "Standard_DS1_v2"
	VirtualMachineSizeTypesStandardDS2      VirtualMachineSizeTypes = "Standard_DS2"
	VirtualMachineSizeTypesStandardDS2V2    VirtualMachineSizeTypes = "Standard_DS2_v2"
	VirtualMachineSizeTypesStandardDS3      VirtualMachineSizeTypes = "Standard_DS3"
	VirtualMachineSizeTypesStandardDS3V2    VirtualMachineSizeTypes = "Standard_DS3_v2"
	VirtualMachineSizeTypesStandardDS4      VirtualMachineSizeTypes = "Standard_DS4"
	VirtualMachineSizeTypesStandardDS4V2    VirtualMachineSizeTypes = "Standard_DS4_v2"
	VirtualMachineSizeTypesStandardDS5V2    VirtualMachineSizeTypes = "Standard_DS5_v2"
	VirtualMachineSizeTypesStandardE16SV3   VirtualMachineSizeTypes = "Standard_E16s_v3"
	VirtualMachineSizeTypesStandardE16V3    VirtualMachineSizeTypes = "Standard_E16_v3"
	VirtualMachineSizeTypesStandardE2SV3    VirtualMachineSizeTypes = "Standard_E2s_v3"
	VirtualMachineSizeTypesStandardE2V3     VirtualMachineSizeTypes = "Standard_E2_v3"
	VirtualMachineSizeTypesStandardE3216V3  VirtualMachineSizeTypes = "Standard_E32-16_v3"
	VirtualMachineSizeTypesStandardE328SV3  VirtualMachineSizeTypes = "Standard_E32-8s_v3"
	VirtualMachineSizeTypesStandardE32SV3   VirtualMachineSizeTypes = "Standard_E32s_v3"
	VirtualMachineSizeTypesStandardE32V3    VirtualMachineSizeTypes = "Standard_E32_v3"
	VirtualMachineSizeTypesStandardE4SV3    VirtualMachineSizeTypes = "Standard_E4s_v3"
	VirtualMachineSizeTypesStandardE4V3     VirtualMachineSizeTypes = "Standard_E4_v3"
	VirtualMachineSizeTypesStandardE6416SV3 VirtualMachineSizeTypes = "Standard_E64-16s_v3"
	VirtualMachineSizeTypesStandardE6432SV3 VirtualMachineSizeTypes = "Standard_E64-32s_v3"
	VirtualMachineSizeTypesStandardE64SV3   VirtualMachineSizeTypes = "Standard_E64s_v3"
	VirtualMachineSizeTypesStandardE64V3    VirtualMachineSizeTypes = "Standard_E64_v3"
	VirtualMachineSizeTypesStandardE8SV3    VirtualMachineSizeTypes = "Standard_E8s_v3"
	VirtualMachineSizeTypesStandardE8V3     VirtualMachineSizeTypes = "Standard_E8_v3"
	VirtualMachineSizeTypesStandardF1       VirtualMachineSizeTypes = "Standard_F1"
	VirtualMachineSizeTypesStandardF16      VirtualMachineSizeTypes = "Standard_F16"
	VirtualMachineSizeTypesStandardF16S     VirtualMachineSizeTypes = "Standard_F16s"
	VirtualMachineSizeTypesStandardF16SV2   VirtualMachineSizeTypes = "Standard_F16s_v2"
	VirtualMachineSizeTypesStandardF1S      VirtualMachineSizeTypes = "Standard_F1s"
	VirtualMachineSizeTypesStandardF2       VirtualMachineSizeTypes = "Standard_F2"
	VirtualMachineSizeTypesStandardF2S      VirtualMachineSizeTypes = "Standard_F2s"
	VirtualMachineSizeTypesStandardF2SV2    VirtualMachineSizeTypes = "Standard_F2s_v2"
	VirtualMachineSizeTypesStandardF32SV2   VirtualMachineSizeTypes = "Standard_F32s_v2"
	VirtualMachineSizeTypesStandardF4       VirtualMachineSizeTypes = "Standard_F4"
	VirtualMachineSizeTypesStandardF4S      VirtualMachineSizeTypes = "Standard_F4s"
	VirtualMachineSizeTypesStandardF4SV2    VirtualMachineSizeTypes = "Standard_F4s_v2"
	VirtualMachineSizeTypesStandardF64SV2   VirtualMachineSizeTypes = "Standard_F64s_v2"
	VirtualMachineSizeTypesStandardF72SV2   VirtualMachineSizeTypes = "Standard_F72s_v2"
	VirtualMachineSizeTypesStandardF8       VirtualMachineSizeTypes = "Standard_F8"
	VirtualMachineSizeTypesStandardF8S      VirtualMachineSizeTypes = "Standard_F8s"
	VirtualMachineSizeTypesStandardF8SV2    VirtualMachineSizeTypes = "Standard_F8s_v2"
	VirtualMachineSizeTypesStandardG1       VirtualMachineSizeTypes = "Standard_G1"
	VirtualMachineSizeTypesStandardG2       VirtualMachineSizeTypes = "Standard_G2"
	VirtualMachineSizeTypesStandardG3       VirtualMachineSizeTypes = "Standard_G3"
	VirtualMachineSizeTypesStandardG4       VirtualMachineSizeTypes = "Standard_G4"
	VirtualMachineSizeTypesStandardG5       VirtualMachineSizeTypes = "Standard_G5"
	VirtualMachineSizeTypesStandardGS1      VirtualMachineSizeTypes = "Standard_GS1"
	VirtualMachineSizeTypesStandardGS2      VirtualMachineSizeTypes = "Standard_GS2"
	VirtualMachineSizeTypesStandardGS3      VirtualMachineSizeTypes = "Standard_GS3"
	VirtualMachineSizeTypesStandardGS4      VirtualMachineSizeTypes = "Standard_GS4"
	VirtualMachineSizeTypesStandardGS44     VirtualMachineSizeTypes = "Standard_GS4-4"
	VirtualMachineSizeTypesStandardGS48     VirtualMachineSizeTypes = "Standard_GS4-8"
	VirtualMachineSizeTypesStandardGS5      VirtualMachineSizeTypes = "Standard_GS5"
	VirtualMachineSizeTypesStandardGS516    VirtualMachineSizeTypes = "Standard_GS5-16"
	VirtualMachineSizeTypesStandardGS58     VirtualMachineSizeTypes = "Standard_GS5-8"
	VirtualMachineSizeTypesStandardH16      VirtualMachineSizeTypes = "Standard_H16"
	VirtualMachineSizeTypesStandardH16M     VirtualMachineSizeTypes = "Standard_H16m"
	VirtualMachineSizeTypesStandardH16Mr    VirtualMachineSizeTypes = "Standard_H16mr"
	VirtualMachineSizeTypesStandardH16R     VirtualMachineSizeTypes = "Standard_H16r"
	VirtualMachineSizeTypesStandardH8       VirtualMachineSizeTypes = "Standard_H8"
	VirtualMachineSizeTypesStandardH8M      VirtualMachineSizeTypes = "Standard_H8m"
	VirtualMachineSizeTypesStandardL16S     VirtualMachineSizeTypes = "Standard_L16s"
	VirtualMachineSizeTypesStandardL32S     VirtualMachineSizeTypes = "Standard_L32s"
	VirtualMachineSizeTypesStandardL4S      VirtualMachineSizeTypes = "Standard_L4s"
	VirtualMachineSizeTypesStandardL8S      VirtualMachineSizeTypes = "Standard_L8s"
	VirtualMachineSizeTypesStandardM12832Ms VirtualMachineSizeTypes = "Standard_M128-32ms"
	VirtualMachineSizeTypesStandardM12864Ms VirtualMachineSizeTypes = "Standard_M128-64ms"
	VirtualMachineSizeTypesStandardM128Ms   VirtualMachineSizeTypes = "Standard_M128ms"
	VirtualMachineSizeTypesStandardM128S    VirtualMachineSizeTypes = "Standard_M128s"
	VirtualMachineSizeTypesStandardM6416Ms  VirtualMachineSizeTypes = "Standard_M64-16ms"
	VirtualMachineSizeTypesStandardM6432Ms  VirtualMachineSizeTypes = "Standard_M64-32ms"
	VirtualMachineSizeTypesStandardM64Ms    VirtualMachineSizeTypes = "Standard_M64ms"
	VirtualMachineSizeTypesStandardM64S     VirtualMachineSizeTypes = "Standard_M64s"
	VirtualMachineSizeTypesStandardNC12     VirtualMachineSizeTypes = "Standard_NC12"
	VirtualMachineSizeTypesStandardNC12SV2  VirtualMachineSizeTypes = "Standard_NC12s_v2"
	VirtualMachineSizeTypesStandardNC12SV3  VirtualMachineSizeTypes = "Standard_NC12s_v3"
	VirtualMachineSizeTypesStandardNC24     VirtualMachineSizeTypes = "Standard_NC24"
	VirtualMachineSizeTypesStandardNC24R    VirtualMachineSizeTypes = "Standard_NC24r"
	VirtualMachineSizeTypesStandardNC24RsV2 VirtualMachineSizeTypes = "Standard_NC24rs_v2"
	VirtualMachineSizeTypesStandardNC24RsV3 VirtualMachineSizeTypes = "Standard_NC24rs_v3"
	VirtualMachineSizeTypesStandardNC24SV2  VirtualMachineSizeTypes = "Standard_NC24s_v2"
	VirtualMachineSizeTypesStandardNC24SV3  VirtualMachineSizeTypes = "Standard_NC24s_v3"
	VirtualMachineSizeTypesStandardNC6      VirtualMachineSizeTypes = "Standard_NC6"
	VirtualMachineSizeTypesStandardNC6SV2   VirtualMachineSizeTypes = "Standard_NC6s_v2"
	VirtualMachineSizeTypesStandardNC6SV3   VirtualMachineSizeTypes = "Standard_NC6s_v3"
	VirtualMachineSizeTypesStandardND12S    VirtualMachineSizeTypes = "Standard_ND12s"
	VirtualMachineSizeTypesStandardND24Rs   VirtualMachineSizeTypes = "Standard_ND24rs"
	VirtualMachineSizeTypesStandardND24S    VirtualMachineSizeTypes = "Standard_ND24s"
	VirtualMachineSizeTypesStandardND6S     VirtualMachineSizeTypes = "Standard_ND6s"
	VirtualMachineSizeTypesStandardNV12     VirtualMachineSizeTypes = "Standard_NV12"
	VirtualMachineSizeTypesStandardNV24     VirtualMachineSizeTypes = "Standard_NV24"
	VirtualMachineSizeTypesStandardNV6      VirtualMachineSizeTypes = "Standard_NV6"
)

// PossibleVirtualMachineSizeTypesValues returns the possible values for the VirtualMachineSizeTypes const type.
func PossibleVirtualMachineSizeTypesValues() []VirtualMachineSizeTypes {
	return []VirtualMachineSizeTypes{
		VirtualMachineSizeTypesBasicA0,
		VirtualMachineSizeTypesBasicA1,
		VirtualMachineSizeTypesBasicA2,
		VirtualMachineSizeTypesBasicA3,
		VirtualMachineSizeTypesBasicA4,
		VirtualMachineSizeTypesStandardA0,
		VirtualMachineSizeTypesStandardA1,
		VirtualMachineSizeTypesStandardA10,
		VirtualMachineSizeTypesStandardA11,
		VirtualMachineSizeTypesStandardA1V2,
		VirtualMachineSizeTypesStandardA2,
		VirtualMachineSizeTypesStandardA2MV2,
		VirtualMachineSizeTypesStandardA2V2,
		VirtualMachineSizeTypesStandardA3,
		VirtualMachineSizeTypesStandardA4,
		VirtualMachineSizeTypesStandardA4MV2,
		VirtualMachineSizeTypesStandardA4V2,
		VirtualMachineSizeTypesStandardA5,
		VirtualMachineSizeTypesStandardA6,
		VirtualMachineSizeTypesStandardA7,
		VirtualMachineSizeTypesStandardA8,
		VirtualMachineSizeTypesStandardA8MV2,
		VirtualMachineSizeTypesStandardA8V2,
		VirtualMachineSizeTypesStandardA9,
		VirtualMachineSizeTypesStandardB1Ms,
		VirtualMachineSizeTypesStandardB1S,
		VirtualMachineSizeTypesStandardB2Ms,
		VirtualMachineSizeTypesStandardB2S,
		VirtualMachineSizeTypesStandardB4Ms,
		VirtualMachineSizeTypesStandardB8Ms,
		VirtualMachineSizeTypesStandardD1,
		VirtualMachineSizeTypesStandardD11,
		VirtualMachineSizeTypesStandardD11V2,
		VirtualMachineSizeTypesStandardD12,
		VirtualMachineSizeTypesStandardD12V2,
		VirtualMachineSizeTypesStandardD13,
		VirtualMachineSizeTypesStandardD13V2,
		VirtualMachineSizeTypesStandardD14,
		VirtualMachineSizeTypesStandardD14V2,
		VirtualMachineSizeTypesStandardD15V2,
		VirtualMachineSizeTypesStandardD16SV3,
		VirtualMachineSizeTypesStandardD16V3,
		VirtualMachineSizeTypesStandardD1V2,
		VirtualMachineSizeTypesStandardD2,
		VirtualMachineSizeTypesStandardD2SV3,
		VirtualMachineSizeTypesStandardD2V2,
		VirtualMachineSizeTypesStandardD2V3,
		VirtualMachineSizeTypesStandardD3,
		VirtualMachineSizeTypesStandardD32SV3,
		VirtualMachineSizeTypesStandardD32V3,
		VirtualMachineSizeTypesStandardD3V2,
		VirtualMachineSizeTypesStandardD4,
		VirtualMachineSizeTypesStandardD4SV3,
		VirtualMachineSizeTypesStandardD4V2,
		VirtualMachineSizeTypesStandardD4V3,
		VirtualMachineSizeTypesStandardD5V2,
		VirtualMachineSizeTypesStandardD64SV3,
		VirtualMachineSizeTypesStandardD64V3,
		VirtualMachineSizeTypesStandardD8SV3,
		VirtualMachineSizeTypesStandardD8V3,
		VirtualMachineSizeTypesStandardDS1,
		VirtualMachineSizeTypesStandardDS11,
		VirtualMachineSizeTypesStandardDS11V2,
		VirtualMachineSizeTypesStandardDS12,
		VirtualMachineSizeTypesStandardDS12V2,
		VirtualMachineSizeTypesStandardDS13,
		VirtualMachineSizeTypesStandardDS132V2,
		VirtualMachineSizeTypesStandardDS134V2,
		VirtualMachineSizeTypesStandardDS13V2,
		VirtualMachineSizeTypesStandardDS14,
		VirtualMachineSizeTypesStandardDS144V2,
		VirtualMachineSizeTypesStandardDS148V2,
		VirtualMachineSizeTypesStandardDS14V2,
		VirtualMachineSizeTypesStandardDS15V2,
		VirtualMachineSizeTypesStandardDS1V2,
		VirtualMachineSizeTypesStandardDS2,
		VirtualMachineSizeTypesStandardDS2V2,
		VirtualMachineSizeTypesStandardDS3,
		VirtualMachineSizeTypesStandardDS3V2,
		VirtualMachineSizeTypesStandardDS4,
		VirtualMachineSizeTypesStandardDS4V2,
		VirtualMachineSizeTypesStandardDS5V2,
		VirtualMachineSizeTypesStandardE16SV3,
		VirtualMachineSizeTypesStandardE16V3,
		VirtualMachineSizeTypesStandardE2SV3,
		VirtualMachineSizeTypesStandardE2V3,
		VirtualMachineSizeTypesStandardE3216V3,
		VirtualMachineSizeTypesStandardE328SV3,
		VirtualMachineSizeTypesStandardE32SV3,
		VirtualMachineSizeTypesStandardE32V3,
		VirtualMachineSizeTypesStandardE4SV3,
		VirtualMachineSizeTypesStandardE4V3,
		VirtualMachineSizeTypesStandardE6416SV3,
		VirtualMachineSizeTypesStandardE6432SV3,
		VirtualMachineSizeTypesStandardE64SV3,
		VirtualMachineSizeTypesStandardE64V3,
		VirtualMachineSizeTypesStandardE8SV3,
		VirtualMachineSizeTypesStandardE8V3,
		VirtualMachineSizeTypesStandardF1,
		VirtualMachineSizeTypesStandardF16,
		VirtualMachineSizeTypesStandardF16S,
		VirtualMachineSizeTypesStandardF16SV2,
		VirtualMachineSizeTypesStandardF1S,
		VirtualMachineSizeTypesStandardF2,
		VirtualMachineSizeTypesStandardF2S,
		VirtualMachineSizeTypesStandardF2SV2,
		VirtualMachineSizeTypesStandardF32SV2,
		VirtualMachineSizeTypesStandardF4,
		VirtualMachineSizeTypesStandardF4S,
		VirtualMachineSizeTypesStandardF4SV2,
		VirtualMachineSizeTypesStandardF64SV2,
		VirtualMachineSizeTypesStandardF72SV2,
		VirtualMachineSizeTypesStandardF8,
		VirtualMachineSizeTypesStandardF8S,
		VirtualMachineSizeTypesStandardF8SV2,
		VirtualMachineSizeTypesStandardG1,
		VirtualMachineSizeTypesStandardG2,
		VirtualMachineSizeTypesStandardG3,
		VirtualMachineSizeTypesStandardG4,
		VirtualMachineSizeTypesStandardG5,
		VirtualMachineSizeTypesStandardGS1,
		VirtualMachineSizeTypesStandardGS2,
		VirtualMachineSizeTypesStandardGS3,
		VirtualMachineSizeTypesStandardGS4,
		VirtualMachineSizeTypesStandardGS44,
		VirtualMachineSizeTypesStandardGS48,
		VirtualMachineSizeTypesStandardGS5,
		VirtualMachineSizeTypesStandardGS516,
		VirtualMachineSizeTypesStandardGS58,
		VirtualMachineSizeTypesStandardH16,
		VirtualMachineSizeTypesStandardH16M,
		VirtualMachineSizeTypesStandardH16Mr,
		VirtualMachineSizeTypesStandardH16R,
		VirtualMachineSizeTypesStandardH8,
		VirtualMachineSizeTypesStandardH8M,
		VirtualMachineSizeTypesStandardL16S,
		VirtualMachineSizeTypesStandardL32S,
		VirtualMachineSizeTypesStandardL4S,
		VirtualMachineSizeTypesStandardL8S,
		VirtualMachineSizeTypesStandardM12832Ms,
		VirtualMachineSizeTypesStandardM12864Ms,
		VirtualMachineSizeTypesStandardM128Ms,
		VirtualMachineSizeTypesStandardM128S,
		VirtualMachineSizeTypesStandardM6416Ms,
		VirtualMachineSizeTypesStandardM6432Ms,
		VirtualMachineSizeTypesStandardM64Ms,
		VirtualMachineSizeTypesStandardM64S,
		VirtualMachineSizeTypesStandardNC12,
		VirtualMachineSizeTypesStandardNC12SV2,
		VirtualMachineSizeTypesStandardNC12SV3,
		VirtualMachineSizeTypesStandardNC24,
		VirtualMachineSizeTypesStandardNC24R,
		VirtualMachineSizeTypesStandardNC24RsV2,
		VirtualMachineSizeTypesStandardNC24RsV3,
		VirtualMachineSizeTypesStandardNC24SV2,
		VirtualMachineSizeTypesStandardNC24SV3,
		VirtualMachineSizeTypesStandardNC6,
		VirtualMachineSizeTypesStandardNC6SV2,
		VirtualMachineSizeTypesStandardNC6SV3,
		VirtualMachineSizeTypesStandardND12S,
		VirtualMachineSizeTypesStandardND24Rs,
		VirtualMachineSizeTypesStandardND24S,
		VirtualMachineSizeTypesStandardND6S,
		VirtualMachineSizeTypesStandardNV12,
		VirtualMachineSizeTypesStandardNV24,
		VirtualMachineSizeTypesStandardNV6,
	}
}

// WindowsPatchAssessmentMode - Specifies the mode of VM Guest patch assessment for the IaaS virtual machine.
// Possible values are:
// ImageDefault - You control the timing of patch assessments on a virtual machine.
// AutomaticByPlatform - The platform will trigger periodic patch assessments. The property provisionVMAgent must be true.
type WindowsPatchAssessmentMode string

const (
	WindowsPatchAssessmentModeAutomaticByPlatform WindowsPatchAssessmentMode = "AutomaticByPlatform"
	WindowsPatchAssessmentModeImageDefault        WindowsPatchAssessmentMode = "ImageDefault"
)

// PossibleWindowsPatchAssessmentModeValues returns the possible values for the WindowsPatchAssessmentMode const type.
func PossibleWindowsPatchAssessmentModeValues() []WindowsPatchAssessmentMode {
	return []WindowsPatchAssessmentMode{
		WindowsPatchAssessmentModeAutomaticByPlatform,
		WindowsPatchAssessmentModeImageDefault,
	}
}

// WindowsVMGuestPatchAutomaticByPlatformRebootSetting - Specifies the reboot setting for all AutomaticByPlatform patch installation
// operations.
type WindowsVMGuestPatchAutomaticByPlatformRebootSetting string

const (
	WindowsVMGuestPatchAutomaticByPlatformRebootSettingAlways     WindowsVMGuestPatchAutomaticByPlatformRebootSetting = "Always"
	WindowsVMGuestPatchAutomaticByPlatformRebootSettingIfRequired WindowsVMGuestPatchAutomaticByPlatformRebootSetting = "IfRequired"
	WindowsVMGuestPatchAutomaticByPlatformRebootSettingNever      WindowsVMGuestPatchAutomaticByPlatformRebootSetting = "Never"
	WindowsVMGuestPatchAutomaticByPlatformRebootSettingUnknown    WindowsVMGuestPatchAutomaticByPlatformRebootSetting = "Unknown"
)

// PossibleWindowsVMGuestPatchAutomaticByPlatformRebootSettingValues returns the possible values for the WindowsVMGuestPatchAutomaticByPlatformRebootSetting const type.
func PossibleWindowsVMGuestPatchAutomaticByPlatformRebootSettingValues() []WindowsVMGuestPatchAutomaticByPlatformRebootSetting {
	return []WindowsVMGuestPatchAutomaticByPlatformRebootSetting{
		WindowsVMGuestPatchAutomaticByPlatformRebootSettingAlways,
		WindowsVMGuestPatchAutomaticByPlatformRebootSettingIfRequired,
		WindowsVMGuestPatchAutomaticByPlatformRebootSettingNever,
		WindowsVMGuestPatchAutomaticByPlatformRebootSettingUnknown,
	}
}

// WindowsVMGuestPatchMode - Specifies the mode of VM Guest Patching to IaaS virtual machine or virtual machines associated
// to virtual machine scale set with OrchestrationMode as Flexible.
// Possible values are:
// Manual - You control the application of patches to a virtual machine. You do this by applying patches manually inside the
// VM. In this mode, automatic updates are disabled; the property
// WindowsConfiguration.enableAutomaticUpdates must be false
// AutomaticByOS - The virtual machine will automatically be updated by the OS. The property WindowsConfiguration.enableAutomaticUpdates
// must be true.
// AutomaticByPlatform - the virtual machine will automatically updated by the platform. The properties provisionVMAgent and
// WindowsConfiguration.enableAutomaticUpdates must be true
type WindowsVMGuestPatchMode string

const (
	WindowsVMGuestPatchModeAutomaticByOS       WindowsVMGuestPatchMode = "AutomaticByOS"
	WindowsVMGuestPatchModeAutomaticByPlatform WindowsVMGuestPatchMode = "AutomaticByPlatform"
	WindowsVMGuestPatchModeManual              WindowsVMGuestPatchMode = "Manual"
)

// PossibleWindowsVMGuestPatchModeValues returns the possible values for the WindowsVMGuestPatchMode const type.
func PossibleWindowsVMGuestPatchModeValues() []WindowsVMGuestPatchMode {
	return []WindowsVMGuestPatchMode{
		WindowsVMGuestPatchModeAutomaticByOS,
		WindowsVMGuestPatchModeAutomaticByPlatform,
		WindowsVMGuestPatchModeManual,
	}
}
