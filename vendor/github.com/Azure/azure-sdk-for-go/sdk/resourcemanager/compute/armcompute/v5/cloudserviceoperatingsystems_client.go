//go:build go1.18
// +build go1.18

// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License. See License.txt in the project root for license information.
// Code generated by Microsoft (R) AutoRest Code Generator. DO NOT EDIT.
// Changes may cause incorrect behavior and will be lost if the code is regenerated.

package armcompute

import (
	"context"
	"errors"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/arm"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/policy"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/runtime"
	"net/http"
	"net/url"
	"strings"
)

// CloudServiceOperatingSystemsClient contains the methods for the CloudServiceOperatingSystems group.
// Don't use this type directly, use NewCloudServiceOperatingSystemsClient() instead.
type CloudServiceOperatingSystemsClient struct {
	internal       *arm.Client
	subscriptionID string
}

// NewCloudServiceOperatingSystemsClient creates a new instance of CloudServiceOperatingSystemsClient with the specified values.
//   - subscriptionID - Subscription credentials which uniquely identify Microsoft Azure subscription. The subscription ID forms
//     part of the URI for every service call.
//   - credential - used to authorize requests. Usually a credential from azidentity.
//   - options - pass nil to accept the default values.
func NewCloudServiceOperatingSystemsClient(subscriptionID string, credential azcore.TokenCredential, options *arm.ClientOptions) (*CloudServiceOperatingSystemsClient, error) {
	cl, err := arm.NewClient(moduleName, moduleVersion, credential, options)
	if err != nil {
		return nil, err
	}
	client := &CloudServiceOperatingSystemsClient{
		subscriptionID: subscriptionID,
		internal:       cl,
	}
	return client, nil
}

// GetOSFamily - Gets properties of a guest operating system family that can be specified in the XML service configuration
// (.cscfg) for a cloud service.
// If the operation fails it returns an *azcore.ResponseError type.
//
// Generated from API version 2022-09-04
//   - location - Name of the location that the OS family pertains to.
//   - osFamilyName - Name of the OS family.
//   - options - CloudServiceOperatingSystemsClientGetOSFamilyOptions contains the optional parameters for the CloudServiceOperatingSystemsClient.GetOSFamily
//     method.
func (client *CloudServiceOperatingSystemsClient) GetOSFamily(ctx context.Context, location string, osFamilyName string, options *CloudServiceOperatingSystemsClientGetOSFamilyOptions) (CloudServiceOperatingSystemsClientGetOSFamilyResponse, error) {
	var err error
	const operationName = "CloudServiceOperatingSystemsClient.GetOSFamily"
	ctx = context.WithValue(ctx, runtime.CtxAPINameKey{}, operationName)
	ctx, endSpan := runtime.StartSpan(ctx, operationName, client.internal.Tracer(), nil)
	defer func() { endSpan(err) }()
	req, err := client.getOSFamilyCreateRequest(ctx, location, osFamilyName, options)
	if err != nil {
		return CloudServiceOperatingSystemsClientGetOSFamilyResponse{}, err
	}
	httpResp, err := client.internal.Pipeline().Do(req)
	if err != nil {
		return CloudServiceOperatingSystemsClientGetOSFamilyResponse{}, err
	}
	if !runtime.HasStatusCode(httpResp, http.StatusOK) {
		err = runtime.NewResponseError(httpResp)
		return CloudServiceOperatingSystemsClientGetOSFamilyResponse{}, err
	}
	resp, err := client.getOSFamilyHandleResponse(httpResp)
	return resp, err
}

// getOSFamilyCreateRequest creates the GetOSFamily request.
func (client *CloudServiceOperatingSystemsClient) getOSFamilyCreateRequest(ctx context.Context, location string, osFamilyName string, options *CloudServiceOperatingSystemsClientGetOSFamilyOptions) (*policy.Request, error) {
	urlPath := "/subscriptions/{subscriptionId}/providers/Microsoft.Compute/locations/{location}/cloudServiceOsFamilies/{osFamilyName}"
	if location == "" {
		return nil, errors.New("parameter location cannot be empty")
	}
	urlPath = strings.ReplaceAll(urlPath, "{location}", url.PathEscape(location))
	if osFamilyName == "" {
		return nil, errors.New("parameter osFamilyName cannot be empty")
	}
	urlPath = strings.ReplaceAll(urlPath, "{osFamilyName}", url.PathEscape(osFamilyName))
	if client.subscriptionID == "" {
		return nil, errors.New("parameter client.subscriptionID cannot be empty")
	}
	urlPath = strings.ReplaceAll(urlPath, "{subscriptionId}", url.PathEscape(client.subscriptionID))
	req, err := runtime.NewRequest(ctx, http.MethodGet, runtime.JoinPaths(client.internal.Endpoint(), urlPath))
	if err != nil {
		return nil, err
	}
	reqQP := req.Raw().URL.Query()
	reqQP.Set("api-version", "2022-09-04")
	req.Raw().URL.RawQuery = reqQP.Encode()
	req.Raw().Header["Accept"] = []string{"application/json"}
	return req, nil
}

// getOSFamilyHandleResponse handles the GetOSFamily response.
func (client *CloudServiceOperatingSystemsClient) getOSFamilyHandleResponse(resp *http.Response) (CloudServiceOperatingSystemsClientGetOSFamilyResponse, error) {
	result := CloudServiceOperatingSystemsClientGetOSFamilyResponse{}
	if err := runtime.UnmarshalAsJSON(resp, &result.OSFamily); err != nil {
		return CloudServiceOperatingSystemsClientGetOSFamilyResponse{}, err
	}
	return result, nil
}

// GetOSVersion - Gets properties of a guest operating system version that can be specified in the XML service configuration
// (.cscfg) for a cloud service.
// If the operation fails it returns an *azcore.ResponseError type.
//
// Generated from API version 2022-09-04
//   - location - Name of the location that the OS version pertains to.
//   - osVersionName - Name of the OS version.
//   - options - CloudServiceOperatingSystemsClientGetOSVersionOptions contains the optional parameters for the CloudServiceOperatingSystemsClient.GetOSVersion
//     method.
func (client *CloudServiceOperatingSystemsClient) GetOSVersion(ctx context.Context, location string, osVersionName string, options *CloudServiceOperatingSystemsClientGetOSVersionOptions) (CloudServiceOperatingSystemsClientGetOSVersionResponse, error) {
	var err error
	const operationName = "CloudServiceOperatingSystemsClient.GetOSVersion"
	ctx = context.WithValue(ctx, runtime.CtxAPINameKey{}, operationName)
	ctx, endSpan := runtime.StartSpan(ctx, operationName, client.internal.Tracer(), nil)
	defer func() { endSpan(err) }()
	req, err := client.getOSVersionCreateRequest(ctx, location, osVersionName, options)
	if err != nil {
		return CloudServiceOperatingSystemsClientGetOSVersionResponse{}, err
	}
	httpResp, err := client.internal.Pipeline().Do(req)
	if err != nil {
		return CloudServiceOperatingSystemsClientGetOSVersionResponse{}, err
	}
	if !runtime.HasStatusCode(httpResp, http.StatusOK) {
		err = runtime.NewResponseError(httpResp)
		return CloudServiceOperatingSystemsClientGetOSVersionResponse{}, err
	}
	resp, err := client.getOSVersionHandleResponse(httpResp)
	return resp, err
}

// getOSVersionCreateRequest creates the GetOSVersion request.
func (client *CloudServiceOperatingSystemsClient) getOSVersionCreateRequest(ctx context.Context, location string, osVersionName string, options *CloudServiceOperatingSystemsClientGetOSVersionOptions) (*policy.Request, error) {
	urlPath := "/subscriptions/{subscriptionId}/providers/Microsoft.Compute/locations/{location}/cloudServiceOsVersions/{osVersionName}"
	if location == "" {
		return nil, errors.New("parameter location cannot be empty")
	}
	urlPath = strings.ReplaceAll(urlPath, "{location}", url.PathEscape(location))
	if osVersionName == "" {
		return nil, errors.New("parameter osVersionName cannot be empty")
	}
	urlPath = strings.ReplaceAll(urlPath, "{osVersionName}", url.PathEscape(osVersionName))
	if client.subscriptionID == "" {
		return nil, errors.New("parameter client.subscriptionID cannot be empty")
	}
	urlPath = strings.ReplaceAll(urlPath, "{subscriptionId}", url.PathEscape(client.subscriptionID))
	req, err := runtime.NewRequest(ctx, http.MethodGet, runtime.JoinPaths(client.internal.Endpoint(), urlPath))
	if err != nil {
		return nil, err
	}
	reqQP := req.Raw().URL.Query()
	reqQP.Set("api-version", "2022-09-04")
	req.Raw().URL.RawQuery = reqQP.Encode()
	req.Raw().Header["Accept"] = []string{"application/json"}
	return req, nil
}

// getOSVersionHandleResponse handles the GetOSVersion response.
func (client *CloudServiceOperatingSystemsClient) getOSVersionHandleResponse(resp *http.Response) (CloudServiceOperatingSystemsClientGetOSVersionResponse, error) {
	result := CloudServiceOperatingSystemsClientGetOSVersionResponse{}
	if err := runtime.UnmarshalAsJSON(resp, &result.OSVersion); err != nil {
		return CloudServiceOperatingSystemsClientGetOSVersionResponse{}, err
	}
	return result, nil
}

// NewListOSFamiliesPager - Gets a list of all guest operating system families available to be specified in the XML service
// configuration (.cscfg) for a cloud service. Use nextLink property in the response to get the next page
// of OS Families. Do this till nextLink is null to fetch all the OS Families.
//
// Generated from API version 2022-09-04
//   - location - Name of the location that the OS families pertain to.
//   - options - CloudServiceOperatingSystemsClientListOSFamiliesOptions contains the optional parameters for the CloudServiceOperatingSystemsClient.NewListOSFamiliesPager
//     method.
func (client *CloudServiceOperatingSystemsClient) NewListOSFamiliesPager(location string, options *CloudServiceOperatingSystemsClientListOSFamiliesOptions) *runtime.Pager[CloudServiceOperatingSystemsClientListOSFamiliesResponse] {
	return runtime.NewPager(runtime.PagingHandler[CloudServiceOperatingSystemsClientListOSFamiliesResponse]{
		More: func(page CloudServiceOperatingSystemsClientListOSFamiliesResponse) bool {
			return page.NextLink != nil && len(*page.NextLink) > 0
		},
		Fetcher: func(ctx context.Context, page *CloudServiceOperatingSystemsClientListOSFamiliesResponse) (CloudServiceOperatingSystemsClientListOSFamiliesResponse, error) {
			ctx = context.WithValue(ctx, runtime.CtxAPINameKey{}, "CloudServiceOperatingSystemsClient.NewListOSFamiliesPager")
			nextLink := ""
			if page != nil {
				nextLink = *page.NextLink
			}
			resp, err := runtime.FetcherForNextLink(ctx, client.internal.Pipeline(), nextLink, func(ctx context.Context) (*policy.Request, error) {
				return client.listOSFamiliesCreateRequest(ctx, location, options)
			}, nil)
			if err != nil {
				return CloudServiceOperatingSystemsClientListOSFamiliesResponse{}, err
			}
			return client.listOSFamiliesHandleResponse(resp)
		},
		Tracer: client.internal.Tracer(),
	})
}

// listOSFamiliesCreateRequest creates the ListOSFamilies request.
func (client *CloudServiceOperatingSystemsClient) listOSFamiliesCreateRequest(ctx context.Context, location string, options *CloudServiceOperatingSystemsClientListOSFamiliesOptions) (*policy.Request, error) {
	urlPath := "/subscriptions/{subscriptionId}/providers/Microsoft.Compute/locations/{location}/cloudServiceOsFamilies"
	if location == "" {
		return nil, errors.New("parameter location cannot be empty")
	}
	urlPath = strings.ReplaceAll(urlPath, "{location}", url.PathEscape(location))
	if client.subscriptionID == "" {
		return nil, errors.New("parameter client.subscriptionID cannot be empty")
	}
	urlPath = strings.ReplaceAll(urlPath, "{subscriptionId}", url.PathEscape(client.subscriptionID))
	req, err := runtime.NewRequest(ctx, http.MethodGet, runtime.JoinPaths(client.internal.Endpoint(), urlPath))
	if err != nil {
		return nil, err
	}
	reqQP := req.Raw().URL.Query()
	reqQP.Set("api-version", "2022-09-04")
	req.Raw().URL.RawQuery = reqQP.Encode()
	req.Raw().Header["Accept"] = []string{"application/json"}
	return req, nil
}

// listOSFamiliesHandleResponse handles the ListOSFamilies response.
func (client *CloudServiceOperatingSystemsClient) listOSFamiliesHandleResponse(resp *http.Response) (CloudServiceOperatingSystemsClientListOSFamiliesResponse, error) {
	result := CloudServiceOperatingSystemsClientListOSFamiliesResponse{}
	if err := runtime.UnmarshalAsJSON(resp, &result.OSFamilyListResult); err != nil {
		return CloudServiceOperatingSystemsClientListOSFamiliesResponse{}, err
	}
	return result, nil
}

// NewListOSVersionsPager - Gets a list of all guest operating system versions available to be specified in the XML service
// configuration (.cscfg) for a cloud service. Use nextLink property in the response to get the next page
// of OS versions. Do this till nextLink is null to fetch all the OS versions.
//
// Generated from API version 2022-09-04
//   - location - Name of the location that the OS versions pertain to.
//   - options - CloudServiceOperatingSystemsClientListOSVersionsOptions contains the optional parameters for the CloudServiceOperatingSystemsClient.NewListOSVersionsPager
//     method.
func (client *CloudServiceOperatingSystemsClient) NewListOSVersionsPager(location string, options *CloudServiceOperatingSystemsClientListOSVersionsOptions) *runtime.Pager[CloudServiceOperatingSystemsClientListOSVersionsResponse] {
	return runtime.NewPager(runtime.PagingHandler[CloudServiceOperatingSystemsClientListOSVersionsResponse]{
		More: func(page CloudServiceOperatingSystemsClientListOSVersionsResponse) bool {
			return page.NextLink != nil && len(*page.NextLink) > 0
		},
		Fetcher: func(ctx context.Context, page *CloudServiceOperatingSystemsClientListOSVersionsResponse) (CloudServiceOperatingSystemsClientListOSVersionsResponse, error) {
			ctx = context.WithValue(ctx, runtime.CtxAPINameKey{}, "CloudServiceOperatingSystemsClient.NewListOSVersionsPager")
			nextLink := ""
			if page != nil {
				nextLink = *page.NextLink
			}
			resp, err := runtime.FetcherForNextLink(ctx, client.internal.Pipeline(), nextLink, func(ctx context.Context) (*policy.Request, error) {
				return client.listOSVersionsCreateRequest(ctx, location, options)
			}, nil)
			if err != nil {
				return CloudServiceOperatingSystemsClientListOSVersionsResponse{}, err
			}
			return client.listOSVersionsHandleResponse(resp)
		},
		Tracer: client.internal.Tracer(),
	})
}

// listOSVersionsCreateRequest creates the ListOSVersions request.
func (client *CloudServiceOperatingSystemsClient) listOSVersionsCreateRequest(ctx context.Context, location string, options *CloudServiceOperatingSystemsClientListOSVersionsOptions) (*policy.Request, error) {
	urlPath := "/subscriptions/{subscriptionId}/providers/Microsoft.Compute/locations/{location}/cloudServiceOsVersions"
	if location == "" {
		return nil, errors.New("parameter location cannot be empty")
	}
	urlPath = strings.ReplaceAll(urlPath, "{location}", url.PathEscape(location))
	if client.subscriptionID == "" {
		return nil, errors.New("parameter client.subscriptionID cannot be empty")
	}
	urlPath = strings.ReplaceAll(urlPath, "{subscriptionId}", url.PathEscape(client.subscriptionID))
	req, err := runtime.NewRequest(ctx, http.MethodGet, runtime.JoinPaths(client.internal.Endpoint(), urlPath))
	if err != nil {
		return nil, err
	}
	reqQP := req.Raw().URL.Query()
	reqQP.Set("api-version", "2022-09-04")
	req.Raw().URL.RawQuery = reqQP.Encode()
	req.Raw().Header["Accept"] = []string{"application/json"}
	return req, nil
}

// listOSVersionsHandleResponse handles the ListOSVersions response.
func (client *CloudServiceOperatingSystemsClient) listOSVersionsHandleResponse(resp *http.Response) (CloudServiceOperatingSystemsClientListOSVersionsResponse, error) {
	result := CloudServiceOperatingSystemsClientListOSVersionsResponse{}
	if err := runtime.UnmarshalAsJSON(resp, &result.OSVersionListResult); err != nil {
		return CloudServiceOperatingSystemsClientListOSVersionsResponse{}, err
	}
	return result, nil
}
