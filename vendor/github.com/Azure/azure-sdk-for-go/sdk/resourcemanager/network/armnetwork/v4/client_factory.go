//go:build go1.18
// +build go1.18

// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License. See License.txt in the project root for license information.
// Code generated by Microsoft (R) AutoRest Code Generator. DO NOT EDIT.
// Changes may cause incorrect behavior and will be lost if the code is regenerated.

package armnetwork

import (
	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/arm"
)

// ClientFactory is a client factory used to create any client in this module.
// Don't use this type directly, use NewClientFactory instead.
type ClientFactory struct {
	subscriptionID string
	credential     azcore.TokenCredential
	options        *arm.ClientOptions
}

// NewClientFactory creates a new instance of ClientFactory with the specified values.
// The parameter values will be propagated to any client created from this factory.
//   - subscriptionID - The subscription credentials which uniquely identify the Microsoft Azure subscription. The subscription
//     ID forms part of the URI for every service call.
//   - credential - used to authorize requests. Usually a credential from azidentity.
//   - options - pass nil to accept the default values.
func NewClientFactory(subscriptionID string, credential azcore.TokenCredential, options *arm.ClientOptions) (*ClientFactory, error) {
	_, err := arm.NewClient(moduleName, moduleVersion, credential, options)
	if err != nil {
		return nil, err
	}
	return &ClientFactory{
		subscriptionID: subscriptionID, credential: credential,
		options: options.Clone(),
	}, nil
}

// NewAdminRuleCollectionsClient creates a new instance of AdminRuleCollectionsClient.
func (c *ClientFactory) NewAdminRuleCollectionsClient() *AdminRuleCollectionsClient {
	subClient, _ := NewAdminRuleCollectionsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewAdminRulesClient creates a new instance of AdminRulesClient.
func (c *ClientFactory) NewAdminRulesClient() *AdminRulesClient {
	subClient, _ := NewAdminRulesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewApplicationGatewayPrivateEndpointConnectionsClient creates a new instance of ApplicationGatewayPrivateEndpointConnectionsClient.
func (c *ClientFactory) NewApplicationGatewayPrivateEndpointConnectionsClient() *ApplicationGatewayPrivateEndpointConnectionsClient {
	subClient, _ := NewApplicationGatewayPrivateEndpointConnectionsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewApplicationGatewayPrivateLinkResourcesClient creates a new instance of ApplicationGatewayPrivateLinkResourcesClient.
func (c *ClientFactory) NewApplicationGatewayPrivateLinkResourcesClient() *ApplicationGatewayPrivateLinkResourcesClient {
	subClient, _ := NewApplicationGatewayPrivateLinkResourcesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewApplicationGatewayWafDynamicManifestsClient creates a new instance of ApplicationGatewayWafDynamicManifestsClient.
func (c *ClientFactory) NewApplicationGatewayWafDynamicManifestsClient() *ApplicationGatewayWafDynamicManifestsClient {
	subClient, _ := NewApplicationGatewayWafDynamicManifestsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewApplicationGatewayWafDynamicManifestsDefaultClient creates a new instance of ApplicationGatewayWafDynamicManifestsDefaultClient.
func (c *ClientFactory) NewApplicationGatewayWafDynamicManifestsDefaultClient() *ApplicationGatewayWafDynamicManifestsDefaultClient {
	subClient, _ := NewApplicationGatewayWafDynamicManifestsDefaultClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewApplicationGatewaysClient creates a new instance of ApplicationGatewaysClient.
func (c *ClientFactory) NewApplicationGatewaysClient() *ApplicationGatewaysClient {
	subClient, _ := NewApplicationGatewaysClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewApplicationSecurityGroupsClient creates a new instance of ApplicationSecurityGroupsClient.
func (c *ClientFactory) NewApplicationSecurityGroupsClient() *ApplicationSecurityGroupsClient {
	subClient, _ := NewApplicationSecurityGroupsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewAvailableDelegationsClient creates a new instance of AvailableDelegationsClient.
func (c *ClientFactory) NewAvailableDelegationsClient() *AvailableDelegationsClient {
	subClient, _ := NewAvailableDelegationsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewAvailableEndpointServicesClient creates a new instance of AvailableEndpointServicesClient.
func (c *ClientFactory) NewAvailableEndpointServicesClient() *AvailableEndpointServicesClient {
	subClient, _ := NewAvailableEndpointServicesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewAvailablePrivateEndpointTypesClient creates a new instance of AvailablePrivateEndpointTypesClient.
func (c *ClientFactory) NewAvailablePrivateEndpointTypesClient() *AvailablePrivateEndpointTypesClient {
	subClient, _ := NewAvailablePrivateEndpointTypesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewAvailableResourceGroupDelegationsClient creates a new instance of AvailableResourceGroupDelegationsClient.
func (c *ClientFactory) NewAvailableResourceGroupDelegationsClient() *AvailableResourceGroupDelegationsClient {
	subClient, _ := NewAvailableResourceGroupDelegationsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewAvailableServiceAliasesClient creates a new instance of AvailableServiceAliasesClient.
func (c *ClientFactory) NewAvailableServiceAliasesClient() *AvailableServiceAliasesClient {
	subClient, _ := NewAvailableServiceAliasesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewAzureFirewallFqdnTagsClient creates a new instance of AzureFirewallFqdnTagsClient.
func (c *ClientFactory) NewAzureFirewallFqdnTagsClient() *AzureFirewallFqdnTagsClient {
	subClient, _ := NewAzureFirewallFqdnTagsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewAzureFirewallsClient creates a new instance of AzureFirewallsClient.
func (c *ClientFactory) NewAzureFirewallsClient() *AzureFirewallsClient {
	subClient, _ := NewAzureFirewallsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewBastionHostsClient creates a new instance of BastionHostsClient.
func (c *ClientFactory) NewBastionHostsClient() *BastionHostsClient {
	subClient, _ := NewBastionHostsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewBgpServiceCommunitiesClient creates a new instance of BgpServiceCommunitiesClient.
func (c *ClientFactory) NewBgpServiceCommunitiesClient() *BgpServiceCommunitiesClient {
	subClient, _ := NewBgpServiceCommunitiesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewConfigurationPolicyGroupsClient creates a new instance of ConfigurationPolicyGroupsClient.
func (c *ClientFactory) NewConfigurationPolicyGroupsClient() *ConfigurationPolicyGroupsClient {
	subClient, _ := NewConfigurationPolicyGroupsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewConnectionMonitorsClient creates a new instance of ConnectionMonitorsClient.
func (c *ClientFactory) NewConnectionMonitorsClient() *ConnectionMonitorsClient {
	subClient, _ := NewConnectionMonitorsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewConnectivityConfigurationsClient creates a new instance of ConnectivityConfigurationsClient.
func (c *ClientFactory) NewConnectivityConfigurationsClient() *ConnectivityConfigurationsClient {
	subClient, _ := NewConnectivityConfigurationsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewCustomIPPrefixesClient creates a new instance of CustomIPPrefixesClient.
func (c *ClientFactory) NewCustomIPPrefixesClient() *CustomIPPrefixesClient {
	subClient, _ := NewCustomIPPrefixesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewDdosCustomPoliciesClient creates a new instance of DdosCustomPoliciesClient.
func (c *ClientFactory) NewDdosCustomPoliciesClient() *DdosCustomPoliciesClient {
	subClient, _ := NewDdosCustomPoliciesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewDdosProtectionPlansClient creates a new instance of DdosProtectionPlansClient.
func (c *ClientFactory) NewDdosProtectionPlansClient() *DdosProtectionPlansClient {
	subClient, _ := NewDdosProtectionPlansClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewDefaultSecurityRulesClient creates a new instance of DefaultSecurityRulesClient.
func (c *ClientFactory) NewDefaultSecurityRulesClient() *DefaultSecurityRulesClient {
	subClient, _ := NewDefaultSecurityRulesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewDscpConfigurationClient creates a new instance of DscpConfigurationClient.
func (c *ClientFactory) NewDscpConfigurationClient() *DscpConfigurationClient {
	subClient, _ := NewDscpConfigurationClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewExpressRouteCircuitAuthorizationsClient creates a new instance of ExpressRouteCircuitAuthorizationsClient.
func (c *ClientFactory) NewExpressRouteCircuitAuthorizationsClient() *ExpressRouteCircuitAuthorizationsClient {
	subClient, _ := NewExpressRouteCircuitAuthorizationsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewExpressRouteCircuitConnectionsClient creates a new instance of ExpressRouteCircuitConnectionsClient.
func (c *ClientFactory) NewExpressRouteCircuitConnectionsClient() *ExpressRouteCircuitConnectionsClient {
	subClient, _ := NewExpressRouteCircuitConnectionsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewExpressRouteCircuitPeeringsClient creates a new instance of ExpressRouteCircuitPeeringsClient.
func (c *ClientFactory) NewExpressRouteCircuitPeeringsClient() *ExpressRouteCircuitPeeringsClient {
	subClient, _ := NewExpressRouteCircuitPeeringsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewExpressRouteCircuitsClient creates a new instance of ExpressRouteCircuitsClient.
func (c *ClientFactory) NewExpressRouteCircuitsClient() *ExpressRouteCircuitsClient {
	subClient, _ := NewExpressRouteCircuitsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewExpressRouteConnectionsClient creates a new instance of ExpressRouteConnectionsClient.
func (c *ClientFactory) NewExpressRouteConnectionsClient() *ExpressRouteConnectionsClient {
	subClient, _ := NewExpressRouteConnectionsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewExpressRouteCrossConnectionPeeringsClient creates a new instance of ExpressRouteCrossConnectionPeeringsClient.
func (c *ClientFactory) NewExpressRouteCrossConnectionPeeringsClient() *ExpressRouteCrossConnectionPeeringsClient {
	subClient, _ := NewExpressRouteCrossConnectionPeeringsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewExpressRouteCrossConnectionsClient creates a new instance of ExpressRouteCrossConnectionsClient.
func (c *ClientFactory) NewExpressRouteCrossConnectionsClient() *ExpressRouteCrossConnectionsClient {
	subClient, _ := NewExpressRouteCrossConnectionsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewExpressRouteGatewaysClient creates a new instance of ExpressRouteGatewaysClient.
func (c *ClientFactory) NewExpressRouteGatewaysClient() *ExpressRouteGatewaysClient {
	subClient, _ := NewExpressRouteGatewaysClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewExpressRouteLinksClient creates a new instance of ExpressRouteLinksClient.
func (c *ClientFactory) NewExpressRouteLinksClient() *ExpressRouteLinksClient {
	subClient, _ := NewExpressRouteLinksClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewExpressRoutePortAuthorizationsClient creates a new instance of ExpressRoutePortAuthorizationsClient.
func (c *ClientFactory) NewExpressRoutePortAuthorizationsClient() *ExpressRoutePortAuthorizationsClient {
	subClient, _ := NewExpressRoutePortAuthorizationsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewExpressRoutePortsClient creates a new instance of ExpressRoutePortsClient.
func (c *ClientFactory) NewExpressRoutePortsClient() *ExpressRoutePortsClient {
	subClient, _ := NewExpressRoutePortsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewExpressRoutePortsLocationsClient creates a new instance of ExpressRoutePortsLocationsClient.
func (c *ClientFactory) NewExpressRoutePortsLocationsClient() *ExpressRoutePortsLocationsClient {
	subClient, _ := NewExpressRoutePortsLocationsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewExpressRouteProviderPortsLocationClient creates a new instance of ExpressRouteProviderPortsLocationClient.
func (c *ClientFactory) NewExpressRouteProviderPortsLocationClient() *ExpressRouteProviderPortsLocationClient {
	subClient, _ := NewExpressRouteProviderPortsLocationClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewExpressRouteServiceProvidersClient creates a new instance of ExpressRouteServiceProvidersClient.
func (c *ClientFactory) NewExpressRouteServiceProvidersClient() *ExpressRouteServiceProvidersClient {
	subClient, _ := NewExpressRouteServiceProvidersClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewFirewallPoliciesClient creates a new instance of FirewallPoliciesClient.
func (c *ClientFactory) NewFirewallPoliciesClient() *FirewallPoliciesClient {
	subClient, _ := NewFirewallPoliciesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewFirewallPolicyIdpsSignaturesClient creates a new instance of FirewallPolicyIdpsSignaturesClient.
func (c *ClientFactory) NewFirewallPolicyIdpsSignaturesClient() *FirewallPolicyIdpsSignaturesClient {
	subClient, _ := NewFirewallPolicyIdpsSignaturesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewFirewallPolicyIdpsSignaturesFilterValuesClient creates a new instance of FirewallPolicyIdpsSignaturesFilterValuesClient.
func (c *ClientFactory) NewFirewallPolicyIdpsSignaturesFilterValuesClient() *FirewallPolicyIdpsSignaturesFilterValuesClient {
	subClient, _ := NewFirewallPolicyIdpsSignaturesFilterValuesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewFirewallPolicyIdpsSignaturesOverridesClient creates a new instance of FirewallPolicyIdpsSignaturesOverridesClient.
func (c *ClientFactory) NewFirewallPolicyIdpsSignaturesOverridesClient() *FirewallPolicyIdpsSignaturesOverridesClient {
	subClient, _ := NewFirewallPolicyIdpsSignaturesOverridesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewFirewallPolicyRuleCollectionGroupsClient creates a new instance of FirewallPolicyRuleCollectionGroupsClient.
func (c *ClientFactory) NewFirewallPolicyRuleCollectionGroupsClient() *FirewallPolicyRuleCollectionGroupsClient {
	subClient, _ := NewFirewallPolicyRuleCollectionGroupsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewFlowLogsClient creates a new instance of FlowLogsClient.
func (c *ClientFactory) NewFlowLogsClient() *FlowLogsClient {
	subClient, _ := NewFlowLogsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewGroupsClient creates a new instance of GroupsClient.
func (c *ClientFactory) NewGroupsClient() *GroupsClient {
	subClient, _ := NewGroupsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewHubRouteTablesClient creates a new instance of HubRouteTablesClient.
func (c *ClientFactory) NewHubRouteTablesClient() *HubRouteTablesClient {
	subClient, _ := NewHubRouteTablesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewHubVirtualNetworkConnectionsClient creates a new instance of HubVirtualNetworkConnectionsClient.
func (c *ClientFactory) NewHubVirtualNetworkConnectionsClient() *HubVirtualNetworkConnectionsClient {
	subClient, _ := NewHubVirtualNetworkConnectionsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewIPAllocationsClient creates a new instance of IPAllocationsClient.
func (c *ClientFactory) NewIPAllocationsClient() *IPAllocationsClient {
	subClient, _ := NewIPAllocationsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewIPGroupsClient creates a new instance of IPGroupsClient.
func (c *ClientFactory) NewIPGroupsClient() *IPGroupsClient {
	subClient, _ := NewIPGroupsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewInboundNatRulesClient creates a new instance of InboundNatRulesClient.
func (c *ClientFactory) NewInboundNatRulesClient() *InboundNatRulesClient {
	subClient, _ := NewInboundNatRulesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewInboundSecurityRuleClient creates a new instance of InboundSecurityRuleClient.
func (c *ClientFactory) NewInboundSecurityRuleClient() *InboundSecurityRuleClient {
	subClient, _ := NewInboundSecurityRuleClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewInterfaceIPConfigurationsClient creates a new instance of InterfaceIPConfigurationsClient.
func (c *ClientFactory) NewInterfaceIPConfigurationsClient() *InterfaceIPConfigurationsClient {
	subClient, _ := NewInterfaceIPConfigurationsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewInterfaceLoadBalancersClient creates a new instance of InterfaceLoadBalancersClient.
func (c *ClientFactory) NewInterfaceLoadBalancersClient() *InterfaceLoadBalancersClient {
	subClient, _ := NewInterfaceLoadBalancersClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewInterfaceTapConfigurationsClient creates a new instance of InterfaceTapConfigurationsClient.
func (c *ClientFactory) NewInterfaceTapConfigurationsClient() *InterfaceTapConfigurationsClient {
	subClient, _ := NewInterfaceTapConfigurationsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewInterfacesClient creates a new instance of InterfacesClient.
func (c *ClientFactory) NewInterfacesClient() *InterfacesClient {
	subClient, _ := NewInterfacesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewLoadBalancerBackendAddressPoolsClient creates a new instance of LoadBalancerBackendAddressPoolsClient.
func (c *ClientFactory) NewLoadBalancerBackendAddressPoolsClient() *LoadBalancerBackendAddressPoolsClient {
	subClient, _ := NewLoadBalancerBackendAddressPoolsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewLoadBalancerFrontendIPConfigurationsClient creates a new instance of LoadBalancerFrontendIPConfigurationsClient.
func (c *ClientFactory) NewLoadBalancerFrontendIPConfigurationsClient() *LoadBalancerFrontendIPConfigurationsClient {
	subClient, _ := NewLoadBalancerFrontendIPConfigurationsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewLoadBalancerLoadBalancingRulesClient creates a new instance of LoadBalancerLoadBalancingRulesClient.
func (c *ClientFactory) NewLoadBalancerLoadBalancingRulesClient() *LoadBalancerLoadBalancingRulesClient {
	subClient, _ := NewLoadBalancerLoadBalancingRulesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewLoadBalancerNetworkInterfacesClient creates a new instance of LoadBalancerNetworkInterfacesClient.
func (c *ClientFactory) NewLoadBalancerNetworkInterfacesClient() *LoadBalancerNetworkInterfacesClient {
	subClient, _ := NewLoadBalancerNetworkInterfacesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewLoadBalancerOutboundRulesClient creates a new instance of LoadBalancerOutboundRulesClient.
func (c *ClientFactory) NewLoadBalancerOutboundRulesClient() *LoadBalancerOutboundRulesClient {
	subClient, _ := NewLoadBalancerOutboundRulesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewLoadBalancerProbesClient creates a new instance of LoadBalancerProbesClient.
func (c *ClientFactory) NewLoadBalancerProbesClient() *LoadBalancerProbesClient {
	subClient, _ := NewLoadBalancerProbesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewLoadBalancersClient creates a new instance of LoadBalancersClient.
func (c *ClientFactory) NewLoadBalancersClient() *LoadBalancersClient {
	subClient, _ := NewLoadBalancersClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewLocalNetworkGatewaysClient creates a new instance of LocalNetworkGatewaysClient.
func (c *ClientFactory) NewLocalNetworkGatewaysClient() *LocalNetworkGatewaysClient {
	subClient, _ := NewLocalNetworkGatewaysClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewManagementClient creates a new instance of ManagementClient.
func (c *ClientFactory) NewManagementClient() *ManagementClient {
	subClient, _ := NewManagementClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewManagementGroupNetworkManagerConnectionsClient creates a new instance of ManagementGroupNetworkManagerConnectionsClient.
func (c *ClientFactory) NewManagementGroupNetworkManagerConnectionsClient() *ManagementGroupNetworkManagerConnectionsClient {
	subClient, _ := NewManagementGroupNetworkManagerConnectionsClient(c.credential, c.options)
	return subClient
}

// NewManagerCommitsClient creates a new instance of ManagerCommitsClient.
func (c *ClientFactory) NewManagerCommitsClient() *ManagerCommitsClient {
	subClient, _ := NewManagerCommitsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewManagerDeploymentStatusClient creates a new instance of ManagerDeploymentStatusClient.
func (c *ClientFactory) NewManagerDeploymentStatusClient() *ManagerDeploymentStatusClient {
	subClient, _ := NewManagerDeploymentStatusClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewManagersClient creates a new instance of ManagersClient.
func (c *ClientFactory) NewManagersClient() *ManagersClient {
	subClient, _ := NewManagersClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewNatGatewaysClient creates a new instance of NatGatewaysClient.
func (c *ClientFactory) NewNatGatewaysClient() *NatGatewaysClient {
	subClient, _ := NewNatGatewaysClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewNatRulesClient creates a new instance of NatRulesClient.
func (c *ClientFactory) NewNatRulesClient() *NatRulesClient {
	subClient, _ := NewNatRulesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewOperationsClient creates a new instance of OperationsClient.
func (c *ClientFactory) NewOperationsClient() *OperationsClient {
	subClient, _ := NewOperationsClient(c.credential, c.options)
	return subClient
}

// NewP2SVPNGatewaysClient creates a new instance of P2SVPNGatewaysClient.
func (c *ClientFactory) NewP2SVPNGatewaysClient() *P2SVPNGatewaysClient {
	subClient, _ := NewP2SVPNGatewaysClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewPacketCapturesClient creates a new instance of PacketCapturesClient.
func (c *ClientFactory) NewPacketCapturesClient() *PacketCapturesClient {
	subClient, _ := NewPacketCapturesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewPeerExpressRouteCircuitConnectionsClient creates a new instance of PeerExpressRouteCircuitConnectionsClient.
func (c *ClientFactory) NewPeerExpressRouteCircuitConnectionsClient() *PeerExpressRouteCircuitConnectionsClient {
	subClient, _ := NewPeerExpressRouteCircuitConnectionsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewPrivateDNSZoneGroupsClient creates a new instance of PrivateDNSZoneGroupsClient.
func (c *ClientFactory) NewPrivateDNSZoneGroupsClient() *PrivateDNSZoneGroupsClient {
	subClient, _ := NewPrivateDNSZoneGroupsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewPrivateEndpointsClient creates a new instance of PrivateEndpointsClient.
func (c *ClientFactory) NewPrivateEndpointsClient() *PrivateEndpointsClient {
	subClient, _ := NewPrivateEndpointsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewPrivateLinkServicesClient creates a new instance of PrivateLinkServicesClient.
func (c *ClientFactory) NewPrivateLinkServicesClient() *PrivateLinkServicesClient {
	subClient, _ := NewPrivateLinkServicesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewProfilesClient creates a new instance of ProfilesClient.
func (c *ClientFactory) NewProfilesClient() *ProfilesClient {
	subClient, _ := NewProfilesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewPublicIPAddressesClient creates a new instance of PublicIPAddressesClient.
func (c *ClientFactory) NewPublicIPAddressesClient() *PublicIPAddressesClient {
	subClient, _ := NewPublicIPAddressesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewPublicIPPrefixesClient creates a new instance of PublicIPPrefixesClient.
func (c *ClientFactory) NewPublicIPPrefixesClient() *PublicIPPrefixesClient {
	subClient, _ := NewPublicIPPrefixesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewResourceNavigationLinksClient creates a new instance of ResourceNavigationLinksClient.
func (c *ClientFactory) NewResourceNavigationLinksClient() *ResourceNavigationLinksClient {
	subClient, _ := NewResourceNavigationLinksClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewRouteFilterRulesClient creates a new instance of RouteFilterRulesClient.
func (c *ClientFactory) NewRouteFilterRulesClient() *RouteFilterRulesClient {
	subClient, _ := NewRouteFilterRulesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewRouteFiltersClient creates a new instance of RouteFiltersClient.
func (c *ClientFactory) NewRouteFiltersClient() *RouteFiltersClient {
	subClient, _ := NewRouteFiltersClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewRouteMapsClient creates a new instance of RouteMapsClient.
func (c *ClientFactory) NewRouteMapsClient() *RouteMapsClient {
	subClient, _ := NewRouteMapsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewRouteTablesClient creates a new instance of RouteTablesClient.
func (c *ClientFactory) NewRouteTablesClient() *RouteTablesClient {
	subClient, _ := NewRouteTablesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewRoutesClient creates a new instance of RoutesClient.
func (c *ClientFactory) NewRoutesClient() *RoutesClient {
	subClient, _ := NewRoutesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewRoutingIntentClient creates a new instance of RoutingIntentClient.
func (c *ClientFactory) NewRoutingIntentClient() *RoutingIntentClient {
	subClient, _ := NewRoutingIntentClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewScopeConnectionsClient creates a new instance of ScopeConnectionsClient.
func (c *ClientFactory) NewScopeConnectionsClient() *ScopeConnectionsClient {
	subClient, _ := NewScopeConnectionsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewSecurityAdminConfigurationsClient creates a new instance of SecurityAdminConfigurationsClient.
func (c *ClientFactory) NewSecurityAdminConfigurationsClient() *SecurityAdminConfigurationsClient {
	subClient, _ := NewSecurityAdminConfigurationsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewSecurityGroupsClient creates a new instance of SecurityGroupsClient.
func (c *ClientFactory) NewSecurityGroupsClient() *SecurityGroupsClient {
	subClient, _ := NewSecurityGroupsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewSecurityPartnerProvidersClient creates a new instance of SecurityPartnerProvidersClient.
func (c *ClientFactory) NewSecurityPartnerProvidersClient() *SecurityPartnerProvidersClient {
	subClient, _ := NewSecurityPartnerProvidersClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewSecurityRulesClient creates a new instance of SecurityRulesClient.
func (c *ClientFactory) NewSecurityRulesClient() *SecurityRulesClient {
	subClient, _ := NewSecurityRulesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewServiceAssociationLinksClient creates a new instance of ServiceAssociationLinksClient.
func (c *ClientFactory) NewServiceAssociationLinksClient() *ServiceAssociationLinksClient {
	subClient, _ := NewServiceAssociationLinksClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewServiceEndpointPoliciesClient creates a new instance of ServiceEndpointPoliciesClient.
func (c *ClientFactory) NewServiceEndpointPoliciesClient() *ServiceEndpointPoliciesClient {
	subClient, _ := NewServiceEndpointPoliciesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewServiceEndpointPolicyDefinitionsClient creates a new instance of ServiceEndpointPolicyDefinitionsClient.
func (c *ClientFactory) NewServiceEndpointPolicyDefinitionsClient() *ServiceEndpointPolicyDefinitionsClient {
	subClient, _ := NewServiceEndpointPolicyDefinitionsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewServiceTagInformationClient creates a new instance of ServiceTagInformationClient.
func (c *ClientFactory) NewServiceTagInformationClient() *ServiceTagInformationClient {
	subClient, _ := NewServiceTagInformationClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewServiceTagsClient creates a new instance of ServiceTagsClient.
func (c *ClientFactory) NewServiceTagsClient() *ServiceTagsClient {
	subClient, _ := NewServiceTagsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewStaticMembersClient creates a new instance of StaticMembersClient.
func (c *ClientFactory) NewStaticMembersClient() *StaticMembersClient {
	subClient, _ := NewStaticMembersClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewSubnetsClient creates a new instance of SubnetsClient.
func (c *ClientFactory) NewSubnetsClient() *SubnetsClient {
	subClient, _ := NewSubnetsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewSubscriptionNetworkManagerConnectionsClient creates a new instance of SubscriptionNetworkManagerConnectionsClient.
func (c *ClientFactory) NewSubscriptionNetworkManagerConnectionsClient() *SubscriptionNetworkManagerConnectionsClient {
	subClient, _ := NewSubscriptionNetworkManagerConnectionsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewUsagesClient creates a new instance of UsagesClient.
func (c *ClientFactory) NewUsagesClient() *UsagesClient {
	subClient, _ := NewUsagesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVPNConnectionsClient creates a new instance of VPNConnectionsClient.
func (c *ClientFactory) NewVPNConnectionsClient() *VPNConnectionsClient {
	subClient, _ := NewVPNConnectionsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVPNGatewaysClient creates a new instance of VPNGatewaysClient.
func (c *ClientFactory) NewVPNGatewaysClient() *VPNGatewaysClient {
	subClient, _ := NewVPNGatewaysClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVPNLinkConnectionsClient creates a new instance of VPNLinkConnectionsClient.
func (c *ClientFactory) NewVPNLinkConnectionsClient() *VPNLinkConnectionsClient {
	subClient, _ := NewVPNLinkConnectionsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVPNServerConfigurationsAssociatedWithVirtualWanClient creates a new instance of VPNServerConfigurationsAssociatedWithVirtualWanClient.
func (c *ClientFactory) NewVPNServerConfigurationsAssociatedWithVirtualWanClient() *VPNServerConfigurationsAssociatedWithVirtualWanClient {
	subClient, _ := NewVPNServerConfigurationsAssociatedWithVirtualWanClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVPNServerConfigurationsClient creates a new instance of VPNServerConfigurationsClient.
func (c *ClientFactory) NewVPNServerConfigurationsClient() *VPNServerConfigurationsClient {
	subClient, _ := NewVPNServerConfigurationsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVPNSiteLinkConnectionsClient creates a new instance of VPNSiteLinkConnectionsClient.
func (c *ClientFactory) NewVPNSiteLinkConnectionsClient() *VPNSiteLinkConnectionsClient {
	subClient, _ := NewVPNSiteLinkConnectionsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVPNSiteLinksClient creates a new instance of VPNSiteLinksClient.
func (c *ClientFactory) NewVPNSiteLinksClient() *VPNSiteLinksClient {
	subClient, _ := NewVPNSiteLinksClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVPNSitesClient creates a new instance of VPNSitesClient.
func (c *ClientFactory) NewVPNSitesClient() *VPNSitesClient {
	subClient, _ := NewVPNSitesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVPNSitesConfigurationClient creates a new instance of VPNSitesConfigurationClient.
func (c *ClientFactory) NewVPNSitesConfigurationClient() *VPNSitesConfigurationClient {
	subClient, _ := NewVPNSitesConfigurationClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVipSwapClient creates a new instance of VipSwapClient.
func (c *ClientFactory) NewVipSwapClient() *VipSwapClient {
	subClient, _ := NewVipSwapClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualApplianceConnectionsClient creates a new instance of VirtualApplianceConnectionsClient.
func (c *ClientFactory) NewVirtualApplianceConnectionsClient() *VirtualApplianceConnectionsClient {
	subClient, _ := NewVirtualApplianceConnectionsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualApplianceSKUsClient creates a new instance of VirtualApplianceSKUsClient.
func (c *ClientFactory) NewVirtualApplianceSKUsClient() *VirtualApplianceSKUsClient {
	subClient, _ := NewVirtualApplianceSKUsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualApplianceSitesClient creates a new instance of VirtualApplianceSitesClient.
func (c *ClientFactory) NewVirtualApplianceSitesClient() *VirtualApplianceSitesClient {
	subClient, _ := NewVirtualApplianceSitesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualAppliancesClient creates a new instance of VirtualAppliancesClient.
func (c *ClientFactory) NewVirtualAppliancesClient() *VirtualAppliancesClient {
	subClient, _ := NewVirtualAppliancesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualHubBgpConnectionClient creates a new instance of VirtualHubBgpConnectionClient.
func (c *ClientFactory) NewVirtualHubBgpConnectionClient() *VirtualHubBgpConnectionClient {
	subClient, _ := NewVirtualHubBgpConnectionClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualHubBgpConnectionsClient creates a new instance of VirtualHubBgpConnectionsClient.
func (c *ClientFactory) NewVirtualHubBgpConnectionsClient() *VirtualHubBgpConnectionsClient {
	subClient, _ := NewVirtualHubBgpConnectionsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualHubIPConfigurationClient creates a new instance of VirtualHubIPConfigurationClient.
func (c *ClientFactory) NewVirtualHubIPConfigurationClient() *VirtualHubIPConfigurationClient {
	subClient, _ := NewVirtualHubIPConfigurationClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualHubRouteTableV2SClient creates a new instance of VirtualHubRouteTableV2SClient.
func (c *ClientFactory) NewVirtualHubRouteTableV2SClient() *VirtualHubRouteTableV2SClient {
	subClient, _ := NewVirtualHubRouteTableV2SClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualHubsClient creates a new instance of VirtualHubsClient.
func (c *ClientFactory) NewVirtualHubsClient() *VirtualHubsClient {
	subClient, _ := NewVirtualHubsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualNetworkGatewayConnectionsClient creates a new instance of VirtualNetworkGatewayConnectionsClient.
func (c *ClientFactory) NewVirtualNetworkGatewayConnectionsClient() *VirtualNetworkGatewayConnectionsClient {
	subClient, _ := NewVirtualNetworkGatewayConnectionsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualNetworkGatewayNatRulesClient creates a new instance of VirtualNetworkGatewayNatRulesClient.
func (c *ClientFactory) NewVirtualNetworkGatewayNatRulesClient() *VirtualNetworkGatewayNatRulesClient {
	subClient, _ := NewVirtualNetworkGatewayNatRulesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualNetworkGatewaysClient creates a new instance of VirtualNetworkGatewaysClient.
func (c *ClientFactory) NewVirtualNetworkGatewaysClient() *VirtualNetworkGatewaysClient {
	subClient, _ := NewVirtualNetworkGatewaysClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualNetworkPeeringsClient creates a new instance of VirtualNetworkPeeringsClient.
func (c *ClientFactory) NewVirtualNetworkPeeringsClient() *VirtualNetworkPeeringsClient {
	subClient, _ := NewVirtualNetworkPeeringsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualNetworkTapsClient creates a new instance of VirtualNetworkTapsClient.
func (c *ClientFactory) NewVirtualNetworkTapsClient() *VirtualNetworkTapsClient {
	subClient, _ := NewVirtualNetworkTapsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualNetworksClient creates a new instance of VirtualNetworksClient.
func (c *ClientFactory) NewVirtualNetworksClient() *VirtualNetworksClient {
	subClient, _ := NewVirtualNetworksClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualRouterPeeringsClient creates a new instance of VirtualRouterPeeringsClient.
func (c *ClientFactory) NewVirtualRouterPeeringsClient() *VirtualRouterPeeringsClient {
	subClient, _ := NewVirtualRouterPeeringsClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualRoutersClient creates a new instance of VirtualRoutersClient.
func (c *ClientFactory) NewVirtualRoutersClient() *VirtualRoutersClient {
	subClient, _ := NewVirtualRoutersClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewVirtualWansClient creates a new instance of VirtualWansClient.
func (c *ClientFactory) NewVirtualWansClient() *VirtualWansClient {
	subClient, _ := NewVirtualWansClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewWatchersClient creates a new instance of WatchersClient.
func (c *ClientFactory) NewWatchersClient() *WatchersClient {
	subClient, _ := NewWatchersClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewWebApplicationFirewallPoliciesClient creates a new instance of WebApplicationFirewallPoliciesClient.
func (c *ClientFactory) NewWebApplicationFirewallPoliciesClient() *WebApplicationFirewallPoliciesClient {
	subClient, _ := NewWebApplicationFirewallPoliciesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}

// NewWebCategoriesClient creates a new instance of WebCategoriesClient.
func (c *ClientFactory) NewWebCategoriesClient() *WebCategoriesClient {
	subClient, _ := NewWebCategoriesClient(c.subscriptionID, c.credential, c.options)
	return subClient
}
