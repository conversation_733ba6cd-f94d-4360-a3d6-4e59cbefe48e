//go:build go1.18
// +build go1.18

// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License. See License.txt in the project root for license information.
// Code generated by Microsoft (R) AutoRest Code Generator. DO NOT EDIT.
// Changes may cause incorrect behavior and will be lost if the code is regenerated.

package armnetwork

const (
	moduleName    = "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/network/armnetwork"
	moduleVersion = "v4.3.0"
)

// Access - Access to be allowed or denied.
type Access string

const (
	AccessAllow Access = "Allow"
	AccessDeny  Access = "Deny"
)

// PossibleAccessValues returns the possible values for the Access const type.
func PossibleAccessValues() []Access {
	return []Access{
		AccessAllow,
		AccessDeny,
	}
}

// ActionType - Defines the action to take on rule match.
type ActionType string

const (
	ActionTypeAllow          ActionType = "Allow"
	ActionTypeAnomalyScoring ActionType = "AnomalyScoring"
	ActionTypeBlock          ActionType = "Block"
	ActionTypeLog            ActionType = "Log"
)

// PossibleActionTypeValues returns the possible values for the ActionType const type.
func PossibleActionTypeValues() []ActionType {
	return []ActionType{
		ActionTypeAllow,
		ActionTypeAnomalyScoring,
		ActionTypeBlock,
		ActionTypeLog,
	}
}

// AddressPrefixType - Address prefix type.
type AddressPrefixType string

const (
	AddressPrefixTypeIPPrefix   AddressPrefixType = "IPPrefix"
	AddressPrefixTypeServiceTag AddressPrefixType = "ServiceTag"
)

// PossibleAddressPrefixTypeValues returns the possible values for the AddressPrefixType const type.
func PossibleAddressPrefixTypeValues() []AddressPrefixType {
	return []AddressPrefixType{
		AddressPrefixTypeIPPrefix,
		AddressPrefixTypeServiceTag,
	}
}

// AdminRuleKind - Whether the rule is custom or default.
type AdminRuleKind string

const (
	AdminRuleKindCustom  AdminRuleKind = "Custom"
	AdminRuleKindDefault AdminRuleKind = "Default"
)

// PossibleAdminRuleKindValues returns the possible values for the AdminRuleKind const type.
func PossibleAdminRuleKindValues() []AdminRuleKind {
	return []AdminRuleKind{
		AdminRuleKindCustom,
		AdminRuleKindDefault,
	}
}

// AdminState - Property to indicate if the Express Route Gateway serves traffic when there are multiple Express Route Gateways
// in the vnet
type AdminState string

const (
	AdminStateDisabled AdminState = "Disabled"
	AdminStateEnabled  AdminState = "Enabled"
)

// PossibleAdminStateValues returns the possible values for the AdminState const type.
func PossibleAdminStateValues() []AdminState {
	return []AdminState{
		AdminStateDisabled,
		AdminStateEnabled,
	}
}

// ApplicationGatewayBackendHealthServerHealth - Health of backend server.
type ApplicationGatewayBackendHealthServerHealth string

const (
	ApplicationGatewayBackendHealthServerHealthDown     ApplicationGatewayBackendHealthServerHealth = "Down"
	ApplicationGatewayBackendHealthServerHealthDraining ApplicationGatewayBackendHealthServerHealth = "Draining"
	ApplicationGatewayBackendHealthServerHealthPartial  ApplicationGatewayBackendHealthServerHealth = "Partial"
	ApplicationGatewayBackendHealthServerHealthUnknown  ApplicationGatewayBackendHealthServerHealth = "Unknown"
	ApplicationGatewayBackendHealthServerHealthUp       ApplicationGatewayBackendHealthServerHealth = "Up"
)

// PossibleApplicationGatewayBackendHealthServerHealthValues returns the possible values for the ApplicationGatewayBackendHealthServerHealth const type.
func PossibleApplicationGatewayBackendHealthServerHealthValues() []ApplicationGatewayBackendHealthServerHealth {
	return []ApplicationGatewayBackendHealthServerHealth{
		ApplicationGatewayBackendHealthServerHealthDown,
		ApplicationGatewayBackendHealthServerHealthDraining,
		ApplicationGatewayBackendHealthServerHealthPartial,
		ApplicationGatewayBackendHealthServerHealthUnknown,
		ApplicationGatewayBackendHealthServerHealthUp,
	}
}

// ApplicationGatewayClientRevocationOptions - Verify client certificate revocation status.
type ApplicationGatewayClientRevocationOptions string

const (
	ApplicationGatewayClientRevocationOptionsNone ApplicationGatewayClientRevocationOptions = "None"
	ApplicationGatewayClientRevocationOptionsOCSP ApplicationGatewayClientRevocationOptions = "OCSP"
)

// PossibleApplicationGatewayClientRevocationOptionsValues returns the possible values for the ApplicationGatewayClientRevocationOptions const type.
func PossibleApplicationGatewayClientRevocationOptionsValues() []ApplicationGatewayClientRevocationOptions {
	return []ApplicationGatewayClientRevocationOptions{
		ApplicationGatewayClientRevocationOptionsNone,
		ApplicationGatewayClientRevocationOptionsOCSP,
	}
}

// ApplicationGatewayCookieBasedAffinity - Cookie based affinity.
type ApplicationGatewayCookieBasedAffinity string

const (
	ApplicationGatewayCookieBasedAffinityDisabled ApplicationGatewayCookieBasedAffinity = "Disabled"
	ApplicationGatewayCookieBasedAffinityEnabled  ApplicationGatewayCookieBasedAffinity = "Enabled"
)

// PossibleApplicationGatewayCookieBasedAffinityValues returns the possible values for the ApplicationGatewayCookieBasedAffinity const type.
func PossibleApplicationGatewayCookieBasedAffinityValues() []ApplicationGatewayCookieBasedAffinity {
	return []ApplicationGatewayCookieBasedAffinity{
		ApplicationGatewayCookieBasedAffinityDisabled,
		ApplicationGatewayCookieBasedAffinityEnabled,
	}
}

// ApplicationGatewayCustomErrorStatusCode - Status code of the application gateway custom error.
type ApplicationGatewayCustomErrorStatusCode string

const (
	ApplicationGatewayCustomErrorStatusCodeHTTPStatus400 ApplicationGatewayCustomErrorStatusCode = "HttpStatus400"
	ApplicationGatewayCustomErrorStatusCodeHTTPStatus403 ApplicationGatewayCustomErrorStatusCode = "HttpStatus403"
	ApplicationGatewayCustomErrorStatusCodeHTTPStatus404 ApplicationGatewayCustomErrorStatusCode = "HttpStatus404"
	ApplicationGatewayCustomErrorStatusCodeHTTPStatus405 ApplicationGatewayCustomErrorStatusCode = "HttpStatus405"
	ApplicationGatewayCustomErrorStatusCodeHTTPStatus408 ApplicationGatewayCustomErrorStatusCode = "HttpStatus408"
	ApplicationGatewayCustomErrorStatusCodeHTTPStatus500 ApplicationGatewayCustomErrorStatusCode = "HttpStatus500"
	ApplicationGatewayCustomErrorStatusCodeHTTPStatus502 ApplicationGatewayCustomErrorStatusCode = "HttpStatus502"
	ApplicationGatewayCustomErrorStatusCodeHTTPStatus503 ApplicationGatewayCustomErrorStatusCode = "HttpStatus503"
	ApplicationGatewayCustomErrorStatusCodeHTTPStatus504 ApplicationGatewayCustomErrorStatusCode = "HttpStatus504"
)

// PossibleApplicationGatewayCustomErrorStatusCodeValues returns the possible values for the ApplicationGatewayCustomErrorStatusCode const type.
func PossibleApplicationGatewayCustomErrorStatusCodeValues() []ApplicationGatewayCustomErrorStatusCode {
	return []ApplicationGatewayCustomErrorStatusCode{
		ApplicationGatewayCustomErrorStatusCodeHTTPStatus400,
		ApplicationGatewayCustomErrorStatusCodeHTTPStatus403,
		ApplicationGatewayCustomErrorStatusCodeHTTPStatus404,
		ApplicationGatewayCustomErrorStatusCodeHTTPStatus405,
		ApplicationGatewayCustomErrorStatusCodeHTTPStatus408,
		ApplicationGatewayCustomErrorStatusCodeHTTPStatus500,
		ApplicationGatewayCustomErrorStatusCodeHTTPStatus502,
		ApplicationGatewayCustomErrorStatusCodeHTTPStatus503,
		ApplicationGatewayCustomErrorStatusCodeHTTPStatus504,
	}
}

// ApplicationGatewayFirewallMode - Web application firewall mode.
type ApplicationGatewayFirewallMode string

const (
	ApplicationGatewayFirewallModeDetection  ApplicationGatewayFirewallMode = "Detection"
	ApplicationGatewayFirewallModePrevention ApplicationGatewayFirewallMode = "Prevention"
)

// PossibleApplicationGatewayFirewallModeValues returns the possible values for the ApplicationGatewayFirewallMode const type.
func PossibleApplicationGatewayFirewallModeValues() []ApplicationGatewayFirewallMode {
	return []ApplicationGatewayFirewallMode{
		ApplicationGatewayFirewallModeDetection,
		ApplicationGatewayFirewallModePrevention,
	}
}

// ApplicationGatewayFirewallRateLimitDuration - Duration over which Rate Limit policy will be applied. Applies only when
// ruleType is RateLimitRule.
type ApplicationGatewayFirewallRateLimitDuration string

const (
	ApplicationGatewayFirewallRateLimitDurationFiveMins ApplicationGatewayFirewallRateLimitDuration = "FiveMins"
	ApplicationGatewayFirewallRateLimitDurationOneMin   ApplicationGatewayFirewallRateLimitDuration = "OneMin"
)

// PossibleApplicationGatewayFirewallRateLimitDurationValues returns the possible values for the ApplicationGatewayFirewallRateLimitDuration const type.
func PossibleApplicationGatewayFirewallRateLimitDurationValues() []ApplicationGatewayFirewallRateLimitDuration {
	return []ApplicationGatewayFirewallRateLimitDuration{
		ApplicationGatewayFirewallRateLimitDurationFiveMins,
		ApplicationGatewayFirewallRateLimitDurationOneMin,
	}
}

// ApplicationGatewayFirewallUserSessionVariable - User Session clause variable.
type ApplicationGatewayFirewallUserSessionVariable string

const (
	ApplicationGatewayFirewallUserSessionVariableClientAddr  ApplicationGatewayFirewallUserSessionVariable = "ClientAddr"
	ApplicationGatewayFirewallUserSessionVariableGeoLocation ApplicationGatewayFirewallUserSessionVariable = "GeoLocation"
	ApplicationGatewayFirewallUserSessionVariableNone        ApplicationGatewayFirewallUserSessionVariable = "None"
)

// PossibleApplicationGatewayFirewallUserSessionVariableValues returns the possible values for the ApplicationGatewayFirewallUserSessionVariable const type.
func PossibleApplicationGatewayFirewallUserSessionVariableValues() []ApplicationGatewayFirewallUserSessionVariable {
	return []ApplicationGatewayFirewallUserSessionVariable{
		ApplicationGatewayFirewallUserSessionVariableClientAddr,
		ApplicationGatewayFirewallUserSessionVariableGeoLocation,
		ApplicationGatewayFirewallUserSessionVariableNone,
	}
}

// ApplicationGatewayLoadDistributionAlgorithm - Load Distribution Algorithm enums.
type ApplicationGatewayLoadDistributionAlgorithm string

const (
	ApplicationGatewayLoadDistributionAlgorithmIPHash           ApplicationGatewayLoadDistributionAlgorithm = "IpHash"
	ApplicationGatewayLoadDistributionAlgorithmLeastConnections ApplicationGatewayLoadDistributionAlgorithm = "LeastConnections"
	ApplicationGatewayLoadDistributionAlgorithmRoundRobin       ApplicationGatewayLoadDistributionAlgorithm = "RoundRobin"
)

// PossibleApplicationGatewayLoadDistributionAlgorithmValues returns the possible values for the ApplicationGatewayLoadDistributionAlgorithm const type.
func PossibleApplicationGatewayLoadDistributionAlgorithmValues() []ApplicationGatewayLoadDistributionAlgorithm {
	return []ApplicationGatewayLoadDistributionAlgorithm{
		ApplicationGatewayLoadDistributionAlgorithmIPHash,
		ApplicationGatewayLoadDistributionAlgorithmLeastConnections,
		ApplicationGatewayLoadDistributionAlgorithmRoundRobin,
	}
}

// ApplicationGatewayOperationalState - Operational state of the application gateway resource.
type ApplicationGatewayOperationalState string

const (
	ApplicationGatewayOperationalStateRunning  ApplicationGatewayOperationalState = "Running"
	ApplicationGatewayOperationalStateStarting ApplicationGatewayOperationalState = "Starting"
	ApplicationGatewayOperationalStateStopped  ApplicationGatewayOperationalState = "Stopped"
	ApplicationGatewayOperationalStateStopping ApplicationGatewayOperationalState = "Stopping"
)

// PossibleApplicationGatewayOperationalStateValues returns the possible values for the ApplicationGatewayOperationalState const type.
func PossibleApplicationGatewayOperationalStateValues() []ApplicationGatewayOperationalState {
	return []ApplicationGatewayOperationalState{
		ApplicationGatewayOperationalStateRunning,
		ApplicationGatewayOperationalStateStarting,
		ApplicationGatewayOperationalStateStopped,
		ApplicationGatewayOperationalStateStopping,
	}
}

// ApplicationGatewayProtocol - Application Gateway protocol.
type ApplicationGatewayProtocol string

const (
	ApplicationGatewayProtocolHTTP  ApplicationGatewayProtocol = "Http"
	ApplicationGatewayProtocolHTTPS ApplicationGatewayProtocol = "Https"
	ApplicationGatewayProtocolTCP   ApplicationGatewayProtocol = "Tcp"
	ApplicationGatewayProtocolTLS   ApplicationGatewayProtocol = "Tls"
)

// PossibleApplicationGatewayProtocolValues returns the possible values for the ApplicationGatewayProtocol const type.
func PossibleApplicationGatewayProtocolValues() []ApplicationGatewayProtocol {
	return []ApplicationGatewayProtocol{
		ApplicationGatewayProtocolHTTP,
		ApplicationGatewayProtocolHTTPS,
		ApplicationGatewayProtocolTCP,
		ApplicationGatewayProtocolTLS,
	}
}

// ApplicationGatewayRedirectType - Redirect type enum.
type ApplicationGatewayRedirectType string

const (
	ApplicationGatewayRedirectTypeFound     ApplicationGatewayRedirectType = "Found"
	ApplicationGatewayRedirectTypePermanent ApplicationGatewayRedirectType = "Permanent"
	ApplicationGatewayRedirectTypeSeeOther  ApplicationGatewayRedirectType = "SeeOther"
	ApplicationGatewayRedirectTypeTemporary ApplicationGatewayRedirectType = "Temporary"
)

// PossibleApplicationGatewayRedirectTypeValues returns the possible values for the ApplicationGatewayRedirectType const type.
func PossibleApplicationGatewayRedirectTypeValues() []ApplicationGatewayRedirectType {
	return []ApplicationGatewayRedirectType{
		ApplicationGatewayRedirectTypeFound,
		ApplicationGatewayRedirectTypePermanent,
		ApplicationGatewayRedirectTypeSeeOther,
		ApplicationGatewayRedirectTypeTemporary,
	}
}

// ApplicationGatewayRequestRoutingRuleType - Rule type.
type ApplicationGatewayRequestRoutingRuleType string

const (
	ApplicationGatewayRequestRoutingRuleTypeBasic            ApplicationGatewayRequestRoutingRuleType = "Basic"
	ApplicationGatewayRequestRoutingRuleTypePathBasedRouting ApplicationGatewayRequestRoutingRuleType = "PathBasedRouting"
)

// PossibleApplicationGatewayRequestRoutingRuleTypeValues returns the possible values for the ApplicationGatewayRequestRoutingRuleType const type.
func PossibleApplicationGatewayRequestRoutingRuleTypeValues() []ApplicationGatewayRequestRoutingRuleType {
	return []ApplicationGatewayRequestRoutingRuleType{
		ApplicationGatewayRequestRoutingRuleTypeBasic,
		ApplicationGatewayRequestRoutingRuleTypePathBasedRouting,
	}
}

// ApplicationGatewayRuleSetStatusOptions - The rule set status
type ApplicationGatewayRuleSetStatusOptions string

const (
	ApplicationGatewayRuleSetStatusOptionsDeprecated ApplicationGatewayRuleSetStatusOptions = "Deprecated"
	ApplicationGatewayRuleSetStatusOptionsGA         ApplicationGatewayRuleSetStatusOptions = "GA"
	ApplicationGatewayRuleSetStatusOptionsPreview    ApplicationGatewayRuleSetStatusOptions = "Preview"
	ApplicationGatewayRuleSetStatusOptionsSupported  ApplicationGatewayRuleSetStatusOptions = "Supported"
)

// PossibleApplicationGatewayRuleSetStatusOptionsValues returns the possible values for the ApplicationGatewayRuleSetStatusOptions const type.
func PossibleApplicationGatewayRuleSetStatusOptionsValues() []ApplicationGatewayRuleSetStatusOptions {
	return []ApplicationGatewayRuleSetStatusOptions{
		ApplicationGatewayRuleSetStatusOptionsDeprecated,
		ApplicationGatewayRuleSetStatusOptionsGA,
		ApplicationGatewayRuleSetStatusOptionsPreview,
		ApplicationGatewayRuleSetStatusOptionsSupported,
	}
}

// ApplicationGatewaySKUName - Name of an application gateway SKU.
type ApplicationGatewaySKUName string

const (
	ApplicationGatewaySKUNameBasic          ApplicationGatewaySKUName = "Basic"
	ApplicationGatewaySKUNameStandardLarge  ApplicationGatewaySKUName = "Standard_Large"
	ApplicationGatewaySKUNameStandardMedium ApplicationGatewaySKUName = "Standard_Medium"
	ApplicationGatewaySKUNameStandardSmall  ApplicationGatewaySKUName = "Standard_Small"
	ApplicationGatewaySKUNameStandardV2     ApplicationGatewaySKUName = "Standard_v2"
	ApplicationGatewaySKUNameWAFLarge       ApplicationGatewaySKUName = "WAF_Large"
	ApplicationGatewaySKUNameWAFMedium      ApplicationGatewaySKUName = "WAF_Medium"
	ApplicationGatewaySKUNameWAFV2          ApplicationGatewaySKUName = "WAF_v2"
)

// PossibleApplicationGatewaySKUNameValues returns the possible values for the ApplicationGatewaySKUName const type.
func PossibleApplicationGatewaySKUNameValues() []ApplicationGatewaySKUName {
	return []ApplicationGatewaySKUName{
		ApplicationGatewaySKUNameBasic,
		ApplicationGatewaySKUNameStandardLarge,
		ApplicationGatewaySKUNameStandardMedium,
		ApplicationGatewaySKUNameStandardSmall,
		ApplicationGatewaySKUNameStandardV2,
		ApplicationGatewaySKUNameWAFLarge,
		ApplicationGatewaySKUNameWAFMedium,
		ApplicationGatewaySKUNameWAFV2,
	}
}

// ApplicationGatewaySSLCipherSuite - Ssl cipher suites enums.
type ApplicationGatewaySSLCipherSuite string

const (
	ApplicationGatewaySSLCipherSuiteTLSDHEDSSWITH3DESEDECBCSHA       ApplicationGatewaySSLCipherSuite = "TLS_DHE_DSS_WITH_3DES_EDE_CBC_SHA"
	ApplicationGatewaySSLCipherSuiteTLSDHEDSSWITHAES128CBCSHA        ApplicationGatewaySSLCipherSuite = "TLS_DHE_DSS_WITH_AES_128_CBC_SHA"
	ApplicationGatewaySSLCipherSuiteTLSDHEDSSWITHAES128CBCSHA256     ApplicationGatewaySSLCipherSuite = "TLS_DHE_DSS_WITH_AES_128_CBC_SHA256"
	ApplicationGatewaySSLCipherSuiteTLSDHEDSSWITHAES256CBCSHA        ApplicationGatewaySSLCipherSuite = "TLS_DHE_DSS_WITH_AES_256_CBC_SHA"
	ApplicationGatewaySSLCipherSuiteTLSDHEDSSWITHAES256CBCSHA256     ApplicationGatewaySSLCipherSuite = "TLS_DHE_DSS_WITH_AES_256_CBC_SHA256"
	ApplicationGatewaySSLCipherSuiteTLSDHERSAWITHAES128CBCSHA        ApplicationGatewaySSLCipherSuite = "TLS_DHE_RSA_WITH_AES_128_CBC_SHA"
	ApplicationGatewaySSLCipherSuiteTLSDHERSAWITHAES128GCMSHA256     ApplicationGatewaySSLCipherSuite = "TLS_DHE_RSA_WITH_AES_128_GCM_SHA256"
	ApplicationGatewaySSLCipherSuiteTLSDHERSAWITHAES256CBCSHA        ApplicationGatewaySSLCipherSuite = "TLS_DHE_RSA_WITH_AES_256_CBC_SHA"
	ApplicationGatewaySSLCipherSuiteTLSDHERSAWITHAES256GCMSHA384     ApplicationGatewaySSLCipherSuite = "TLS_DHE_RSA_WITH_AES_256_GCM_SHA384"
	ApplicationGatewaySSLCipherSuiteTLSECDHEECDSAWITHAES128CBCSHA    ApplicationGatewaySSLCipherSuite = "TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA"
	ApplicationGatewaySSLCipherSuiteTLSECDHEECDSAWITHAES128CBCSHA256 ApplicationGatewaySSLCipherSuite = "TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256"
	ApplicationGatewaySSLCipherSuiteTLSECDHEECDSAWITHAES128GCMSHA256 ApplicationGatewaySSLCipherSuite = "TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256"
	ApplicationGatewaySSLCipherSuiteTLSECDHEECDSAWITHAES256CBCSHA    ApplicationGatewaySSLCipherSuite = "TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA"
	ApplicationGatewaySSLCipherSuiteTLSECDHEECDSAWITHAES256CBCSHA384 ApplicationGatewaySSLCipherSuite = "TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384"
	ApplicationGatewaySSLCipherSuiteTLSECDHEECDSAWITHAES256GCMSHA384 ApplicationGatewaySSLCipherSuite = "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384"
	ApplicationGatewaySSLCipherSuiteTLSECDHERSAWITHAES128CBCSHA      ApplicationGatewaySSLCipherSuite = "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA"
	ApplicationGatewaySSLCipherSuiteTLSECDHERSAWITHAES128CBCSHA256   ApplicationGatewaySSLCipherSuite = "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256"
	ApplicationGatewaySSLCipherSuiteTLSECDHERSAWITHAES128GCMSHA256   ApplicationGatewaySSLCipherSuite = "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
	ApplicationGatewaySSLCipherSuiteTLSECDHERSAWITHAES256CBCSHA      ApplicationGatewaySSLCipherSuite = "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA"
	ApplicationGatewaySSLCipherSuiteTLSECDHERSAWITHAES256CBCSHA384   ApplicationGatewaySSLCipherSuite = "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384"
	ApplicationGatewaySSLCipherSuiteTLSECDHERSAWITHAES256GCMSHA384   ApplicationGatewaySSLCipherSuite = "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
	ApplicationGatewaySSLCipherSuiteTLSRSAWITH3DESEDECBCSHA          ApplicationGatewaySSLCipherSuite = "TLS_RSA_WITH_3DES_EDE_CBC_SHA"
	ApplicationGatewaySSLCipherSuiteTLSRSAWITHAES128CBCSHA           ApplicationGatewaySSLCipherSuite = "TLS_RSA_WITH_AES_128_CBC_SHA"
	ApplicationGatewaySSLCipherSuiteTLSRSAWITHAES128CBCSHA256        ApplicationGatewaySSLCipherSuite = "TLS_RSA_WITH_AES_128_CBC_SHA256"
	ApplicationGatewaySSLCipherSuiteTLSRSAWITHAES128GCMSHA256        ApplicationGatewaySSLCipherSuite = "TLS_RSA_WITH_AES_128_GCM_SHA256"
	ApplicationGatewaySSLCipherSuiteTLSRSAWITHAES256CBCSHA           ApplicationGatewaySSLCipherSuite = "TLS_RSA_WITH_AES_256_CBC_SHA"
	ApplicationGatewaySSLCipherSuiteTLSRSAWITHAES256CBCSHA256        ApplicationGatewaySSLCipherSuite = "TLS_RSA_WITH_AES_256_CBC_SHA256"
	ApplicationGatewaySSLCipherSuiteTLSRSAWITHAES256GCMSHA384        ApplicationGatewaySSLCipherSuite = "TLS_RSA_WITH_AES_256_GCM_SHA384"
)

// PossibleApplicationGatewaySSLCipherSuiteValues returns the possible values for the ApplicationGatewaySSLCipherSuite const type.
func PossibleApplicationGatewaySSLCipherSuiteValues() []ApplicationGatewaySSLCipherSuite {
	return []ApplicationGatewaySSLCipherSuite{
		ApplicationGatewaySSLCipherSuiteTLSDHEDSSWITH3DESEDECBCSHA,
		ApplicationGatewaySSLCipherSuiteTLSDHEDSSWITHAES128CBCSHA,
		ApplicationGatewaySSLCipherSuiteTLSDHEDSSWITHAES128CBCSHA256,
		ApplicationGatewaySSLCipherSuiteTLSDHEDSSWITHAES256CBCSHA,
		ApplicationGatewaySSLCipherSuiteTLSDHEDSSWITHAES256CBCSHA256,
		ApplicationGatewaySSLCipherSuiteTLSDHERSAWITHAES128CBCSHA,
		ApplicationGatewaySSLCipherSuiteTLSDHERSAWITHAES128GCMSHA256,
		ApplicationGatewaySSLCipherSuiteTLSDHERSAWITHAES256CBCSHA,
		ApplicationGatewaySSLCipherSuiteTLSDHERSAWITHAES256GCMSHA384,
		ApplicationGatewaySSLCipherSuiteTLSECDHEECDSAWITHAES128CBCSHA,
		ApplicationGatewaySSLCipherSuiteTLSECDHEECDSAWITHAES128CBCSHA256,
		ApplicationGatewaySSLCipherSuiteTLSECDHEECDSAWITHAES128GCMSHA256,
		ApplicationGatewaySSLCipherSuiteTLSECDHEECDSAWITHAES256CBCSHA,
		ApplicationGatewaySSLCipherSuiteTLSECDHEECDSAWITHAES256CBCSHA384,
		ApplicationGatewaySSLCipherSuiteTLSECDHEECDSAWITHAES256GCMSHA384,
		ApplicationGatewaySSLCipherSuiteTLSECDHERSAWITHAES128CBCSHA,
		ApplicationGatewaySSLCipherSuiteTLSECDHERSAWITHAES128CBCSHA256,
		ApplicationGatewaySSLCipherSuiteTLSECDHERSAWITHAES128GCMSHA256,
		ApplicationGatewaySSLCipherSuiteTLSECDHERSAWITHAES256CBCSHA,
		ApplicationGatewaySSLCipherSuiteTLSECDHERSAWITHAES256CBCSHA384,
		ApplicationGatewaySSLCipherSuiteTLSECDHERSAWITHAES256GCMSHA384,
		ApplicationGatewaySSLCipherSuiteTLSRSAWITH3DESEDECBCSHA,
		ApplicationGatewaySSLCipherSuiteTLSRSAWITHAES128CBCSHA,
		ApplicationGatewaySSLCipherSuiteTLSRSAWITHAES128CBCSHA256,
		ApplicationGatewaySSLCipherSuiteTLSRSAWITHAES128GCMSHA256,
		ApplicationGatewaySSLCipherSuiteTLSRSAWITHAES256CBCSHA,
		ApplicationGatewaySSLCipherSuiteTLSRSAWITHAES256CBCSHA256,
		ApplicationGatewaySSLCipherSuiteTLSRSAWITHAES256GCMSHA384,
	}
}

// ApplicationGatewaySSLPolicyName - Ssl predefined policy name enums.
type ApplicationGatewaySSLPolicyName string

const (
	ApplicationGatewaySSLPolicyNameAppGwSSLPolicy20150501  ApplicationGatewaySSLPolicyName = "AppGwSslPolicy20150501"
	ApplicationGatewaySSLPolicyNameAppGwSSLPolicy20170401  ApplicationGatewaySSLPolicyName = "AppGwSslPolicy20170401"
	ApplicationGatewaySSLPolicyNameAppGwSSLPolicy20170401S ApplicationGatewaySSLPolicyName = "AppGwSslPolicy20170401S"
	ApplicationGatewaySSLPolicyNameAppGwSSLPolicy20220101  ApplicationGatewaySSLPolicyName = "AppGwSslPolicy20220101"
	ApplicationGatewaySSLPolicyNameAppGwSSLPolicy20220101S ApplicationGatewaySSLPolicyName = "AppGwSslPolicy20220101S"
)

// PossibleApplicationGatewaySSLPolicyNameValues returns the possible values for the ApplicationGatewaySSLPolicyName const type.
func PossibleApplicationGatewaySSLPolicyNameValues() []ApplicationGatewaySSLPolicyName {
	return []ApplicationGatewaySSLPolicyName{
		ApplicationGatewaySSLPolicyNameAppGwSSLPolicy20150501,
		ApplicationGatewaySSLPolicyNameAppGwSSLPolicy20170401,
		ApplicationGatewaySSLPolicyNameAppGwSSLPolicy20170401S,
		ApplicationGatewaySSLPolicyNameAppGwSSLPolicy20220101,
		ApplicationGatewaySSLPolicyNameAppGwSSLPolicy20220101S,
	}
}

// ApplicationGatewaySSLPolicyType - Type of Ssl Policy.
type ApplicationGatewaySSLPolicyType string

const (
	ApplicationGatewaySSLPolicyTypeCustom     ApplicationGatewaySSLPolicyType = "Custom"
	ApplicationGatewaySSLPolicyTypeCustomV2   ApplicationGatewaySSLPolicyType = "CustomV2"
	ApplicationGatewaySSLPolicyTypePredefined ApplicationGatewaySSLPolicyType = "Predefined"
)

// PossibleApplicationGatewaySSLPolicyTypeValues returns the possible values for the ApplicationGatewaySSLPolicyType const type.
func PossibleApplicationGatewaySSLPolicyTypeValues() []ApplicationGatewaySSLPolicyType {
	return []ApplicationGatewaySSLPolicyType{
		ApplicationGatewaySSLPolicyTypeCustom,
		ApplicationGatewaySSLPolicyTypeCustomV2,
		ApplicationGatewaySSLPolicyTypePredefined,
	}
}

// ApplicationGatewaySSLProtocol - Ssl protocol enums.
type ApplicationGatewaySSLProtocol string

const (
	ApplicationGatewaySSLProtocolTLSv10 ApplicationGatewaySSLProtocol = "TLSv1_0"
	ApplicationGatewaySSLProtocolTLSv11 ApplicationGatewaySSLProtocol = "TLSv1_1"
	ApplicationGatewaySSLProtocolTLSv12 ApplicationGatewaySSLProtocol = "TLSv1_2"
	ApplicationGatewaySSLProtocolTLSv13 ApplicationGatewaySSLProtocol = "TLSv1_3"
)

// PossibleApplicationGatewaySSLProtocolValues returns the possible values for the ApplicationGatewaySSLProtocol const type.
func PossibleApplicationGatewaySSLProtocolValues() []ApplicationGatewaySSLProtocol {
	return []ApplicationGatewaySSLProtocol{
		ApplicationGatewaySSLProtocolTLSv10,
		ApplicationGatewaySSLProtocolTLSv11,
		ApplicationGatewaySSLProtocolTLSv12,
		ApplicationGatewaySSLProtocolTLSv13,
	}
}

// ApplicationGatewayTier - Tier of an application gateway.
type ApplicationGatewayTier string

const (
	ApplicationGatewayTierBasic      ApplicationGatewayTier = "Basic"
	ApplicationGatewayTierStandard   ApplicationGatewayTier = "Standard"
	ApplicationGatewayTierStandardV2 ApplicationGatewayTier = "Standard_v2"
	ApplicationGatewayTierWAF        ApplicationGatewayTier = "WAF"
	ApplicationGatewayTierWAFV2      ApplicationGatewayTier = "WAF_v2"
)

// PossibleApplicationGatewayTierValues returns the possible values for the ApplicationGatewayTier const type.
func PossibleApplicationGatewayTierValues() []ApplicationGatewayTier {
	return []ApplicationGatewayTier{
		ApplicationGatewayTierBasic,
		ApplicationGatewayTierStandard,
		ApplicationGatewayTierStandardV2,
		ApplicationGatewayTierWAF,
		ApplicationGatewayTierWAFV2,
	}
}

type ApplicationGatewayTierTypes string

const (
	ApplicationGatewayTierTypesStandard   ApplicationGatewayTierTypes = "Standard"
	ApplicationGatewayTierTypesStandardV2 ApplicationGatewayTierTypes = "Standard_v2"
	ApplicationGatewayTierTypesWAF        ApplicationGatewayTierTypes = "WAF"
	ApplicationGatewayTierTypesWAFV2      ApplicationGatewayTierTypes = "WAF_v2"
)

// PossibleApplicationGatewayTierTypesValues returns the possible values for the ApplicationGatewayTierTypes const type.
func PossibleApplicationGatewayTierTypesValues() []ApplicationGatewayTierTypes {
	return []ApplicationGatewayTierTypes{
		ApplicationGatewayTierTypesStandard,
		ApplicationGatewayTierTypesStandardV2,
		ApplicationGatewayTierTypesWAF,
		ApplicationGatewayTierTypesWAFV2,
	}
}

// ApplicationGatewayWafRuleActionTypes - The string representation of the web application firewall rule action.
type ApplicationGatewayWafRuleActionTypes string

const (
	ApplicationGatewayWafRuleActionTypesAllow          ApplicationGatewayWafRuleActionTypes = "Allow"
	ApplicationGatewayWafRuleActionTypesAnomalyScoring ApplicationGatewayWafRuleActionTypes = "AnomalyScoring"
	ApplicationGatewayWafRuleActionTypesBlock          ApplicationGatewayWafRuleActionTypes = "Block"
	ApplicationGatewayWafRuleActionTypesLog            ApplicationGatewayWafRuleActionTypes = "Log"
	ApplicationGatewayWafRuleActionTypesNone           ApplicationGatewayWafRuleActionTypes = "None"
)

// PossibleApplicationGatewayWafRuleActionTypesValues returns the possible values for the ApplicationGatewayWafRuleActionTypes const type.
func PossibleApplicationGatewayWafRuleActionTypesValues() []ApplicationGatewayWafRuleActionTypes {
	return []ApplicationGatewayWafRuleActionTypes{
		ApplicationGatewayWafRuleActionTypesAllow,
		ApplicationGatewayWafRuleActionTypesAnomalyScoring,
		ApplicationGatewayWafRuleActionTypesBlock,
		ApplicationGatewayWafRuleActionTypesLog,
		ApplicationGatewayWafRuleActionTypesNone,
	}
}

// ApplicationGatewayWafRuleStateTypes - The string representation of the web application firewall rule state.
type ApplicationGatewayWafRuleStateTypes string

const (
	ApplicationGatewayWafRuleStateTypesDisabled ApplicationGatewayWafRuleStateTypes = "Disabled"
	ApplicationGatewayWafRuleStateTypesEnabled  ApplicationGatewayWafRuleStateTypes = "Enabled"
)

// PossibleApplicationGatewayWafRuleStateTypesValues returns the possible values for the ApplicationGatewayWafRuleStateTypes const type.
func PossibleApplicationGatewayWafRuleStateTypesValues() []ApplicationGatewayWafRuleStateTypes {
	return []ApplicationGatewayWafRuleStateTypes{
		ApplicationGatewayWafRuleStateTypesDisabled,
		ApplicationGatewayWafRuleStateTypesEnabled,
	}
}

// AssociationType - The association type of the child resource to the parent resource.
type AssociationType string

const (
	AssociationTypeAssociated AssociationType = "Associated"
	AssociationTypeContains   AssociationType = "Contains"
)

// PossibleAssociationTypeValues returns the possible values for the AssociationType const type.
func PossibleAssociationTypeValues() []AssociationType {
	return []AssociationType{
		AssociationTypeAssociated,
		AssociationTypeContains,
	}
}

// AuthenticationMethod - VPN client authentication method.
type AuthenticationMethod string

const (
	AuthenticationMethodEAPMSCHAPv2 AuthenticationMethod = "EAPMSCHAPv2"
	AuthenticationMethodEAPTLS      AuthenticationMethod = "EAPTLS"
)

// PossibleAuthenticationMethodValues returns the possible values for the AuthenticationMethod const type.
func PossibleAuthenticationMethodValues() []AuthenticationMethod {
	return []AuthenticationMethod{
		AuthenticationMethodEAPMSCHAPv2,
		AuthenticationMethodEAPTLS,
	}
}

// AuthorizationUseStatus - The authorization use status.
type AuthorizationUseStatus string

const (
	AuthorizationUseStatusAvailable AuthorizationUseStatus = "Available"
	AuthorizationUseStatusInUse     AuthorizationUseStatus = "InUse"
)

// PossibleAuthorizationUseStatusValues returns the possible values for the AuthorizationUseStatus const type.
func PossibleAuthorizationUseStatusValues() []AuthorizationUseStatus {
	return []AuthorizationUseStatus{
		AuthorizationUseStatusAvailable,
		AuthorizationUseStatusInUse,
	}
}

// AutoLearnPrivateRangesMode - The operation mode for automatically learning private ranges to not be SNAT
type AutoLearnPrivateRangesMode string

const (
	AutoLearnPrivateRangesModeDisabled AutoLearnPrivateRangesMode = "Disabled"
	AutoLearnPrivateRangesModeEnabled  AutoLearnPrivateRangesMode = "Enabled"
)

// PossibleAutoLearnPrivateRangesModeValues returns the possible values for the AutoLearnPrivateRangesMode const type.
func PossibleAutoLearnPrivateRangesModeValues() []AutoLearnPrivateRangesMode {
	return []AutoLearnPrivateRangesMode{
		AutoLearnPrivateRangesModeDisabled,
		AutoLearnPrivateRangesModeEnabled,
	}
}

// AzureFirewallApplicationRuleProtocolType - The protocol type of a Application Rule resource.
type AzureFirewallApplicationRuleProtocolType string

const (
	AzureFirewallApplicationRuleProtocolTypeHTTP  AzureFirewallApplicationRuleProtocolType = "Http"
	AzureFirewallApplicationRuleProtocolTypeHTTPS AzureFirewallApplicationRuleProtocolType = "Https"
	AzureFirewallApplicationRuleProtocolTypeMssql AzureFirewallApplicationRuleProtocolType = "Mssql"
)

// PossibleAzureFirewallApplicationRuleProtocolTypeValues returns the possible values for the AzureFirewallApplicationRuleProtocolType const type.
func PossibleAzureFirewallApplicationRuleProtocolTypeValues() []AzureFirewallApplicationRuleProtocolType {
	return []AzureFirewallApplicationRuleProtocolType{
		AzureFirewallApplicationRuleProtocolTypeHTTP,
		AzureFirewallApplicationRuleProtocolTypeHTTPS,
		AzureFirewallApplicationRuleProtocolTypeMssql,
	}
}

// AzureFirewallNatRCActionType - The action type of a NAT rule collection.
type AzureFirewallNatRCActionType string

const (
	AzureFirewallNatRCActionTypeDnat AzureFirewallNatRCActionType = "Dnat"
	AzureFirewallNatRCActionTypeSnat AzureFirewallNatRCActionType = "Snat"
)

// PossibleAzureFirewallNatRCActionTypeValues returns the possible values for the AzureFirewallNatRCActionType const type.
func PossibleAzureFirewallNatRCActionTypeValues() []AzureFirewallNatRCActionType {
	return []AzureFirewallNatRCActionType{
		AzureFirewallNatRCActionTypeDnat,
		AzureFirewallNatRCActionTypeSnat,
	}
}

// AzureFirewallNetworkRuleProtocol - The protocol of a Network Rule resource.
type AzureFirewallNetworkRuleProtocol string

const (
	AzureFirewallNetworkRuleProtocolAny  AzureFirewallNetworkRuleProtocol = "Any"
	AzureFirewallNetworkRuleProtocolICMP AzureFirewallNetworkRuleProtocol = "ICMP"
	AzureFirewallNetworkRuleProtocolTCP  AzureFirewallNetworkRuleProtocol = "TCP"
	AzureFirewallNetworkRuleProtocolUDP  AzureFirewallNetworkRuleProtocol = "UDP"
)

// PossibleAzureFirewallNetworkRuleProtocolValues returns the possible values for the AzureFirewallNetworkRuleProtocol const type.
func PossibleAzureFirewallNetworkRuleProtocolValues() []AzureFirewallNetworkRuleProtocol {
	return []AzureFirewallNetworkRuleProtocol{
		AzureFirewallNetworkRuleProtocolAny,
		AzureFirewallNetworkRuleProtocolICMP,
		AzureFirewallNetworkRuleProtocolTCP,
		AzureFirewallNetworkRuleProtocolUDP,
	}
}

// AzureFirewallPacketCaptureFlagsType - The flags type to be captured.
type AzureFirewallPacketCaptureFlagsType string

const (
	AzureFirewallPacketCaptureFlagsTypeAck  AzureFirewallPacketCaptureFlagsType = "ack"
	AzureFirewallPacketCaptureFlagsTypeFin  AzureFirewallPacketCaptureFlagsType = "fin"
	AzureFirewallPacketCaptureFlagsTypePush AzureFirewallPacketCaptureFlagsType = "push"
	AzureFirewallPacketCaptureFlagsTypeRst  AzureFirewallPacketCaptureFlagsType = "rst"
	AzureFirewallPacketCaptureFlagsTypeSyn  AzureFirewallPacketCaptureFlagsType = "syn"
	AzureFirewallPacketCaptureFlagsTypeUrg  AzureFirewallPacketCaptureFlagsType = "urg"
)

// PossibleAzureFirewallPacketCaptureFlagsTypeValues returns the possible values for the AzureFirewallPacketCaptureFlagsType const type.
func PossibleAzureFirewallPacketCaptureFlagsTypeValues() []AzureFirewallPacketCaptureFlagsType {
	return []AzureFirewallPacketCaptureFlagsType{
		AzureFirewallPacketCaptureFlagsTypeAck,
		AzureFirewallPacketCaptureFlagsTypeFin,
		AzureFirewallPacketCaptureFlagsTypePush,
		AzureFirewallPacketCaptureFlagsTypeRst,
		AzureFirewallPacketCaptureFlagsTypeSyn,
		AzureFirewallPacketCaptureFlagsTypeUrg,
	}
}

// AzureFirewallRCActionType - The action type of a rule collection.
type AzureFirewallRCActionType string

const (
	AzureFirewallRCActionTypeAllow AzureFirewallRCActionType = "Allow"
	AzureFirewallRCActionTypeDeny  AzureFirewallRCActionType = "Deny"
)

// PossibleAzureFirewallRCActionTypeValues returns the possible values for the AzureFirewallRCActionType const type.
func PossibleAzureFirewallRCActionTypeValues() []AzureFirewallRCActionType {
	return []AzureFirewallRCActionType{
		AzureFirewallRCActionTypeAllow,
		AzureFirewallRCActionTypeDeny,
	}
}

// AzureFirewallSKUName - Name of an Azure Firewall SKU.
type AzureFirewallSKUName string

const (
	AzureFirewallSKUNameAZFWHub  AzureFirewallSKUName = "AZFW_Hub"
	AzureFirewallSKUNameAZFWVnet AzureFirewallSKUName = "AZFW_VNet"
)

// PossibleAzureFirewallSKUNameValues returns the possible values for the AzureFirewallSKUName const type.
func PossibleAzureFirewallSKUNameValues() []AzureFirewallSKUName {
	return []AzureFirewallSKUName{
		AzureFirewallSKUNameAZFWHub,
		AzureFirewallSKUNameAZFWVnet,
	}
}

// AzureFirewallSKUTier - Tier of an Azure Firewall.
type AzureFirewallSKUTier string

const (
	AzureFirewallSKUTierBasic    AzureFirewallSKUTier = "Basic"
	AzureFirewallSKUTierPremium  AzureFirewallSKUTier = "Premium"
	AzureFirewallSKUTierStandard AzureFirewallSKUTier = "Standard"
)

// PossibleAzureFirewallSKUTierValues returns the possible values for the AzureFirewallSKUTier const type.
func PossibleAzureFirewallSKUTierValues() []AzureFirewallSKUTier {
	return []AzureFirewallSKUTier{
		AzureFirewallSKUTierBasic,
		AzureFirewallSKUTierPremium,
		AzureFirewallSKUTierStandard,
	}
}

// AzureFirewallThreatIntelMode - The operation mode for Threat Intel.
type AzureFirewallThreatIntelMode string

const (
	AzureFirewallThreatIntelModeAlert AzureFirewallThreatIntelMode = "Alert"
	AzureFirewallThreatIntelModeDeny  AzureFirewallThreatIntelMode = "Deny"
	AzureFirewallThreatIntelModeOff   AzureFirewallThreatIntelMode = "Off"
)

// PossibleAzureFirewallThreatIntelModeValues returns the possible values for the AzureFirewallThreatIntelMode const type.
func PossibleAzureFirewallThreatIntelModeValues() []AzureFirewallThreatIntelMode {
	return []AzureFirewallThreatIntelMode{
		AzureFirewallThreatIntelModeAlert,
		AzureFirewallThreatIntelModeDeny,
		AzureFirewallThreatIntelModeOff,
	}
}

// BastionConnectProtocol - The protocol used to connect to the target.
type BastionConnectProtocol string

const (
	BastionConnectProtocolRDP BastionConnectProtocol = "RDP"
	BastionConnectProtocolSSH BastionConnectProtocol = "SSH"
)

// PossibleBastionConnectProtocolValues returns the possible values for the BastionConnectProtocol const type.
func PossibleBastionConnectProtocolValues() []BastionConnectProtocol {
	return []BastionConnectProtocol{
		BastionConnectProtocolRDP,
		BastionConnectProtocolSSH,
	}
}

// BastionHostSKUName - The name of this Bastion Host.
type BastionHostSKUName string

const (
	BastionHostSKUNameBasic    BastionHostSKUName = "Basic"
	BastionHostSKUNameStandard BastionHostSKUName = "Standard"
)

// PossibleBastionHostSKUNameValues returns the possible values for the BastionHostSKUName const type.
func PossibleBastionHostSKUNameValues() []BastionHostSKUName {
	return []BastionHostSKUName{
		BastionHostSKUNameBasic,
		BastionHostSKUNameStandard,
	}
}

// BgpPeerState - The BGP peer state.
type BgpPeerState string

const (
	BgpPeerStateConnected  BgpPeerState = "Connected"
	BgpPeerStateConnecting BgpPeerState = "Connecting"
	BgpPeerStateIdle       BgpPeerState = "Idle"
	BgpPeerStateStopped    BgpPeerState = "Stopped"
	BgpPeerStateUnknown    BgpPeerState = "Unknown"
)

// PossibleBgpPeerStateValues returns the possible values for the BgpPeerState const type.
func PossibleBgpPeerStateValues() []BgpPeerState {
	return []BgpPeerState{
		BgpPeerStateConnected,
		BgpPeerStateConnecting,
		BgpPeerStateIdle,
		BgpPeerStateStopped,
		BgpPeerStateUnknown,
	}
}

// CircuitConnectionStatus - Express Route Circuit connection state.
type CircuitConnectionStatus string

const (
	CircuitConnectionStatusConnected    CircuitConnectionStatus = "Connected"
	CircuitConnectionStatusConnecting   CircuitConnectionStatus = "Connecting"
	CircuitConnectionStatusDisconnected CircuitConnectionStatus = "Disconnected"
)

// PossibleCircuitConnectionStatusValues returns the possible values for the CircuitConnectionStatus const type.
func PossibleCircuitConnectionStatusValues() []CircuitConnectionStatus {
	return []CircuitConnectionStatus{
		CircuitConnectionStatusConnected,
		CircuitConnectionStatusConnecting,
		CircuitConnectionStatusDisconnected,
	}
}

// CommissionedState - The commissioned state of the Custom IP Prefix.
type CommissionedState string

const (
	CommissionedStateCommissioned                    CommissionedState = "Commissioned"
	CommissionedStateCommissionedNoInternetAdvertise CommissionedState = "CommissionedNoInternetAdvertise"
	CommissionedStateCommissioning                   CommissionedState = "Commissioning"
	CommissionedStateDecommissioning                 CommissionedState = "Decommissioning"
	CommissionedStateDeprovisioned                   CommissionedState = "Deprovisioned"
	CommissionedStateDeprovisioning                  CommissionedState = "Deprovisioning"
	CommissionedStateProvisioned                     CommissionedState = "Provisioned"
	CommissionedStateProvisioning                    CommissionedState = "Provisioning"
)

// PossibleCommissionedStateValues returns the possible values for the CommissionedState const type.
func PossibleCommissionedStateValues() []CommissionedState {
	return []CommissionedState{
		CommissionedStateCommissioned,
		CommissionedStateCommissionedNoInternetAdvertise,
		CommissionedStateCommissioning,
		CommissionedStateDecommissioning,
		CommissionedStateDeprovisioned,
		CommissionedStateDeprovisioning,
		CommissionedStateProvisioned,
		CommissionedStateProvisioning,
	}
}

// ConfigurationType - Configuration Deployment Type.
type ConfigurationType string

const (
	ConfigurationTypeConnectivity  ConfigurationType = "Connectivity"
	ConfigurationTypeSecurityAdmin ConfigurationType = "SecurityAdmin"
)

// PossibleConfigurationTypeValues returns the possible values for the ConfigurationType const type.
func PossibleConfigurationTypeValues() []ConfigurationType {
	return []ConfigurationType{
		ConfigurationTypeConnectivity,
		ConfigurationTypeSecurityAdmin,
	}
}

// ConnectionMonitorEndpointFilterItemType - The type of item included in the filter. Currently only 'AgentAddress' is supported.
type ConnectionMonitorEndpointFilterItemType string

const (
	ConnectionMonitorEndpointFilterItemTypeAgentAddress ConnectionMonitorEndpointFilterItemType = "AgentAddress"
)

// PossibleConnectionMonitorEndpointFilterItemTypeValues returns the possible values for the ConnectionMonitorEndpointFilterItemType const type.
func PossibleConnectionMonitorEndpointFilterItemTypeValues() []ConnectionMonitorEndpointFilterItemType {
	return []ConnectionMonitorEndpointFilterItemType{
		ConnectionMonitorEndpointFilterItemTypeAgentAddress,
	}
}

// ConnectionMonitorEndpointFilterType - The behavior of the endpoint filter. Currently only 'Include' is supported.
type ConnectionMonitorEndpointFilterType string

const (
	ConnectionMonitorEndpointFilterTypeInclude ConnectionMonitorEndpointFilterType = "Include"
)

// PossibleConnectionMonitorEndpointFilterTypeValues returns the possible values for the ConnectionMonitorEndpointFilterType const type.
func PossibleConnectionMonitorEndpointFilterTypeValues() []ConnectionMonitorEndpointFilterType {
	return []ConnectionMonitorEndpointFilterType{
		ConnectionMonitorEndpointFilterTypeInclude,
	}
}

// ConnectionMonitorSourceStatus - Status of connection monitor source.
type ConnectionMonitorSourceStatus string

const (
	ConnectionMonitorSourceStatusActive   ConnectionMonitorSourceStatus = "Active"
	ConnectionMonitorSourceStatusInactive ConnectionMonitorSourceStatus = "Inactive"
	ConnectionMonitorSourceStatusUnknown  ConnectionMonitorSourceStatus = "Unknown"
)

// PossibleConnectionMonitorSourceStatusValues returns the possible values for the ConnectionMonitorSourceStatus const type.
func PossibleConnectionMonitorSourceStatusValues() []ConnectionMonitorSourceStatus {
	return []ConnectionMonitorSourceStatus{
		ConnectionMonitorSourceStatusActive,
		ConnectionMonitorSourceStatusInactive,
		ConnectionMonitorSourceStatusUnknown,
	}
}

// ConnectionMonitorTestConfigurationProtocol - The protocol to use in test evaluation.
type ConnectionMonitorTestConfigurationProtocol string

const (
	ConnectionMonitorTestConfigurationProtocolHTTP ConnectionMonitorTestConfigurationProtocol = "Http"
	ConnectionMonitorTestConfigurationProtocolIcmp ConnectionMonitorTestConfigurationProtocol = "Icmp"
	ConnectionMonitorTestConfigurationProtocolTCP  ConnectionMonitorTestConfigurationProtocol = "Tcp"
)

// PossibleConnectionMonitorTestConfigurationProtocolValues returns the possible values for the ConnectionMonitorTestConfigurationProtocol const type.
func PossibleConnectionMonitorTestConfigurationProtocolValues() []ConnectionMonitorTestConfigurationProtocol {
	return []ConnectionMonitorTestConfigurationProtocol{
		ConnectionMonitorTestConfigurationProtocolHTTP,
		ConnectionMonitorTestConfigurationProtocolIcmp,
		ConnectionMonitorTestConfigurationProtocolTCP,
	}
}

// ConnectionMonitorType - Type of connection monitor.
type ConnectionMonitorType string

const (
	ConnectionMonitorTypeMultiEndpoint           ConnectionMonitorType = "MultiEndpoint"
	ConnectionMonitorTypeSingleSourceDestination ConnectionMonitorType = "SingleSourceDestination"
)

// PossibleConnectionMonitorTypeValues returns the possible values for the ConnectionMonitorType const type.
func PossibleConnectionMonitorTypeValues() []ConnectionMonitorType {
	return []ConnectionMonitorType{
		ConnectionMonitorTypeMultiEndpoint,
		ConnectionMonitorTypeSingleSourceDestination,
	}
}

// ConnectionState - The connection state.
type ConnectionState string

const (
	ConnectionStateReachable   ConnectionState = "Reachable"
	ConnectionStateUnknown     ConnectionState = "Unknown"
	ConnectionStateUnreachable ConnectionState = "Unreachable"
)

// PossibleConnectionStateValues returns the possible values for the ConnectionState const type.
func PossibleConnectionStateValues() []ConnectionState {
	return []ConnectionState{
		ConnectionStateReachable,
		ConnectionStateUnknown,
		ConnectionStateUnreachable,
	}
}

// ConnectionStatus - The connection status.
type ConnectionStatus string

const (
	ConnectionStatusConnected    ConnectionStatus = "Connected"
	ConnectionStatusDegraded     ConnectionStatus = "Degraded"
	ConnectionStatusDisconnected ConnectionStatus = "Disconnected"
	ConnectionStatusUnknown      ConnectionStatus = "Unknown"
)

// PossibleConnectionStatusValues returns the possible values for the ConnectionStatus const type.
func PossibleConnectionStatusValues() []ConnectionStatus {
	return []ConnectionStatus{
		ConnectionStatusConnected,
		ConnectionStatusDegraded,
		ConnectionStatusDisconnected,
		ConnectionStatusUnknown,
	}
}

// ConnectivityTopology - Connectivity topology type.
type ConnectivityTopology string

const (
	ConnectivityTopologyHubAndSpoke ConnectivityTopology = "HubAndSpoke"
	ConnectivityTopologyMesh        ConnectivityTopology = "Mesh"
)

// PossibleConnectivityTopologyValues returns the possible values for the ConnectivityTopology const type.
func PossibleConnectivityTopologyValues() []ConnectivityTopology {
	return []ConnectivityTopology{
		ConnectivityTopologyHubAndSpoke,
		ConnectivityTopologyMesh,
	}
}

// CoverageLevel - Test coverage for the endpoint.
type CoverageLevel string

const (
	CoverageLevelAboveAverage CoverageLevel = "AboveAverage"
	CoverageLevelAverage      CoverageLevel = "Average"
	CoverageLevelBelowAverage CoverageLevel = "BelowAverage"
	CoverageLevelDefault      CoverageLevel = "Default"
	CoverageLevelFull         CoverageLevel = "Full"
	CoverageLevelLow          CoverageLevel = "Low"
)

// PossibleCoverageLevelValues returns the possible values for the CoverageLevel const type.
func PossibleCoverageLevelValues() []CoverageLevel {
	return []CoverageLevel{
		CoverageLevelAboveAverage,
		CoverageLevelAverage,
		CoverageLevelBelowAverage,
		CoverageLevelDefault,
		CoverageLevelFull,
		CoverageLevelLow,
	}
}

// CreatedByType - The type of identity that created the resource.
type CreatedByType string

const (
	CreatedByTypeApplication     CreatedByType = "Application"
	CreatedByTypeKey             CreatedByType = "Key"
	CreatedByTypeManagedIdentity CreatedByType = "ManagedIdentity"
	CreatedByTypeUser            CreatedByType = "User"
)

// PossibleCreatedByTypeValues returns the possible values for the CreatedByType const type.
func PossibleCreatedByTypeValues() []CreatedByType {
	return []CreatedByType{
		CreatedByTypeApplication,
		CreatedByTypeKey,
		CreatedByTypeManagedIdentity,
		CreatedByTypeUser,
	}
}

// CustomIPPrefixType - Type of custom IP prefix. Should be Singular, Parent, or Child.
type CustomIPPrefixType string

const (
	CustomIPPrefixTypeChild    CustomIPPrefixType = "Child"
	CustomIPPrefixTypeParent   CustomIPPrefixType = "Parent"
	CustomIPPrefixTypeSingular CustomIPPrefixType = "Singular"
)

// PossibleCustomIPPrefixTypeValues returns the possible values for the CustomIPPrefixType const type.
func PossibleCustomIPPrefixTypeValues() []CustomIPPrefixType {
	return []CustomIPPrefixType{
		CustomIPPrefixTypeChild,
		CustomIPPrefixTypeParent,
		CustomIPPrefixTypeSingular,
	}
}

// DdosSettingsProtectionMode - The DDoS protection mode of the public IP
type DdosSettingsProtectionMode string

const (
	DdosSettingsProtectionModeDisabled                DdosSettingsProtectionMode = "Disabled"
	DdosSettingsProtectionModeEnabled                 DdosSettingsProtectionMode = "Enabled"
	DdosSettingsProtectionModeVirtualNetworkInherited DdosSettingsProtectionMode = "VirtualNetworkInherited"
)

// PossibleDdosSettingsProtectionModeValues returns the possible values for the DdosSettingsProtectionMode const type.
func PossibleDdosSettingsProtectionModeValues() []DdosSettingsProtectionMode {
	return []DdosSettingsProtectionMode{
		DdosSettingsProtectionModeDisabled,
		DdosSettingsProtectionModeEnabled,
		DdosSettingsProtectionModeVirtualNetworkInherited,
	}
}

// DeleteExistingPeering - Flag if need to remove current existing peerings.
type DeleteExistingPeering string

const (
	DeleteExistingPeeringFalse DeleteExistingPeering = "False"
	DeleteExistingPeeringTrue  DeleteExistingPeering = "True"
)

// PossibleDeleteExistingPeeringValues returns the possible values for the DeleteExistingPeering const type.
func PossibleDeleteExistingPeeringValues() []DeleteExistingPeering {
	return []DeleteExistingPeering{
		DeleteExistingPeeringFalse,
		DeleteExistingPeeringTrue,
	}
}

// DeleteOptions - Specify what happens to the public IP address when the VM using it is deleted
type DeleteOptions string

const (
	DeleteOptionsDelete DeleteOptions = "Delete"
	DeleteOptionsDetach DeleteOptions = "Detach"
)

// PossibleDeleteOptionsValues returns the possible values for the DeleteOptions const type.
func PossibleDeleteOptionsValues() []DeleteOptions {
	return []DeleteOptions{
		DeleteOptionsDelete,
		DeleteOptionsDetach,
	}
}

// DeploymentStatus - Deployment Status.
type DeploymentStatus string

const (
	DeploymentStatusDeployed   DeploymentStatus = "Deployed"
	DeploymentStatusDeploying  DeploymentStatus = "Deploying"
	DeploymentStatusFailed     DeploymentStatus = "Failed"
	DeploymentStatusNotStarted DeploymentStatus = "NotStarted"
)

// PossibleDeploymentStatusValues returns the possible values for the DeploymentStatus const type.
func PossibleDeploymentStatusValues() []DeploymentStatus {
	return []DeploymentStatus{
		DeploymentStatusDeployed,
		DeploymentStatusDeploying,
		DeploymentStatusFailed,
		DeploymentStatusNotStarted,
	}
}

// DestinationPortBehavior - Destination port behavior.
type DestinationPortBehavior string

const (
	DestinationPortBehaviorListenIfAvailable DestinationPortBehavior = "ListenIfAvailable"
	DestinationPortBehaviorNone              DestinationPortBehavior = "None"
)

// PossibleDestinationPortBehaviorValues returns the possible values for the DestinationPortBehavior const type.
func PossibleDestinationPortBehaviorValues() []DestinationPortBehavior {
	return []DestinationPortBehavior{
		DestinationPortBehaviorListenIfAvailable,
		DestinationPortBehaviorNone,
	}
}

// DhGroup - The DH Groups used in IKE Phase 1 for initial SA.
type DhGroup string

const (
	DhGroupDHGroup1    DhGroup = "DHGroup1"
	DhGroupDHGroup14   DhGroup = "DHGroup14"
	DhGroupDHGroup2    DhGroup = "DHGroup2"
	DhGroupDHGroup2048 DhGroup = "DHGroup2048"
	DhGroupDHGroup24   DhGroup = "DHGroup24"
	DhGroupECP256      DhGroup = "ECP256"
	DhGroupECP384      DhGroup = "ECP384"
	DhGroupNone        DhGroup = "None"
)

// PossibleDhGroupValues returns the possible values for the DhGroup const type.
func PossibleDhGroupValues() []DhGroup {
	return []DhGroup{
		DhGroupDHGroup1,
		DhGroupDHGroup14,
		DhGroupDHGroup2,
		DhGroupDHGroup2048,
		DhGroupDHGroup24,
		DhGroupECP256,
		DhGroupECP384,
		DhGroupNone,
	}
}

// Direction - The direction of the traffic.
type Direction string

const (
	DirectionInbound  Direction = "Inbound"
	DirectionOutbound Direction = "Outbound"
)

// PossibleDirectionValues returns the possible values for the Direction const type.
func PossibleDirectionValues() []Direction {
	return []Direction{
		DirectionInbound,
		DirectionOutbound,
	}
}

// EffectiveAdminRuleKind - Whether the rule is custom or default.
type EffectiveAdminRuleKind string

const (
	EffectiveAdminRuleKindCustom  EffectiveAdminRuleKind = "Custom"
	EffectiveAdminRuleKindDefault EffectiveAdminRuleKind = "Default"
)

// PossibleEffectiveAdminRuleKindValues returns the possible values for the EffectiveAdminRuleKind const type.
func PossibleEffectiveAdminRuleKindValues() []EffectiveAdminRuleKind {
	return []EffectiveAdminRuleKind{
		EffectiveAdminRuleKindCustom,
		EffectiveAdminRuleKindDefault,
	}
}

// EffectiveRouteSource - Who created the route.
type EffectiveRouteSource string

const (
	EffectiveRouteSourceDefault               EffectiveRouteSource = "Default"
	EffectiveRouteSourceUnknown               EffectiveRouteSource = "Unknown"
	EffectiveRouteSourceUser                  EffectiveRouteSource = "User"
	EffectiveRouteSourceVirtualNetworkGateway EffectiveRouteSource = "VirtualNetworkGateway"
)

// PossibleEffectiveRouteSourceValues returns the possible values for the EffectiveRouteSource const type.
func PossibleEffectiveRouteSourceValues() []EffectiveRouteSource {
	return []EffectiveRouteSource{
		EffectiveRouteSourceDefault,
		EffectiveRouteSourceUnknown,
		EffectiveRouteSourceUser,
		EffectiveRouteSourceVirtualNetworkGateway,
	}
}

// EffectiveRouteState - The value of effective route.
type EffectiveRouteState string

const (
	EffectiveRouteStateActive  EffectiveRouteState = "Active"
	EffectiveRouteStateInvalid EffectiveRouteState = "Invalid"
)

// PossibleEffectiveRouteStateValues returns the possible values for the EffectiveRouteState const type.
func PossibleEffectiveRouteStateValues() []EffectiveRouteState {
	return []EffectiveRouteState{
		EffectiveRouteStateActive,
		EffectiveRouteStateInvalid,
	}
}

// EffectiveSecurityRuleProtocol - The network protocol this rule applies to.
type EffectiveSecurityRuleProtocol string

const (
	EffectiveSecurityRuleProtocolAll EffectiveSecurityRuleProtocol = "All"
	EffectiveSecurityRuleProtocolTCP EffectiveSecurityRuleProtocol = "Tcp"
	EffectiveSecurityRuleProtocolUDP EffectiveSecurityRuleProtocol = "Udp"
)

// PossibleEffectiveSecurityRuleProtocolValues returns the possible values for the EffectiveSecurityRuleProtocol const type.
func PossibleEffectiveSecurityRuleProtocolValues() []EffectiveSecurityRuleProtocol {
	return []EffectiveSecurityRuleProtocol{
		EffectiveSecurityRuleProtocolAll,
		EffectiveSecurityRuleProtocolTCP,
		EffectiveSecurityRuleProtocolUDP,
	}
}

// EndpointType - The endpoint type.
type EndpointType string

const (
	EndpointTypeAzureArcVM          EndpointType = "AzureArcVM"
	EndpointTypeAzureSubnet         EndpointType = "AzureSubnet"
	EndpointTypeAzureVM             EndpointType = "AzureVM"
	EndpointTypeAzureVMSS           EndpointType = "AzureVMSS"
	EndpointTypeAzureVNet           EndpointType = "AzureVNet"
	EndpointTypeExternalAddress     EndpointType = "ExternalAddress"
	EndpointTypeMMAWorkspaceMachine EndpointType = "MMAWorkspaceMachine"
	EndpointTypeMMAWorkspaceNetwork EndpointType = "MMAWorkspaceNetwork"
)

// PossibleEndpointTypeValues returns the possible values for the EndpointType const type.
func PossibleEndpointTypeValues() []EndpointType {
	return []EndpointType{
		EndpointTypeAzureArcVM,
		EndpointTypeAzureSubnet,
		EndpointTypeAzureVM,
		EndpointTypeAzureVMSS,
		EndpointTypeAzureVNet,
		EndpointTypeExternalAddress,
		EndpointTypeMMAWorkspaceMachine,
		EndpointTypeMMAWorkspaceNetwork,
	}
}

// EvaluationState - Connectivity analysis evaluation state.
type EvaluationState string

const (
	EvaluationStateCompleted  EvaluationState = "Completed"
	EvaluationStateInProgress EvaluationState = "InProgress"
	EvaluationStateNotStarted EvaluationState = "NotStarted"
)

// PossibleEvaluationStateValues returns the possible values for the EvaluationState const type.
func PossibleEvaluationStateValues() []EvaluationState {
	return []EvaluationState{
		EvaluationStateCompleted,
		EvaluationStateInProgress,
		EvaluationStateNotStarted,
	}
}

// ExpressRouteCircuitPeeringAdvertisedPublicPrefixState - The advertised public prefix state of the Peering resource.
type ExpressRouteCircuitPeeringAdvertisedPublicPrefixState string

const (
	ExpressRouteCircuitPeeringAdvertisedPublicPrefixStateConfigured       ExpressRouteCircuitPeeringAdvertisedPublicPrefixState = "Configured"
	ExpressRouteCircuitPeeringAdvertisedPublicPrefixStateConfiguring      ExpressRouteCircuitPeeringAdvertisedPublicPrefixState = "Configuring"
	ExpressRouteCircuitPeeringAdvertisedPublicPrefixStateNotConfigured    ExpressRouteCircuitPeeringAdvertisedPublicPrefixState = "NotConfigured"
	ExpressRouteCircuitPeeringAdvertisedPublicPrefixStateValidationNeeded ExpressRouteCircuitPeeringAdvertisedPublicPrefixState = "ValidationNeeded"
)

// PossibleExpressRouteCircuitPeeringAdvertisedPublicPrefixStateValues returns the possible values for the ExpressRouteCircuitPeeringAdvertisedPublicPrefixState const type.
func PossibleExpressRouteCircuitPeeringAdvertisedPublicPrefixStateValues() []ExpressRouteCircuitPeeringAdvertisedPublicPrefixState {
	return []ExpressRouteCircuitPeeringAdvertisedPublicPrefixState{
		ExpressRouteCircuitPeeringAdvertisedPublicPrefixStateConfigured,
		ExpressRouteCircuitPeeringAdvertisedPublicPrefixStateConfiguring,
		ExpressRouteCircuitPeeringAdvertisedPublicPrefixStateNotConfigured,
		ExpressRouteCircuitPeeringAdvertisedPublicPrefixStateValidationNeeded,
	}
}

// ExpressRouteCircuitPeeringState - The state of peering.
type ExpressRouteCircuitPeeringState string

const (
	ExpressRouteCircuitPeeringStateDisabled ExpressRouteCircuitPeeringState = "Disabled"
	ExpressRouteCircuitPeeringStateEnabled  ExpressRouteCircuitPeeringState = "Enabled"
)

// PossibleExpressRouteCircuitPeeringStateValues returns the possible values for the ExpressRouteCircuitPeeringState const type.
func PossibleExpressRouteCircuitPeeringStateValues() []ExpressRouteCircuitPeeringState {
	return []ExpressRouteCircuitPeeringState{
		ExpressRouteCircuitPeeringStateDisabled,
		ExpressRouteCircuitPeeringStateEnabled,
	}
}

// ExpressRouteCircuitSKUFamily - The family of the SKU.
type ExpressRouteCircuitSKUFamily string

const (
	ExpressRouteCircuitSKUFamilyMeteredData   ExpressRouteCircuitSKUFamily = "MeteredData"
	ExpressRouteCircuitSKUFamilyUnlimitedData ExpressRouteCircuitSKUFamily = "UnlimitedData"
)

// PossibleExpressRouteCircuitSKUFamilyValues returns the possible values for the ExpressRouteCircuitSKUFamily const type.
func PossibleExpressRouteCircuitSKUFamilyValues() []ExpressRouteCircuitSKUFamily {
	return []ExpressRouteCircuitSKUFamily{
		ExpressRouteCircuitSKUFamilyMeteredData,
		ExpressRouteCircuitSKUFamilyUnlimitedData,
	}
}

// ExpressRouteCircuitSKUTier - The tier of the SKU.
type ExpressRouteCircuitSKUTier string

const (
	ExpressRouteCircuitSKUTierBasic    ExpressRouteCircuitSKUTier = "Basic"
	ExpressRouteCircuitSKUTierLocal    ExpressRouteCircuitSKUTier = "Local"
	ExpressRouteCircuitSKUTierPremium  ExpressRouteCircuitSKUTier = "Premium"
	ExpressRouteCircuitSKUTierStandard ExpressRouteCircuitSKUTier = "Standard"
)

// PossibleExpressRouteCircuitSKUTierValues returns the possible values for the ExpressRouteCircuitSKUTier const type.
func PossibleExpressRouteCircuitSKUTierValues() []ExpressRouteCircuitSKUTier {
	return []ExpressRouteCircuitSKUTier{
		ExpressRouteCircuitSKUTierBasic,
		ExpressRouteCircuitSKUTierLocal,
		ExpressRouteCircuitSKUTierPremium,
		ExpressRouteCircuitSKUTierStandard,
	}
}

// ExpressRouteLinkAdminState - Administrative state of the physical port.
type ExpressRouteLinkAdminState string

const (
	ExpressRouteLinkAdminStateDisabled ExpressRouteLinkAdminState = "Disabled"
	ExpressRouteLinkAdminStateEnabled  ExpressRouteLinkAdminState = "Enabled"
)

// PossibleExpressRouteLinkAdminStateValues returns the possible values for the ExpressRouteLinkAdminState const type.
func PossibleExpressRouteLinkAdminStateValues() []ExpressRouteLinkAdminState {
	return []ExpressRouteLinkAdminState{
		ExpressRouteLinkAdminStateDisabled,
		ExpressRouteLinkAdminStateEnabled,
	}
}

// ExpressRouteLinkConnectorType - Physical fiber port type.
type ExpressRouteLinkConnectorType string

const (
	ExpressRouteLinkConnectorTypeLC ExpressRouteLinkConnectorType = "LC"
	ExpressRouteLinkConnectorTypeSC ExpressRouteLinkConnectorType = "SC"
)

// PossibleExpressRouteLinkConnectorTypeValues returns the possible values for the ExpressRouteLinkConnectorType const type.
func PossibleExpressRouteLinkConnectorTypeValues() []ExpressRouteLinkConnectorType {
	return []ExpressRouteLinkConnectorType{
		ExpressRouteLinkConnectorTypeLC,
		ExpressRouteLinkConnectorTypeSC,
	}
}

// ExpressRouteLinkMacSecCipher - Mac security cipher.
type ExpressRouteLinkMacSecCipher string

const (
	ExpressRouteLinkMacSecCipherGCMAES128    ExpressRouteLinkMacSecCipher = "GcmAes128"
	ExpressRouteLinkMacSecCipherGCMAES256    ExpressRouteLinkMacSecCipher = "GcmAes256"
	ExpressRouteLinkMacSecCipherGCMAesXpn128 ExpressRouteLinkMacSecCipher = "GcmAesXpn128"
	ExpressRouteLinkMacSecCipherGCMAesXpn256 ExpressRouteLinkMacSecCipher = "GcmAesXpn256"
)

// PossibleExpressRouteLinkMacSecCipherValues returns the possible values for the ExpressRouteLinkMacSecCipher const type.
func PossibleExpressRouteLinkMacSecCipherValues() []ExpressRouteLinkMacSecCipher {
	return []ExpressRouteLinkMacSecCipher{
		ExpressRouteLinkMacSecCipherGCMAES128,
		ExpressRouteLinkMacSecCipherGCMAES256,
		ExpressRouteLinkMacSecCipherGCMAesXpn128,
		ExpressRouteLinkMacSecCipherGCMAesXpn256,
	}
}

// ExpressRouteLinkMacSecSciState - Sci mode enabled/disabled.
type ExpressRouteLinkMacSecSciState string

const (
	ExpressRouteLinkMacSecSciStateDisabled ExpressRouteLinkMacSecSciState = "Disabled"
	ExpressRouteLinkMacSecSciStateEnabled  ExpressRouteLinkMacSecSciState = "Enabled"
)

// PossibleExpressRouteLinkMacSecSciStateValues returns the possible values for the ExpressRouteLinkMacSecSciState const type.
func PossibleExpressRouteLinkMacSecSciStateValues() []ExpressRouteLinkMacSecSciState {
	return []ExpressRouteLinkMacSecSciState{
		ExpressRouteLinkMacSecSciStateDisabled,
		ExpressRouteLinkMacSecSciStateEnabled,
	}
}

// ExpressRoutePeeringState - The state of peering.
type ExpressRoutePeeringState string

const (
	ExpressRoutePeeringStateDisabled ExpressRoutePeeringState = "Disabled"
	ExpressRoutePeeringStateEnabled  ExpressRoutePeeringState = "Enabled"
)

// PossibleExpressRoutePeeringStateValues returns the possible values for the ExpressRoutePeeringState const type.
func PossibleExpressRoutePeeringStateValues() []ExpressRoutePeeringState {
	return []ExpressRoutePeeringState{
		ExpressRoutePeeringStateDisabled,
		ExpressRoutePeeringStateEnabled,
	}
}

// ExpressRoutePeeringType - The peering type.
type ExpressRoutePeeringType string

const (
	ExpressRoutePeeringTypeAzurePrivatePeering ExpressRoutePeeringType = "AzurePrivatePeering"
	ExpressRoutePeeringTypeAzurePublicPeering  ExpressRoutePeeringType = "AzurePublicPeering"
	ExpressRoutePeeringTypeMicrosoftPeering    ExpressRoutePeeringType = "MicrosoftPeering"
)

// PossibleExpressRoutePeeringTypeValues returns the possible values for the ExpressRoutePeeringType const type.
func PossibleExpressRoutePeeringTypeValues() []ExpressRoutePeeringType {
	return []ExpressRoutePeeringType{
		ExpressRoutePeeringTypeAzurePrivatePeering,
		ExpressRoutePeeringTypeAzurePublicPeering,
		ExpressRoutePeeringTypeMicrosoftPeering,
	}
}

// ExpressRoutePortAuthorizationUseStatus - The authorization use status.
type ExpressRoutePortAuthorizationUseStatus string

const (
	ExpressRoutePortAuthorizationUseStatusAvailable ExpressRoutePortAuthorizationUseStatus = "Available"
	ExpressRoutePortAuthorizationUseStatusInUse     ExpressRoutePortAuthorizationUseStatus = "InUse"
)

// PossibleExpressRoutePortAuthorizationUseStatusValues returns the possible values for the ExpressRoutePortAuthorizationUseStatus const type.
func PossibleExpressRoutePortAuthorizationUseStatusValues() []ExpressRoutePortAuthorizationUseStatus {
	return []ExpressRoutePortAuthorizationUseStatus{
		ExpressRoutePortAuthorizationUseStatusAvailable,
		ExpressRoutePortAuthorizationUseStatusInUse,
	}
}

// ExpressRoutePortsBillingType - The billing type of the ExpressRoutePort resource.
type ExpressRoutePortsBillingType string

const (
	ExpressRoutePortsBillingTypeMeteredData   ExpressRoutePortsBillingType = "MeteredData"
	ExpressRoutePortsBillingTypeUnlimitedData ExpressRoutePortsBillingType = "UnlimitedData"
)

// PossibleExpressRoutePortsBillingTypeValues returns the possible values for the ExpressRoutePortsBillingType const type.
func PossibleExpressRoutePortsBillingTypeValues() []ExpressRoutePortsBillingType {
	return []ExpressRoutePortsBillingType{
		ExpressRoutePortsBillingTypeMeteredData,
		ExpressRoutePortsBillingTypeUnlimitedData,
	}
}

// ExpressRoutePortsEncapsulation - Encapsulation method on physical ports.
type ExpressRoutePortsEncapsulation string

const (
	ExpressRoutePortsEncapsulationDot1Q ExpressRoutePortsEncapsulation = "Dot1Q"
	ExpressRoutePortsEncapsulationQinQ  ExpressRoutePortsEncapsulation = "QinQ"
)

// PossibleExpressRoutePortsEncapsulationValues returns the possible values for the ExpressRoutePortsEncapsulation const type.
func PossibleExpressRoutePortsEncapsulationValues() []ExpressRoutePortsEncapsulation {
	return []ExpressRoutePortsEncapsulation{
		ExpressRoutePortsEncapsulationDot1Q,
		ExpressRoutePortsEncapsulationQinQ,
	}
}

// ExtendedLocationTypes - The supported ExtendedLocation types. Currently only EdgeZone is supported in Microsoft.Network
// resources.
type ExtendedLocationTypes string

const (
	ExtendedLocationTypesEdgeZone ExtendedLocationTypes = "EdgeZone"
)

// PossibleExtendedLocationTypesValues returns the possible values for the ExtendedLocationTypes const type.
func PossibleExtendedLocationTypesValues() []ExtendedLocationTypes {
	return []ExtendedLocationTypes{
		ExtendedLocationTypesEdgeZone,
	}
}

// FirewallPolicyFilterRuleCollectionActionType - The action type of a rule.
type FirewallPolicyFilterRuleCollectionActionType string

const (
	FirewallPolicyFilterRuleCollectionActionTypeAllow FirewallPolicyFilterRuleCollectionActionType = "Allow"
	FirewallPolicyFilterRuleCollectionActionTypeDeny  FirewallPolicyFilterRuleCollectionActionType = "Deny"
)

// PossibleFirewallPolicyFilterRuleCollectionActionTypeValues returns the possible values for the FirewallPolicyFilterRuleCollectionActionType const type.
func PossibleFirewallPolicyFilterRuleCollectionActionTypeValues() []FirewallPolicyFilterRuleCollectionActionType {
	return []FirewallPolicyFilterRuleCollectionActionType{
		FirewallPolicyFilterRuleCollectionActionTypeAllow,
		FirewallPolicyFilterRuleCollectionActionTypeDeny,
	}
}

// FirewallPolicyIDPSQuerySortOrder - Describes if results should be in ascending/descending order
type FirewallPolicyIDPSQuerySortOrder string

const (
	FirewallPolicyIDPSQuerySortOrderAscending  FirewallPolicyIDPSQuerySortOrder = "Ascending"
	FirewallPolicyIDPSQuerySortOrderDescending FirewallPolicyIDPSQuerySortOrder = "Descending"
)

// PossibleFirewallPolicyIDPSQuerySortOrderValues returns the possible values for the FirewallPolicyIDPSQuerySortOrder const type.
func PossibleFirewallPolicyIDPSQuerySortOrderValues() []FirewallPolicyIDPSQuerySortOrder {
	return []FirewallPolicyIDPSQuerySortOrder{
		FirewallPolicyIDPSQuerySortOrderAscending,
		FirewallPolicyIDPSQuerySortOrderDescending,
	}
}

// FirewallPolicyIDPSSignatureDirection - Describes in which direction signature is being enforced: 0 - Inbound, 1 - OutBound,
// 2 - Bidirectional
type FirewallPolicyIDPSSignatureDirection int32

const (
	FirewallPolicyIDPSSignatureDirectionOne  FirewallPolicyIDPSSignatureDirection = 1
	FirewallPolicyIDPSSignatureDirectionTwo  FirewallPolicyIDPSSignatureDirection = 2
	FirewallPolicyIDPSSignatureDirectionZero FirewallPolicyIDPSSignatureDirection = 0
)

// PossibleFirewallPolicyIDPSSignatureDirectionValues returns the possible values for the FirewallPolicyIDPSSignatureDirection const type.
func PossibleFirewallPolicyIDPSSignatureDirectionValues() []FirewallPolicyIDPSSignatureDirection {
	return []FirewallPolicyIDPSSignatureDirection{
		FirewallPolicyIDPSSignatureDirectionOne,
		FirewallPolicyIDPSSignatureDirectionTwo,
		FirewallPolicyIDPSSignatureDirectionZero,
	}
}

// FirewallPolicyIDPSSignatureMode - The current mode enforced, 0 - Disabled, 1 - Alert, 2 -Deny
type FirewallPolicyIDPSSignatureMode int32

const (
	FirewallPolicyIDPSSignatureModeOne  FirewallPolicyIDPSSignatureMode = 1
	FirewallPolicyIDPSSignatureModeTwo  FirewallPolicyIDPSSignatureMode = 2
	FirewallPolicyIDPSSignatureModeZero FirewallPolicyIDPSSignatureMode = 0
)

// PossibleFirewallPolicyIDPSSignatureModeValues returns the possible values for the FirewallPolicyIDPSSignatureMode const type.
func PossibleFirewallPolicyIDPSSignatureModeValues() []FirewallPolicyIDPSSignatureMode {
	return []FirewallPolicyIDPSSignatureMode{
		FirewallPolicyIDPSSignatureModeOne,
		FirewallPolicyIDPSSignatureModeTwo,
		FirewallPolicyIDPSSignatureModeZero,
	}
}

// FirewallPolicyIDPSSignatureSeverity - Describes the severity of signature: 1 - Low, 2 - Medium, 3 - High
type FirewallPolicyIDPSSignatureSeverity int32

const (
	FirewallPolicyIDPSSignatureSeverityOne   FirewallPolicyIDPSSignatureSeverity = 1
	FirewallPolicyIDPSSignatureSeverityThree FirewallPolicyIDPSSignatureSeverity = 3
	FirewallPolicyIDPSSignatureSeverityTwo   FirewallPolicyIDPSSignatureSeverity = 2
)

// PossibleFirewallPolicyIDPSSignatureSeverityValues returns the possible values for the FirewallPolicyIDPSSignatureSeverity const type.
func PossibleFirewallPolicyIDPSSignatureSeverityValues() []FirewallPolicyIDPSSignatureSeverity {
	return []FirewallPolicyIDPSSignatureSeverity{
		FirewallPolicyIDPSSignatureSeverityOne,
		FirewallPolicyIDPSSignatureSeverityThree,
		FirewallPolicyIDPSSignatureSeverityTwo,
	}
}

// FirewallPolicyIntrusionDetectionProtocol - Possible intrusion detection bypass traffic protocols.
type FirewallPolicyIntrusionDetectionProtocol string

const (
	FirewallPolicyIntrusionDetectionProtocolANY  FirewallPolicyIntrusionDetectionProtocol = "ANY"
	FirewallPolicyIntrusionDetectionProtocolICMP FirewallPolicyIntrusionDetectionProtocol = "ICMP"
	FirewallPolicyIntrusionDetectionProtocolTCP  FirewallPolicyIntrusionDetectionProtocol = "TCP"
	FirewallPolicyIntrusionDetectionProtocolUDP  FirewallPolicyIntrusionDetectionProtocol = "UDP"
)

// PossibleFirewallPolicyIntrusionDetectionProtocolValues returns the possible values for the FirewallPolicyIntrusionDetectionProtocol const type.
func PossibleFirewallPolicyIntrusionDetectionProtocolValues() []FirewallPolicyIntrusionDetectionProtocol {
	return []FirewallPolicyIntrusionDetectionProtocol{
		FirewallPolicyIntrusionDetectionProtocolANY,
		FirewallPolicyIntrusionDetectionProtocolICMP,
		FirewallPolicyIntrusionDetectionProtocolTCP,
		FirewallPolicyIntrusionDetectionProtocolUDP,
	}
}

// FirewallPolicyIntrusionDetectionStateType - Possible state values.
type FirewallPolicyIntrusionDetectionStateType string

const (
	FirewallPolicyIntrusionDetectionStateTypeAlert FirewallPolicyIntrusionDetectionStateType = "Alert"
	FirewallPolicyIntrusionDetectionStateTypeDeny  FirewallPolicyIntrusionDetectionStateType = "Deny"
	FirewallPolicyIntrusionDetectionStateTypeOff   FirewallPolicyIntrusionDetectionStateType = "Off"
)

// PossibleFirewallPolicyIntrusionDetectionStateTypeValues returns the possible values for the FirewallPolicyIntrusionDetectionStateType const type.
func PossibleFirewallPolicyIntrusionDetectionStateTypeValues() []FirewallPolicyIntrusionDetectionStateType {
	return []FirewallPolicyIntrusionDetectionStateType{
		FirewallPolicyIntrusionDetectionStateTypeAlert,
		FirewallPolicyIntrusionDetectionStateTypeDeny,
		FirewallPolicyIntrusionDetectionStateTypeOff,
	}
}

// FirewallPolicyNatRuleCollectionActionType - The action type of a rule.
type FirewallPolicyNatRuleCollectionActionType string

const (
	FirewallPolicyNatRuleCollectionActionTypeDNAT FirewallPolicyNatRuleCollectionActionType = "DNAT"
)

// PossibleFirewallPolicyNatRuleCollectionActionTypeValues returns the possible values for the FirewallPolicyNatRuleCollectionActionType const type.
func PossibleFirewallPolicyNatRuleCollectionActionTypeValues() []FirewallPolicyNatRuleCollectionActionType {
	return []FirewallPolicyNatRuleCollectionActionType{
		FirewallPolicyNatRuleCollectionActionTypeDNAT,
	}
}

// FirewallPolicyRuleApplicationProtocolType - The application protocol type of a Rule.
type FirewallPolicyRuleApplicationProtocolType string

const (
	FirewallPolicyRuleApplicationProtocolTypeHTTP  FirewallPolicyRuleApplicationProtocolType = "Http"
	FirewallPolicyRuleApplicationProtocolTypeHTTPS FirewallPolicyRuleApplicationProtocolType = "Https"
)

// PossibleFirewallPolicyRuleApplicationProtocolTypeValues returns the possible values for the FirewallPolicyRuleApplicationProtocolType const type.
func PossibleFirewallPolicyRuleApplicationProtocolTypeValues() []FirewallPolicyRuleApplicationProtocolType {
	return []FirewallPolicyRuleApplicationProtocolType{
		FirewallPolicyRuleApplicationProtocolTypeHTTP,
		FirewallPolicyRuleApplicationProtocolTypeHTTPS,
	}
}

// FirewallPolicyRuleCollectionType - The type of the rule collection.
type FirewallPolicyRuleCollectionType string

const (
	FirewallPolicyRuleCollectionTypeFirewallPolicyFilterRuleCollection FirewallPolicyRuleCollectionType = "FirewallPolicyFilterRuleCollection"
	FirewallPolicyRuleCollectionTypeFirewallPolicyNatRuleCollection    FirewallPolicyRuleCollectionType = "FirewallPolicyNatRuleCollection"
)

// PossibleFirewallPolicyRuleCollectionTypeValues returns the possible values for the FirewallPolicyRuleCollectionType const type.
func PossibleFirewallPolicyRuleCollectionTypeValues() []FirewallPolicyRuleCollectionType {
	return []FirewallPolicyRuleCollectionType{
		FirewallPolicyRuleCollectionTypeFirewallPolicyFilterRuleCollection,
		FirewallPolicyRuleCollectionTypeFirewallPolicyNatRuleCollection,
	}
}

// FirewallPolicyRuleNetworkProtocol - The Network protocol of a Rule.
type FirewallPolicyRuleNetworkProtocol string

const (
	FirewallPolicyRuleNetworkProtocolAny  FirewallPolicyRuleNetworkProtocol = "Any"
	FirewallPolicyRuleNetworkProtocolICMP FirewallPolicyRuleNetworkProtocol = "ICMP"
	FirewallPolicyRuleNetworkProtocolTCP  FirewallPolicyRuleNetworkProtocol = "TCP"
	FirewallPolicyRuleNetworkProtocolUDP  FirewallPolicyRuleNetworkProtocol = "UDP"
)

// PossibleFirewallPolicyRuleNetworkProtocolValues returns the possible values for the FirewallPolicyRuleNetworkProtocol const type.
func PossibleFirewallPolicyRuleNetworkProtocolValues() []FirewallPolicyRuleNetworkProtocol {
	return []FirewallPolicyRuleNetworkProtocol{
		FirewallPolicyRuleNetworkProtocolAny,
		FirewallPolicyRuleNetworkProtocolICMP,
		FirewallPolicyRuleNetworkProtocolTCP,
		FirewallPolicyRuleNetworkProtocolUDP,
	}
}

// FirewallPolicyRuleType - Rule Type.
type FirewallPolicyRuleType string

const (
	FirewallPolicyRuleTypeApplicationRule FirewallPolicyRuleType = "ApplicationRule"
	FirewallPolicyRuleTypeNatRule         FirewallPolicyRuleType = "NatRule"
	FirewallPolicyRuleTypeNetworkRule     FirewallPolicyRuleType = "NetworkRule"
)

// PossibleFirewallPolicyRuleTypeValues returns the possible values for the FirewallPolicyRuleType const type.
func PossibleFirewallPolicyRuleTypeValues() []FirewallPolicyRuleType {
	return []FirewallPolicyRuleType{
		FirewallPolicyRuleTypeApplicationRule,
		FirewallPolicyRuleTypeNatRule,
		FirewallPolicyRuleTypeNetworkRule,
	}
}

// FirewallPolicySKUTier - Tier of Firewall Policy.
type FirewallPolicySKUTier string

const (
	FirewallPolicySKUTierBasic    FirewallPolicySKUTier = "Basic"
	FirewallPolicySKUTierPremium  FirewallPolicySKUTier = "Premium"
	FirewallPolicySKUTierStandard FirewallPolicySKUTier = "Standard"
)

// PossibleFirewallPolicySKUTierValues returns the possible values for the FirewallPolicySKUTier const type.
func PossibleFirewallPolicySKUTierValues() []FirewallPolicySKUTier {
	return []FirewallPolicySKUTier{
		FirewallPolicySKUTierBasic,
		FirewallPolicySKUTierPremium,
		FirewallPolicySKUTierStandard,
	}
}

// FlowLogFormatType - The file type of flow log.
type FlowLogFormatType string

const (
	FlowLogFormatTypeJSON FlowLogFormatType = "JSON"
)

// PossibleFlowLogFormatTypeValues returns the possible values for the FlowLogFormatType const type.
func PossibleFlowLogFormatTypeValues() []FlowLogFormatType {
	return []FlowLogFormatType{
		FlowLogFormatTypeJSON,
	}
}

// GatewayLoadBalancerTunnelInterfaceType - Traffic type of gateway load balancer tunnel interface.
type GatewayLoadBalancerTunnelInterfaceType string

const (
	GatewayLoadBalancerTunnelInterfaceTypeExternal GatewayLoadBalancerTunnelInterfaceType = "External"
	GatewayLoadBalancerTunnelInterfaceTypeInternal GatewayLoadBalancerTunnelInterfaceType = "Internal"
	GatewayLoadBalancerTunnelInterfaceTypeNone     GatewayLoadBalancerTunnelInterfaceType = "None"
)

// PossibleGatewayLoadBalancerTunnelInterfaceTypeValues returns the possible values for the GatewayLoadBalancerTunnelInterfaceType const type.
func PossibleGatewayLoadBalancerTunnelInterfaceTypeValues() []GatewayLoadBalancerTunnelInterfaceType {
	return []GatewayLoadBalancerTunnelInterfaceType{
		GatewayLoadBalancerTunnelInterfaceTypeExternal,
		GatewayLoadBalancerTunnelInterfaceTypeInternal,
		GatewayLoadBalancerTunnelInterfaceTypeNone,
	}
}

// GatewayLoadBalancerTunnelProtocol - Protocol of gateway load balancer tunnel interface.
type GatewayLoadBalancerTunnelProtocol string

const (
	GatewayLoadBalancerTunnelProtocolNative GatewayLoadBalancerTunnelProtocol = "Native"
	GatewayLoadBalancerTunnelProtocolNone   GatewayLoadBalancerTunnelProtocol = "None"
	GatewayLoadBalancerTunnelProtocolVXLAN  GatewayLoadBalancerTunnelProtocol = "VXLAN"
)

// PossibleGatewayLoadBalancerTunnelProtocolValues returns the possible values for the GatewayLoadBalancerTunnelProtocol const type.
func PossibleGatewayLoadBalancerTunnelProtocolValues() []GatewayLoadBalancerTunnelProtocol {
	return []GatewayLoadBalancerTunnelProtocol{
		GatewayLoadBalancerTunnelProtocolNative,
		GatewayLoadBalancerTunnelProtocolNone,
		GatewayLoadBalancerTunnelProtocolVXLAN,
	}
}

// Geo - The Geo for CIDR advertising. Should be an Geo code.
type Geo string

const (
	GeoAFRI    Geo = "AFRI"
	GeoAPAC    Geo = "APAC"
	GeoAQ      Geo = "AQ"
	GeoEURO    Geo = "EURO"
	GeoGLOBAL  Geo = "GLOBAL"
	GeoLATAM   Geo = "LATAM"
	GeoME      Geo = "ME"
	GeoNAM     Geo = "NAM"
	GeoOCEANIA Geo = "OCEANIA"
)

// PossibleGeoValues returns the possible values for the Geo const type.
func PossibleGeoValues() []Geo {
	return []Geo{
		GeoAFRI,
		GeoAPAC,
		GeoAQ,
		GeoEURO,
		GeoGLOBAL,
		GeoLATAM,
		GeoME,
		GeoNAM,
		GeoOCEANIA,
	}
}

// GroupConnectivity - Group connectivity type.
type GroupConnectivity string

const (
	GroupConnectivityDirectlyConnected GroupConnectivity = "DirectlyConnected"
	GroupConnectivityNone              GroupConnectivity = "None"
)

// PossibleGroupConnectivityValues returns the possible values for the GroupConnectivity const type.
func PossibleGroupConnectivityValues() []GroupConnectivity {
	return []GroupConnectivity{
		GroupConnectivityDirectlyConnected,
		GroupConnectivityNone,
	}
}

// HTTPConfigurationMethod - The HTTP method to use.
type HTTPConfigurationMethod string

const (
	HTTPConfigurationMethodGet  HTTPConfigurationMethod = "Get"
	HTTPConfigurationMethodPost HTTPConfigurationMethod = "Post"
)

// PossibleHTTPConfigurationMethodValues returns the possible values for the HTTPConfigurationMethod const type.
func PossibleHTTPConfigurationMethodValues() []HTTPConfigurationMethod {
	return []HTTPConfigurationMethod{
		HTTPConfigurationMethodGet,
		HTTPConfigurationMethodPost,
	}
}

// HTTPMethod - HTTP method.
type HTTPMethod string

const (
	HTTPMethodGet HTTPMethod = "Get"
)

// PossibleHTTPMethodValues returns the possible values for the HTTPMethod const type.
func PossibleHTTPMethodValues() []HTTPMethod {
	return []HTTPMethod{
		HTTPMethodGet,
	}
}

// HubBgpConnectionStatus - The current state of the VirtualHub to Peer.
type HubBgpConnectionStatus string

const (
	HubBgpConnectionStatusConnected    HubBgpConnectionStatus = "Connected"
	HubBgpConnectionStatusConnecting   HubBgpConnectionStatus = "Connecting"
	HubBgpConnectionStatusNotConnected HubBgpConnectionStatus = "NotConnected"
	HubBgpConnectionStatusUnknown      HubBgpConnectionStatus = "Unknown"
)

// PossibleHubBgpConnectionStatusValues returns the possible values for the HubBgpConnectionStatus const type.
func PossibleHubBgpConnectionStatusValues() []HubBgpConnectionStatus {
	return []HubBgpConnectionStatus{
		HubBgpConnectionStatusConnected,
		HubBgpConnectionStatusConnecting,
		HubBgpConnectionStatusNotConnected,
		HubBgpConnectionStatusUnknown,
	}
}

// HubRoutingPreference - The hub routing preference gateway types
type HubRoutingPreference string

const (
	HubRoutingPreferenceASPath       HubRoutingPreference = "ASPath"
	HubRoutingPreferenceExpressRoute HubRoutingPreference = "ExpressRoute"
	HubRoutingPreferenceVPNGateway   HubRoutingPreference = "VpnGateway"
)

// PossibleHubRoutingPreferenceValues returns the possible values for the HubRoutingPreference const type.
func PossibleHubRoutingPreferenceValues() []HubRoutingPreference {
	return []HubRoutingPreference{
		HubRoutingPreferenceASPath,
		HubRoutingPreferenceExpressRoute,
		HubRoutingPreferenceVPNGateway,
	}
}

// HubVirtualNetworkConnectionStatus - The current state of the VirtualHub to vnet connection.
type HubVirtualNetworkConnectionStatus string

const (
	HubVirtualNetworkConnectionStatusConnected    HubVirtualNetworkConnectionStatus = "Connected"
	HubVirtualNetworkConnectionStatusConnecting   HubVirtualNetworkConnectionStatus = "Connecting"
	HubVirtualNetworkConnectionStatusNotConnected HubVirtualNetworkConnectionStatus = "NotConnected"
	HubVirtualNetworkConnectionStatusUnknown      HubVirtualNetworkConnectionStatus = "Unknown"
)

// PossibleHubVirtualNetworkConnectionStatusValues returns the possible values for the HubVirtualNetworkConnectionStatus const type.
func PossibleHubVirtualNetworkConnectionStatusValues() []HubVirtualNetworkConnectionStatus {
	return []HubVirtualNetworkConnectionStatus{
		HubVirtualNetworkConnectionStatusConnected,
		HubVirtualNetworkConnectionStatusConnecting,
		HubVirtualNetworkConnectionStatusNotConnected,
		HubVirtualNetworkConnectionStatusUnknown,
	}
}

// IPAllocationMethod - IP address allocation method.
type IPAllocationMethod string

const (
	IPAllocationMethodDynamic IPAllocationMethod = "Dynamic"
	IPAllocationMethodStatic  IPAllocationMethod = "Static"
)

// PossibleIPAllocationMethodValues returns the possible values for the IPAllocationMethod const type.
func PossibleIPAllocationMethodValues() []IPAllocationMethod {
	return []IPAllocationMethod{
		IPAllocationMethodDynamic,
		IPAllocationMethodStatic,
	}
}

// IPAllocationType - IpAllocation type.
type IPAllocationType string

const (
	IPAllocationTypeHypernet  IPAllocationType = "Hypernet"
	IPAllocationTypeUndefined IPAllocationType = "Undefined"
)

// PossibleIPAllocationTypeValues returns the possible values for the IPAllocationType const type.
func PossibleIPAllocationTypeValues() []IPAllocationType {
	return []IPAllocationType{
		IPAllocationTypeHypernet,
		IPAllocationTypeUndefined,
	}
}

// IPFlowProtocol - Protocol to be verified on.
type IPFlowProtocol string

const (
	IPFlowProtocolTCP IPFlowProtocol = "TCP"
	IPFlowProtocolUDP IPFlowProtocol = "UDP"
)

// PossibleIPFlowProtocolValues returns the possible values for the IPFlowProtocol const type.
func PossibleIPFlowProtocolValues() []IPFlowProtocol {
	return []IPFlowProtocol{
		IPFlowProtocolTCP,
		IPFlowProtocolUDP,
	}
}

// IPSecEncryption - The IPSec encryption algorithm (IKE phase 1).
type IPSecEncryption string

const (
	IPSecEncryptionAES128    IPSecEncryption = "AES128"
	IPSecEncryptionAES192    IPSecEncryption = "AES192"
	IPSecEncryptionAES256    IPSecEncryption = "AES256"
	IPSecEncryptionDES       IPSecEncryption = "DES"
	IPSecEncryptionDES3      IPSecEncryption = "DES3"
	IPSecEncryptionGCMAES128 IPSecEncryption = "GCMAES128"
	IPSecEncryptionGCMAES192 IPSecEncryption = "GCMAES192"
	IPSecEncryptionGCMAES256 IPSecEncryption = "GCMAES256"
	IPSecEncryptionNone      IPSecEncryption = "None"
)

// PossibleIPSecEncryptionValues returns the possible values for the IPSecEncryption const type.
func PossibleIPSecEncryptionValues() []IPSecEncryption {
	return []IPSecEncryption{
		IPSecEncryptionAES128,
		IPSecEncryptionAES192,
		IPSecEncryptionAES256,
		IPSecEncryptionDES,
		IPSecEncryptionDES3,
		IPSecEncryptionGCMAES128,
		IPSecEncryptionGCMAES192,
		IPSecEncryptionGCMAES256,
		IPSecEncryptionNone,
	}
}

// IPSecIntegrity - The IPSec integrity algorithm (IKE phase 1).
type IPSecIntegrity string

const (
	IPSecIntegrityGCMAES128 IPSecIntegrity = "GCMAES128"
	IPSecIntegrityGCMAES192 IPSecIntegrity = "GCMAES192"
	IPSecIntegrityGCMAES256 IPSecIntegrity = "GCMAES256"
	IPSecIntegrityMD5       IPSecIntegrity = "MD5"
	IPSecIntegritySHA1      IPSecIntegrity = "SHA1"
	IPSecIntegritySHA256    IPSecIntegrity = "SHA256"
)

// PossibleIPSecIntegrityValues returns the possible values for the IPSecIntegrity const type.
func PossibleIPSecIntegrityValues() []IPSecIntegrity {
	return []IPSecIntegrity{
		IPSecIntegrityGCMAES128,
		IPSecIntegrityGCMAES192,
		IPSecIntegrityGCMAES256,
		IPSecIntegrityMD5,
		IPSecIntegritySHA1,
		IPSecIntegritySHA256,
	}
}

// IPVersion - IP address version.
type IPVersion string

const (
	IPVersionIPv4 IPVersion = "IPv4"
	IPVersionIPv6 IPVersion = "IPv6"
)

// PossibleIPVersionValues returns the possible values for the IPVersion const type.
func PossibleIPVersionValues() []IPVersion {
	return []IPVersion{
		IPVersionIPv4,
		IPVersionIPv6,
	}
}

// IkeEncryption - The IKE encryption algorithm (IKE phase 2).
type IkeEncryption string

const (
	IkeEncryptionAES128    IkeEncryption = "AES128"
	IkeEncryptionAES192    IkeEncryption = "AES192"
	IkeEncryptionAES256    IkeEncryption = "AES256"
	IkeEncryptionDES       IkeEncryption = "DES"
	IkeEncryptionDES3      IkeEncryption = "DES3"
	IkeEncryptionGCMAES128 IkeEncryption = "GCMAES128"
	IkeEncryptionGCMAES256 IkeEncryption = "GCMAES256"
)

// PossibleIkeEncryptionValues returns the possible values for the IkeEncryption const type.
func PossibleIkeEncryptionValues() []IkeEncryption {
	return []IkeEncryption{
		IkeEncryptionAES128,
		IkeEncryptionAES192,
		IkeEncryptionAES256,
		IkeEncryptionDES,
		IkeEncryptionDES3,
		IkeEncryptionGCMAES128,
		IkeEncryptionGCMAES256,
	}
}

// IkeIntegrity - The IKE integrity algorithm (IKE phase 2).
type IkeIntegrity string

const (
	IkeIntegrityGCMAES128 IkeIntegrity = "GCMAES128"
	IkeIntegrityGCMAES256 IkeIntegrity = "GCMAES256"
	IkeIntegrityMD5       IkeIntegrity = "MD5"
	IkeIntegritySHA1      IkeIntegrity = "SHA1"
	IkeIntegritySHA256    IkeIntegrity = "SHA256"
	IkeIntegritySHA384    IkeIntegrity = "SHA384"
)

// PossibleIkeIntegrityValues returns the possible values for the IkeIntegrity const type.
func PossibleIkeIntegrityValues() []IkeIntegrity {
	return []IkeIntegrity{
		IkeIntegrityGCMAES128,
		IkeIntegrityGCMAES256,
		IkeIntegrityMD5,
		IkeIntegritySHA1,
		IkeIntegritySHA256,
		IkeIntegritySHA384,
	}
}

// InboundSecurityRulesProtocol - Protocol. This should be either TCP or UDP.
type InboundSecurityRulesProtocol string

const (
	InboundSecurityRulesProtocolTCP InboundSecurityRulesProtocol = "TCP"
	InboundSecurityRulesProtocolUDP InboundSecurityRulesProtocol = "UDP"
)

// PossibleInboundSecurityRulesProtocolValues returns the possible values for the InboundSecurityRulesProtocol const type.
func PossibleInboundSecurityRulesProtocolValues() []InboundSecurityRulesProtocol {
	return []InboundSecurityRulesProtocol{
		InboundSecurityRulesProtocolTCP,
		InboundSecurityRulesProtocolUDP,
	}
}

// IsGlobal - Flag if global mesh is supported.
type IsGlobal string

const (
	IsGlobalFalse IsGlobal = "False"
	IsGlobalTrue  IsGlobal = "True"
)

// PossibleIsGlobalValues returns the possible values for the IsGlobal const type.
func PossibleIsGlobalValues() []IsGlobal {
	return []IsGlobal{
		IsGlobalFalse,
		IsGlobalTrue,
	}
}

// IsWorkloadProtected - Value indicating whether the IP address is DDoS workload protected or not.
type IsWorkloadProtected string

const (
	IsWorkloadProtectedFalse IsWorkloadProtected = "False"
	IsWorkloadProtectedTrue  IsWorkloadProtected = "True"
)

// PossibleIsWorkloadProtectedValues returns the possible values for the IsWorkloadProtected const type.
func PossibleIsWorkloadProtectedValues() []IsWorkloadProtected {
	return []IsWorkloadProtected{
		IsWorkloadProtectedFalse,
		IsWorkloadProtectedTrue,
	}
}

// IssueType - The type of issue.
type IssueType string

const (
	IssueTypeAgentStopped        IssueType = "AgentStopped"
	IssueTypeDNSResolution       IssueType = "DnsResolution"
	IssueTypeGuestFirewall       IssueType = "GuestFirewall"
	IssueTypeNetworkSecurityRule IssueType = "NetworkSecurityRule"
	IssueTypePlatform            IssueType = "Platform"
	IssueTypePortThrottled       IssueType = "PortThrottled"
	IssueTypeSocketBind          IssueType = "SocketBind"
	IssueTypeUnknown             IssueType = "Unknown"
	IssueTypeUserDefinedRoute    IssueType = "UserDefinedRoute"
)

// PossibleIssueTypeValues returns the possible values for the IssueType const type.
func PossibleIssueTypeValues() []IssueType {
	return []IssueType{
		IssueTypeAgentStopped,
		IssueTypeDNSResolution,
		IssueTypeGuestFirewall,
		IssueTypeNetworkSecurityRule,
		IssueTypePlatform,
		IssueTypePortThrottled,
		IssueTypeSocketBind,
		IssueTypeUnknown,
		IssueTypeUserDefinedRoute,
	}
}

// LoadBalancerBackendAddressAdminState - A list of administrative states which once set can override health probe so that
// Load Balancer will always forward new connections to backend, or deny new connections and reset existing connections.
type LoadBalancerBackendAddressAdminState string

const (
	LoadBalancerBackendAddressAdminStateDown LoadBalancerBackendAddressAdminState = "Down"
	LoadBalancerBackendAddressAdminStateNone LoadBalancerBackendAddressAdminState = "None"
	LoadBalancerBackendAddressAdminStateUp   LoadBalancerBackendAddressAdminState = "Up"
)

// PossibleLoadBalancerBackendAddressAdminStateValues returns the possible values for the LoadBalancerBackendAddressAdminState const type.
func PossibleLoadBalancerBackendAddressAdminStateValues() []LoadBalancerBackendAddressAdminState {
	return []LoadBalancerBackendAddressAdminState{
		LoadBalancerBackendAddressAdminStateDown,
		LoadBalancerBackendAddressAdminStateNone,
		LoadBalancerBackendAddressAdminStateUp,
	}
}

// LoadBalancerOutboundRuleProtocol - The protocol for the outbound rule in load balancer.
type LoadBalancerOutboundRuleProtocol string

const (
	LoadBalancerOutboundRuleProtocolAll LoadBalancerOutboundRuleProtocol = "All"
	LoadBalancerOutboundRuleProtocolTCP LoadBalancerOutboundRuleProtocol = "Tcp"
	LoadBalancerOutboundRuleProtocolUDP LoadBalancerOutboundRuleProtocol = "Udp"
)

// PossibleLoadBalancerOutboundRuleProtocolValues returns the possible values for the LoadBalancerOutboundRuleProtocol const type.
func PossibleLoadBalancerOutboundRuleProtocolValues() []LoadBalancerOutboundRuleProtocol {
	return []LoadBalancerOutboundRuleProtocol{
		LoadBalancerOutboundRuleProtocolAll,
		LoadBalancerOutboundRuleProtocolTCP,
		LoadBalancerOutboundRuleProtocolUDP,
	}
}

// LoadBalancerSKUName - Name of a load balancer SKU.
type LoadBalancerSKUName string

const (
	LoadBalancerSKUNameBasic    LoadBalancerSKUName = "Basic"
	LoadBalancerSKUNameGateway  LoadBalancerSKUName = "Gateway"
	LoadBalancerSKUNameStandard LoadBalancerSKUName = "Standard"
)

// PossibleLoadBalancerSKUNameValues returns the possible values for the LoadBalancerSKUName const type.
func PossibleLoadBalancerSKUNameValues() []LoadBalancerSKUName {
	return []LoadBalancerSKUName{
		LoadBalancerSKUNameBasic,
		LoadBalancerSKUNameGateway,
		LoadBalancerSKUNameStandard,
	}
}

// LoadBalancerSKUTier - Tier of a load balancer SKU.
type LoadBalancerSKUTier string

const (
	LoadBalancerSKUTierGlobal   LoadBalancerSKUTier = "Global"
	LoadBalancerSKUTierRegional LoadBalancerSKUTier = "Regional"
)

// PossibleLoadBalancerSKUTierValues returns the possible values for the LoadBalancerSKUTier const type.
func PossibleLoadBalancerSKUTierValues() []LoadBalancerSKUTier {
	return []LoadBalancerSKUTier{
		LoadBalancerSKUTierGlobal,
		LoadBalancerSKUTierRegional,
	}
}

// LoadDistribution - The load distribution policy for this rule.
type LoadDistribution string

const (
	LoadDistributionDefault          LoadDistribution = "Default"
	LoadDistributionSourceIP         LoadDistribution = "SourceIP"
	LoadDistributionSourceIPProtocol LoadDistribution = "SourceIPProtocol"
)

// PossibleLoadDistributionValues returns the possible values for the LoadDistribution const type.
func PossibleLoadDistributionValues() []LoadDistribution {
	return []LoadDistribution{
		LoadDistributionDefault,
		LoadDistributionSourceIP,
		LoadDistributionSourceIPProtocol,
	}
}

// ManagedRuleEnabledState - The state of the managed rule. Defaults to Disabled if not specified.
type ManagedRuleEnabledState string

const (
	ManagedRuleEnabledStateDisabled ManagedRuleEnabledState = "Disabled"
	ManagedRuleEnabledStateEnabled  ManagedRuleEnabledState = "Enabled"
)

// PossibleManagedRuleEnabledStateValues returns the possible values for the ManagedRuleEnabledState const type.
func PossibleManagedRuleEnabledStateValues() []ManagedRuleEnabledState {
	return []ManagedRuleEnabledState{
		ManagedRuleEnabledStateDisabled,
		ManagedRuleEnabledStateEnabled,
	}
}

// NatGatewaySKUName - Name of Nat Gateway SKU.
type NatGatewaySKUName string

const (
	NatGatewaySKUNameStandard NatGatewaySKUName = "Standard"
)

// PossibleNatGatewaySKUNameValues returns the possible values for the NatGatewaySKUName const type.
func PossibleNatGatewaySKUNameValues() []NatGatewaySKUName {
	return []NatGatewaySKUName{
		NatGatewaySKUNameStandard,
	}
}

// NetworkIntentPolicyBasedService - Network intent policy based services.
type NetworkIntentPolicyBasedService string

const (
	NetworkIntentPolicyBasedServiceAll            NetworkIntentPolicyBasedService = "All"
	NetworkIntentPolicyBasedServiceAllowRulesOnly NetworkIntentPolicyBasedService = "AllowRulesOnly"
	NetworkIntentPolicyBasedServiceNone           NetworkIntentPolicyBasedService = "None"
)

// PossibleNetworkIntentPolicyBasedServiceValues returns the possible values for the NetworkIntentPolicyBasedService const type.
func PossibleNetworkIntentPolicyBasedServiceValues() []NetworkIntentPolicyBasedService {
	return []NetworkIntentPolicyBasedService{
		NetworkIntentPolicyBasedServiceAll,
		NetworkIntentPolicyBasedServiceAllowRulesOnly,
		NetworkIntentPolicyBasedServiceNone,
	}
}

// NetworkInterfaceAuxiliaryMode - Auxiliary mode of Network Interface resource.
type NetworkInterfaceAuxiliaryMode string

const (
	NetworkInterfaceAuxiliaryModeAcceleratedConnections NetworkInterfaceAuxiliaryMode = "AcceleratedConnections"
	NetworkInterfaceAuxiliaryModeFloating               NetworkInterfaceAuxiliaryMode = "Floating"
	NetworkInterfaceAuxiliaryModeMaxConnections         NetworkInterfaceAuxiliaryMode = "MaxConnections"
	NetworkInterfaceAuxiliaryModeNone                   NetworkInterfaceAuxiliaryMode = "None"
)

// PossibleNetworkInterfaceAuxiliaryModeValues returns the possible values for the NetworkInterfaceAuxiliaryMode const type.
func PossibleNetworkInterfaceAuxiliaryModeValues() []NetworkInterfaceAuxiliaryMode {
	return []NetworkInterfaceAuxiliaryMode{
		NetworkInterfaceAuxiliaryModeAcceleratedConnections,
		NetworkInterfaceAuxiliaryModeFloating,
		NetworkInterfaceAuxiliaryModeMaxConnections,
		NetworkInterfaceAuxiliaryModeNone,
	}
}

// NetworkInterfaceAuxiliarySKU - Auxiliary sku of Network Interface resource.
type NetworkInterfaceAuxiliarySKU string

const (
	NetworkInterfaceAuxiliarySKUA1   NetworkInterfaceAuxiliarySKU = "A1"
	NetworkInterfaceAuxiliarySKUA2   NetworkInterfaceAuxiliarySKU = "A2"
	NetworkInterfaceAuxiliarySKUA4   NetworkInterfaceAuxiliarySKU = "A4"
	NetworkInterfaceAuxiliarySKUA8   NetworkInterfaceAuxiliarySKU = "A8"
	NetworkInterfaceAuxiliarySKUNone NetworkInterfaceAuxiliarySKU = "None"
)

// PossibleNetworkInterfaceAuxiliarySKUValues returns the possible values for the NetworkInterfaceAuxiliarySKU const type.
func PossibleNetworkInterfaceAuxiliarySKUValues() []NetworkInterfaceAuxiliarySKU {
	return []NetworkInterfaceAuxiliarySKU{
		NetworkInterfaceAuxiliarySKUA1,
		NetworkInterfaceAuxiliarySKUA2,
		NetworkInterfaceAuxiliarySKUA4,
		NetworkInterfaceAuxiliarySKUA8,
		NetworkInterfaceAuxiliarySKUNone,
	}
}

// NetworkInterfaceMigrationPhase - Migration phase of Network Interface resource.
type NetworkInterfaceMigrationPhase string

const (
	NetworkInterfaceMigrationPhaseAbort     NetworkInterfaceMigrationPhase = "Abort"
	NetworkInterfaceMigrationPhaseCommit    NetworkInterfaceMigrationPhase = "Commit"
	NetworkInterfaceMigrationPhaseCommitted NetworkInterfaceMigrationPhase = "Committed"
	NetworkInterfaceMigrationPhaseNone      NetworkInterfaceMigrationPhase = "None"
	NetworkInterfaceMigrationPhasePrepare   NetworkInterfaceMigrationPhase = "Prepare"
)

// PossibleNetworkInterfaceMigrationPhaseValues returns the possible values for the NetworkInterfaceMigrationPhase const type.
func PossibleNetworkInterfaceMigrationPhaseValues() []NetworkInterfaceMigrationPhase {
	return []NetworkInterfaceMigrationPhase{
		NetworkInterfaceMigrationPhaseAbort,
		NetworkInterfaceMigrationPhaseCommit,
		NetworkInterfaceMigrationPhaseCommitted,
		NetworkInterfaceMigrationPhaseNone,
		NetworkInterfaceMigrationPhasePrepare,
	}
}

// NetworkInterfaceNicType - Type of Network Interface resource.
type NetworkInterfaceNicType string

const (
	NetworkInterfaceNicTypeElastic  NetworkInterfaceNicType = "Elastic"
	NetworkInterfaceNicTypeStandard NetworkInterfaceNicType = "Standard"
)

// PossibleNetworkInterfaceNicTypeValues returns the possible values for the NetworkInterfaceNicType const type.
func PossibleNetworkInterfaceNicTypeValues() []NetworkInterfaceNicType {
	return []NetworkInterfaceNicType{
		NetworkInterfaceNicTypeElastic,
		NetworkInterfaceNicTypeStandard,
	}
}

// NetworkOperationStatus - Status of the Azure async operation.
type NetworkOperationStatus string

const (
	NetworkOperationStatusFailed     NetworkOperationStatus = "Failed"
	NetworkOperationStatusInProgress NetworkOperationStatus = "InProgress"
	NetworkOperationStatusSucceeded  NetworkOperationStatus = "Succeeded"
)

// PossibleNetworkOperationStatusValues returns the possible values for the NetworkOperationStatus const type.
func PossibleNetworkOperationStatusValues() []NetworkOperationStatus {
	return []NetworkOperationStatus{
		NetworkOperationStatusFailed,
		NetworkOperationStatusInProgress,
		NetworkOperationStatusSucceeded,
	}
}

// NextHopType - Next hop type.
type NextHopType string

const (
	NextHopTypeHyperNetGateway       NextHopType = "HyperNetGateway"
	NextHopTypeInternet              NextHopType = "Internet"
	NextHopTypeNone                  NextHopType = "None"
	NextHopTypeVirtualAppliance      NextHopType = "VirtualAppliance"
	NextHopTypeVirtualNetworkGateway NextHopType = "VirtualNetworkGateway"
	NextHopTypeVnetLocal             NextHopType = "VnetLocal"
)

// PossibleNextHopTypeValues returns the possible values for the NextHopType const type.
func PossibleNextHopTypeValues() []NextHopType {
	return []NextHopType{
		NextHopTypeHyperNetGateway,
		NextHopTypeInternet,
		NextHopTypeNone,
		NextHopTypeVirtualAppliance,
		NextHopTypeVirtualNetworkGateway,
		NextHopTypeVnetLocal,
	}
}

// NextStep - Supported next step behaviors after a rule is applied to a matched route
type NextStep string

const (
	NextStepContinue  NextStep = "Continue"
	NextStepTerminate NextStep = "Terminate"
	NextStepUnknown   NextStep = "Unknown"
)

// PossibleNextStepValues returns the possible values for the NextStep const type.
func PossibleNextStepValues() []NextStep {
	return []NextStep{
		NextStepContinue,
		NextStepTerminate,
		NextStepUnknown,
	}
}

// OfficeTrafficCategory - The office traffic category.
type OfficeTrafficCategory string

const (
	OfficeTrafficCategoryAll              OfficeTrafficCategory = "All"
	OfficeTrafficCategoryNone             OfficeTrafficCategory = "None"
	OfficeTrafficCategoryOptimize         OfficeTrafficCategory = "Optimize"
	OfficeTrafficCategoryOptimizeAndAllow OfficeTrafficCategory = "OptimizeAndAllow"
)

// PossibleOfficeTrafficCategoryValues returns the possible values for the OfficeTrafficCategory const type.
func PossibleOfficeTrafficCategoryValues() []OfficeTrafficCategory {
	return []OfficeTrafficCategory{
		OfficeTrafficCategoryAll,
		OfficeTrafficCategoryNone,
		OfficeTrafficCategoryOptimize,
		OfficeTrafficCategoryOptimizeAndAllow,
	}
}

// Origin - The origin of the issue.
type Origin string

const (
	OriginInbound  Origin = "Inbound"
	OriginLocal    Origin = "Local"
	OriginOutbound Origin = "Outbound"
)

// PossibleOriginValues returns the possible values for the Origin const type.
func PossibleOriginValues() []Origin {
	return []Origin{
		OriginInbound,
		OriginLocal,
		OriginOutbound,
	}
}

// OutputType - Connection monitor output destination type. Currently, only "Workspace" is supported.
type OutputType string

const (
	OutputTypeWorkspace OutputType = "Workspace"
)

// PossibleOutputTypeValues returns the possible values for the OutputType const type.
func PossibleOutputTypeValues() []OutputType {
	return []OutputType{
		OutputTypeWorkspace,
	}
}

// OwaspCrsExclusionEntryMatchVariable - The variable to be excluded.
type OwaspCrsExclusionEntryMatchVariable string

const (
	OwaspCrsExclusionEntryMatchVariableRequestArgKeys      OwaspCrsExclusionEntryMatchVariable = "RequestArgKeys"
	OwaspCrsExclusionEntryMatchVariableRequestArgNames     OwaspCrsExclusionEntryMatchVariable = "RequestArgNames"
	OwaspCrsExclusionEntryMatchVariableRequestArgValues    OwaspCrsExclusionEntryMatchVariable = "RequestArgValues"
	OwaspCrsExclusionEntryMatchVariableRequestCookieKeys   OwaspCrsExclusionEntryMatchVariable = "RequestCookieKeys"
	OwaspCrsExclusionEntryMatchVariableRequestCookieNames  OwaspCrsExclusionEntryMatchVariable = "RequestCookieNames"
	OwaspCrsExclusionEntryMatchVariableRequestCookieValues OwaspCrsExclusionEntryMatchVariable = "RequestCookieValues"
	OwaspCrsExclusionEntryMatchVariableRequestHeaderKeys   OwaspCrsExclusionEntryMatchVariable = "RequestHeaderKeys"
	OwaspCrsExclusionEntryMatchVariableRequestHeaderNames  OwaspCrsExclusionEntryMatchVariable = "RequestHeaderNames"
	OwaspCrsExclusionEntryMatchVariableRequestHeaderValues OwaspCrsExclusionEntryMatchVariable = "RequestHeaderValues"
)

// PossibleOwaspCrsExclusionEntryMatchVariableValues returns the possible values for the OwaspCrsExclusionEntryMatchVariable const type.
func PossibleOwaspCrsExclusionEntryMatchVariableValues() []OwaspCrsExclusionEntryMatchVariable {
	return []OwaspCrsExclusionEntryMatchVariable{
		OwaspCrsExclusionEntryMatchVariableRequestArgKeys,
		OwaspCrsExclusionEntryMatchVariableRequestArgNames,
		OwaspCrsExclusionEntryMatchVariableRequestArgValues,
		OwaspCrsExclusionEntryMatchVariableRequestCookieKeys,
		OwaspCrsExclusionEntryMatchVariableRequestCookieNames,
		OwaspCrsExclusionEntryMatchVariableRequestCookieValues,
		OwaspCrsExclusionEntryMatchVariableRequestHeaderKeys,
		OwaspCrsExclusionEntryMatchVariableRequestHeaderNames,
		OwaspCrsExclusionEntryMatchVariableRequestHeaderValues,
	}
}

// OwaspCrsExclusionEntrySelectorMatchOperator - When matchVariable is a collection, operate on the selector to specify which
// elements in the collection this exclusion applies to.
type OwaspCrsExclusionEntrySelectorMatchOperator string

const (
	OwaspCrsExclusionEntrySelectorMatchOperatorContains   OwaspCrsExclusionEntrySelectorMatchOperator = "Contains"
	OwaspCrsExclusionEntrySelectorMatchOperatorEndsWith   OwaspCrsExclusionEntrySelectorMatchOperator = "EndsWith"
	OwaspCrsExclusionEntrySelectorMatchOperatorEquals     OwaspCrsExclusionEntrySelectorMatchOperator = "Equals"
	OwaspCrsExclusionEntrySelectorMatchOperatorEqualsAny  OwaspCrsExclusionEntrySelectorMatchOperator = "EqualsAny"
	OwaspCrsExclusionEntrySelectorMatchOperatorStartsWith OwaspCrsExclusionEntrySelectorMatchOperator = "StartsWith"
)

// PossibleOwaspCrsExclusionEntrySelectorMatchOperatorValues returns the possible values for the OwaspCrsExclusionEntrySelectorMatchOperator const type.
func PossibleOwaspCrsExclusionEntrySelectorMatchOperatorValues() []OwaspCrsExclusionEntrySelectorMatchOperator {
	return []OwaspCrsExclusionEntrySelectorMatchOperator{
		OwaspCrsExclusionEntrySelectorMatchOperatorContains,
		OwaspCrsExclusionEntrySelectorMatchOperatorEndsWith,
		OwaspCrsExclusionEntrySelectorMatchOperatorEquals,
		OwaspCrsExclusionEntrySelectorMatchOperatorEqualsAny,
		OwaspCrsExclusionEntrySelectorMatchOperatorStartsWith,
	}
}

// PacketCaptureTargetType - Target type of the resource provided.
type PacketCaptureTargetType string

const (
	PacketCaptureTargetTypeAzureVM   PacketCaptureTargetType = "AzureVM"
	PacketCaptureTargetTypeAzureVMSS PacketCaptureTargetType = "AzureVMSS"
)

// PossiblePacketCaptureTargetTypeValues returns the possible values for the PacketCaptureTargetType const type.
func PossiblePacketCaptureTargetTypeValues() []PacketCaptureTargetType {
	return []PacketCaptureTargetType{
		PacketCaptureTargetTypeAzureVM,
		PacketCaptureTargetTypeAzureVMSS,
	}
}

type PcError string

const (
	PcErrorAgentStopped    PcError = "AgentStopped"
	PcErrorCaptureFailed   PcError = "CaptureFailed"
	PcErrorInternalError   PcError = "InternalError"
	PcErrorLocalFileFailed PcError = "LocalFileFailed"
	PcErrorStorageFailed   PcError = "StorageFailed"
)

// PossiblePcErrorValues returns the possible values for the PcError const type.
func PossiblePcErrorValues() []PcError {
	return []PcError{
		PcErrorAgentStopped,
		PcErrorCaptureFailed,
		PcErrorInternalError,
		PcErrorLocalFileFailed,
		PcErrorStorageFailed,
	}
}

// PcProtocol - Protocol to be filtered on.
type PcProtocol string

const (
	PcProtocolAny PcProtocol = "Any"
	PcProtocolTCP PcProtocol = "TCP"
	PcProtocolUDP PcProtocol = "UDP"
)

// PossiblePcProtocolValues returns the possible values for the PcProtocol const type.
func PossiblePcProtocolValues() []PcProtocol {
	return []PcProtocol{
		PcProtocolAny,
		PcProtocolTCP,
		PcProtocolUDP,
	}
}

// PcStatus - The status of the packet capture session.
type PcStatus string

const (
	PcStatusError      PcStatus = "Error"
	PcStatusNotStarted PcStatus = "NotStarted"
	PcStatusRunning    PcStatus = "Running"
	PcStatusStopped    PcStatus = "Stopped"
	PcStatusUnknown    PcStatus = "Unknown"
)

// PossiblePcStatusValues returns the possible values for the PcStatus const type.
func PossiblePcStatusValues() []PcStatus {
	return []PcStatus{
		PcStatusError,
		PcStatusNotStarted,
		PcStatusRunning,
		PcStatusStopped,
		PcStatusUnknown,
	}
}

// PfsGroup - The Pfs Groups used in IKE Phase 2 for new child SA.
type PfsGroup string

const (
	PfsGroupECP256  PfsGroup = "ECP256"
	PfsGroupECP384  PfsGroup = "ECP384"
	PfsGroupNone    PfsGroup = "None"
	PfsGroupPFS1    PfsGroup = "PFS1"
	PfsGroupPFS14   PfsGroup = "PFS14"
	PfsGroupPFS2    PfsGroup = "PFS2"
	PfsGroupPFS2048 PfsGroup = "PFS2048"
	PfsGroupPFS24   PfsGroup = "PFS24"
	PfsGroupPFSMM   PfsGroup = "PFSMM"
)

// PossiblePfsGroupValues returns the possible values for the PfsGroup const type.
func PossiblePfsGroupValues() []PfsGroup {
	return []PfsGroup{
		PfsGroupECP256,
		PfsGroupECP384,
		PfsGroupNone,
		PfsGroupPFS1,
		PfsGroupPFS14,
		PfsGroupPFS2,
		PfsGroupPFS2048,
		PfsGroupPFS24,
		PfsGroupPFSMM,
	}
}

// PreferredIPVersion - The preferred IP version to use in test evaluation. The connection monitor may choose to use a different
// version depending on other parameters.
type PreferredIPVersion string

const (
	PreferredIPVersionIPv4 PreferredIPVersion = "IPv4"
	PreferredIPVersionIPv6 PreferredIPVersion = "IPv6"
)

// PossiblePreferredIPVersionValues returns the possible values for the PreferredIPVersion const type.
func PossiblePreferredIPVersionValues() []PreferredIPVersion {
	return []PreferredIPVersion{
		PreferredIPVersionIPv4,
		PreferredIPVersionIPv6,
	}
}

// PreferredRoutingGateway - The preferred routing gateway types
type PreferredRoutingGateway string

const (
	PreferredRoutingGatewayExpressRoute PreferredRoutingGateway = "ExpressRoute"
	PreferredRoutingGatewayNone         PreferredRoutingGateway = "None"
	PreferredRoutingGatewayVPNGateway   PreferredRoutingGateway = "VpnGateway"
)

// PossiblePreferredRoutingGatewayValues returns the possible values for the PreferredRoutingGateway const type.
func PossiblePreferredRoutingGatewayValues() []PreferredRoutingGateway {
	return []PreferredRoutingGateway{
		PreferredRoutingGatewayExpressRoute,
		PreferredRoutingGatewayNone,
		PreferredRoutingGatewayVPNGateway,
	}
}

// ProbeProtocol - The protocol of the end point. If 'Tcp' is specified, a received ACK is required for the probe to be successful.
// If 'Http' or 'Https' is specified, a 200 OK response from the specifies URI is required
// for the probe to be successful.
type ProbeProtocol string

const (
	ProbeProtocolHTTP  ProbeProtocol = "Http"
	ProbeProtocolHTTPS ProbeProtocol = "Https"
	ProbeProtocolTCP   ProbeProtocol = "Tcp"
)

// PossibleProbeProtocolValues returns the possible values for the ProbeProtocol const type.
func PossibleProbeProtocolValues() []ProbeProtocol {
	return []ProbeProtocol{
		ProbeProtocolHTTP,
		ProbeProtocolHTTPS,
		ProbeProtocolTCP,
	}
}

// ProcessorArchitecture - VPN client Processor Architecture.
type ProcessorArchitecture string

const (
	ProcessorArchitectureAmd64 ProcessorArchitecture = "Amd64"
	ProcessorArchitectureX86   ProcessorArchitecture = "X86"
)

// PossibleProcessorArchitectureValues returns the possible values for the ProcessorArchitecture const type.
func PossibleProcessorArchitectureValues() []ProcessorArchitecture {
	return []ProcessorArchitecture{
		ProcessorArchitectureAmd64,
		ProcessorArchitectureX86,
	}
}

// Protocol - Network protocol.
type Protocol string

const (
	ProtocolHTTP  Protocol = "Http"
	ProtocolHTTPS Protocol = "Https"
	ProtocolIcmp  Protocol = "Icmp"
	ProtocolTCP   Protocol = "Tcp"
)

// PossibleProtocolValues returns the possible values for the Protocol const type.
func PossibleProtocolValues() []Protocol {
	return []Protocol{
		ProtocolHTTP,
		ProtocolHTTPS,
		ProtocolIcmp,
		ProtocolTCP,
	}
}

// ProtocolType - RNM supported protocol types.
type ProtocolType string

const (
	ProtocolTypeAh       ProtocolType = "Ah"
	ProtocolTypeAll      ProtocolType = "All"
	ProtocolTypeDoNotUse ProtocolType = "DoNotUse"
	ProtocolTypeEsp      ProtocolType = "Esp"
	ProtocolTypeGre      ProtocolType = "Gre"
	ProtocolTypeIcmp     ProtocolType = "Icmp"
	ProtocolTypeTCP      ProtocolType = "Tcp"
	ProtocolTypeUDP      ProtocolType = "Udp"
	ProtocolTypeVxlan    ProtocolType = "Vxlan"
)

// PossibleProtocolTypeValues returns the possible values for the ProtocolType const type.
func PossibleProtocolTypeValues() []ProtocolType {
	return []ProtocolType{
		ProtocolTypeAh,
		ProtocolTypeAll,
		ProtocolTypeDoNotUse,
		ProtocolTypeEsp,
		ProtocolTypeGre,
		ProtocolTypeIcmp,
		ProtocolTypeTCP,
		ProtocolTypeUDP,
		ProtocolTypeVxlan,
	}
}

// ProvisioningState - The current provisioning state.
type ProvisioningState string

const (
	ProvisioningStateDeleting  ProvisioningState = "Deleting"
	ProvisioningStateFailed    ProvisioningState = "Failed"
	ProvisioningStateSucceeded ProvisioningState = "Succeeded"
	ProvisioningStateUpdating  ProvisioningState = "Updating"
)

// PossibleProvisioningStateValues returns the possible values for the ProvisioningState const type.
func PossibleProvisioningStateValues() []ProvisioningState {
	return []ProvisioningState{
		ProvisioningStateDeleting,
		ProvisioningStateFailed,
		ProvisioningStateSucceeded,
		ProvisioningStateUpdating,
	}
}

// PublicIPAddressDNSSettingsDomainNameLabelScope - The domain name label scope. If a domain name label and a domain name
// label scope are specified, an A DNS record is created for the public IP in the Microsoft Azure DNS system with a hashed
// value
// includes in FQDN.
type PublicIPAddressDNSSettingsDomainNameLabelScope string

const (
	PublicIPAddressDNSSettingsDomainNameLabelScopeNoReuse            PublicIPAddressDNSSettingsDomainNameLabelScope = "NoReuse"
	PublicIPAddressDNSSettingsDomainNameLabelScopeResourceGroupReuse PublicIPAddressDNSSettingsDomainNameLabelScope = "ResourceGroupReuse"
	PublicIPAddressDNSSettingsDomainNameLabelScopeSubscriptionReuse  PublicIPAddressDNSSettingsDomainNameLabelScope = "SubscriptionReuse"
	PublicIPAddressDNSSettingsDomainNameLabelScopeTenantReuse        PublicIPAddressDNSSettingsDomainNameLabelScope = "TenantReuse"
)

// PossiblePublicIPAddressDNSSettingsDomainNameLabelScopeValues returns the possible values for the PublicIPAddressDNSSettingsDomainNameLabelScope const type.
func PossiblePublicIPAddressDNSSettingsDomainNameLabelScopeValues() []PublicIPAddressDNSSettingsDomainNameLabelScope {
	return []PublicIPAddressDNSSettingsDomainNameLabelScope{
		PublicIPAddressDNSSettingsDomainNameLabelScopeNoReuse,
		PublicIPAddressDNSSettingsDomainNameLabelScopeResourceGroupReuse,
		PublicIPAddressDNSSettingsDomainNameLabelScopeSubscriptionReuse,
		PublicIPAddressDNSSettingsDomainNameLabelScopeTenantReuse,
	}
}

// PublicIPAddressMigrationPhase - Migration phase of Public IP Address.
type PublicIPAddressMigrationPhase string

const (
	PublicIPAddressMigrationPhaseAbort     PublicIPAddressMigrationPhase = "Abort"
	PublicIPAddressMigrationPhaseCommit    PublicIPAddressMigrationPhase = "Commit"
	PublicIPAddressMigrationPhaseCommitted PublicIPAddressMigrationPhase = "Committed"
	PublicIPAddressMigrationPhaseNone      PublicIPAddressMigrationPhase = "None"
	PublicIPAddressMigrationPhasePrepare   PublicIPAddressMigrationPhase = "Prepare"
)

// PossiblePublicIPAddressMigrationPhaseValues returns the possible values for the PublicIPAddressMigrationPhase const type.
func PossiblePublicIPAddressMigrationPhaseValues() []PublicIPAddressMigrationPhase {
	return []PublicIPAddressMigrationPhase{
		PublicIPAddressMigrationPhaseAbort,
		PublicIPAddressMigrationPhaseCommit,
		PublicIPAddressMigrationPhaseCommitted,
		PublicIPAddressMigrationPhaseNone,
		PublicIPAddressMigrationPhasePrepare,
	}
}

// PublicIPAddressSKUName - Name of a public IP address SKU.
type PublicIPAddressSKUName string

const (
	PublicIPAddressSKUNameBasic    PublicIPAddressSKUName = "Basic"
	PublicIPAddressSKUNameStandard PublicIPAddressSKUName = "Standard"
)

// PossiblePublicIPAddressSKUNameValues returns the possible values for the PublicIPAddressSKUName const type.
func PossiblePublicIPAddressSKUNameValues() []PublicIPAddressSKUName {
	return []PublicIPAddressSKUName{
		PublicIPAddressSKUNameBasic,
		PublicIPAddressSKUNameStandard,
	}
}

// PublicIPAddressSKUTier - Tier of a public IP address SKU.
type PublicIPAddressSKUTier string

const (
	PublicIPAddressSKUTierGlobal   PublicIPAddressSKUTier = "Global"
	PublicIPAddressSKUTierRegional PublicIPAddressSKUTier = "Regional"
)

// PossiblePublicIPAddressSKUTierValues returns the possible values for the PublicIPAddressSKUTier const type.
func PossiblePublicIPAddressSKUTierValues() []PublicIPAddressSKUTier {
	return []PublicIPAddressSKUTier{
		PublicIPAddressSKUTierGlobal,
		PublicIPAddressSKUTierRegional,
	}
}

// PublicIPPrefixSKUName - Name of a public IP prefix SKU.
type PublicIPPrefixSKUName string

const (
	PublicIPPrefixSKUNameStandard PublicIPPrefixSKUName = "Standard"
)

// PossiblePublicIPPrefixSKUNameValues returns the possible values for the PublicIPPrefixSKUName const type.
func PossiblePublicIPPrefixSKUNameValues() []PublicIPPrefixSKUName {
	return []PublicIPPrefixSKUName{
		PublicIPPrefixSKUNameStandard,
	}
}

// PublicIPPrefixSKUTier - Tier of a public IP prefix SKU.
type PublicIPPrefixSKUTier string

const (
	PublicIPPrefixSKUTierGlobal   PublicIPPrefixSKUTier = "Global"
	PublicIPPrefixSKUTierRegional PublicIPPrefixSKUTier = "Regional"
)

// PossiblePublicIPPrefixSKUTierValues returns the possible values for the PublicIPPrefixSKUTier const type.
func PossiblePublicIPPrefixSKUTierValues() []PublicIPPrefixSKUTier {
	return []PublicIPPrefixSKUTier{
		PublicIPPrefixSKUTierGlobal,
		PublicIPPrefixSKUTierRegional,
	}
}

// ResourceIdentityType - The type of identity used for the resource. The type 'SystemAssigned, UserAssigned' includes both
// an implicitly created identity and a set of user assigned identities. The type 'None' will remove any
// identities from the virtual machine.
type ResourceIdentityType string

const (
	ResourceIdentityTypeNone                       ResourceIdentityType = "None"
	ResourceIdentityTypeSystemAssigned             ResourceIdentityType = "SystemAssigned"
	ResourceIdentityTypeSystemAssignedUserAssigned ResourceIdentityType = "SystemAssigned, UserAssigned"
	ResourceIdentityTypeUserAssigned               ResourceIdentityType = "UserAssigned"
)

// PossibleResourceIdentityTypeValues returns the possible values for the ResourceIdentityType const type.
func PossibleResourceIdentityTypeValues() []ResourceIdentityType {
	return []ResourceIdentityType{
		ResourceIdentityTypeNone,
		ResourceIdentityTypeSystemAssigned,
		ResourceIdentityTypeSystemAssignedUserAssigned,
		ResourceIdentityTypeUserAssigned,
	}
}

// RouteFilterRuleType - The rule type of the rule.
type RouteFilterRuleType string

const (
	RouteFilterRuleTypeCommunity RouteFilterRuleType = "Community"
)

// PossibleRouteFilterRuleTypeValues returns the possible values for the RouteFilterRuleType const type.
func PossibleRouteFilterRuleTypeValues() []RouteFilterRuleType {
	return []RouteFilterRuleType{
		RouteFilterRuleTypeCommunity,
	}
}

// RouteMapActionType - Kind of actions which can be taken on a matched route. Add, Replace, Remove refer to parameters on
// the route, like community or prefix
type RouteMapActionType string

const (
	RouteMapActionTypeAdd     RouteMapActionType = "Add"
	RouteMapActionTypeDrop    RouteMapActionType = "Drop"
	RouteMapActionTypeRemove  RouteMapActionType = "Remove"
	RouteMapActionTypeReplace RouteMapActionType = "Replace"
	RouteMapActionTypeUnknown RouteMapActionType = "Unknown"
)

// PossibleRouteMapActionTypeValues returns the possible values for the RouteMapActionType const type.
func PossibleRouteMapActionTypeValues() []RouteMapActionType {
	return []RouteMapActionType{
		RouteMapActionTypeAdd,
		RouteMapActionTypeDrop,
		RouteMapActionTypeRemove,
		RouteMapActionTypeReplace,
		RouteMapActionTypeUnknown,
	}
}

// RouteMapMatchCondition - Match condition to apply RouteMap rules.
type RouteMapMatchCondition string

const (
	RouteMapMatchConditionContains    RouteMapMatchCondition = "Contains"
	RouteMapMatchConditionEquals      RouteMapMatchCondition = "Equals"
	RouteMapMatchConditionNotContains RouteMapMatchCondition = "NotContains"
	RouteMapMatchConditionNotEquals   RouteMapMatchCondition = "NotEquals"
	RouteMapMatchConditionUnknown     RouteMapMatchCondition = "Unknown"
)

// PossibleRouteMapMatchConditionValues returns the possible values for the RouteMapMatchCondition const type.
func PossibleRouteMapMatchConditionValues() []RouteMapMatchCondition {
	return []RouteMapMatchCondition{
		RouteMapMatchConditionContains,
		RouteMapMatchConditionEquals,
		RouteMapMatchConditionNotContains,
		RouteMapMatchConditionNotEquals,
		RouteMapMatchConditionUnknown,
	}
}

// RouteNextHopType - The type of Azure hop the packet should be sent to.
type RouteNextHopType string

const (
	RouteNextHopTypeInternet              RouteNextHopType = "Internet"
	RouteNextHopTypeNone                  RouteNextHopType = "None"
	RouteNextHopTypeVirtualAppliance      RouteNextHopType = "VirtualAppliance"
	RouteNextHopTypeVirtualNetworkGateway RouteNextHopType = "VirtualNetworkGateway"
	RouteNextHopTypeVnetLocal             RouteNextHopType = "VnetLocal"
)

// PossibleRouteNextHopTypeValues returns the possible values for the RouteNextHopType const type.
func PossibleRouteNextHopTypeValues() []RouteNextHopType {
	return []RouteNextHopType{
		RouteNextHopTypeInternet,
		RouteNextHopTypeNone,
		RouteNextHopTypeVirtualAppliance,
		RouteNextHopTypeVirtualNetworkGateway,
		RouteNextHopTypeVnetLocal,
	}
}

// RoutingState - The current routing state of the VirtualHub.
type RoutingState string

const (
	RoutingStateFailed       RoutingState = "Failed"
	RoutingStateNone         RoutingState = "None"
	RoutingStateProvisioned  RoutingState = "Provisioned"
	RoutingStateProvisioning RoutingState = "Provisioning"
)

// PossibleRoutingStateValues returns the possible values for the RoutingState const type.
func PossibleRoutingStateValues() []RoutingState {
	return []RoutingState{
		RoutingStateFailed,
		RoutingStateNone,
		RoutingStateProvisioned,
		RoutingStateProvisioning,
	}
}

// ScopeConnectionState - The current scope connection state.
type ScopeConnectionState string

const (
	ScopeConnectionStateConflict  ScopeConnectionState = "Conflict"
	ScopeConnectionStateConnected ScopeConnectionState = "Connected"
	ScopeConnectionStatePending   ScopeConnectionState = "Pending"
	ScopeConnectionStateRejected  ScopeConnectionState = "Rejected"
	ScopeConnectionStateRevoked   ScopeConnectionState = "Revoked"
)

// PossibleScopeConnectionStateValues returns the possible values for the ScopeConnectionState const type.
func PossibleScopeConnectionStateValues() []ScopeConnectionState {
	return []ScopeConnectionState{
		ScopeConnectionStateConflict,
		ScopeConnectionStateConnected,
		ScopeConnectionStatePending,
		ScopeConnectionStateRejected,
		ScopeConnectionStateRevoked,
	}
}

// ScrubbingRuleEntryMatchOperator - When matchVariable is a collection, operate on the selector to specify which elements
// in the collection this rule applies to.
type ScrubbingRuleEntryMatchOperator string

const (
	ScrubbingRuleEntryMatchOperatorEquals    ScrubbingRuleEntryMatchOperator = "Equals"
	ScrubbingRuleEntryMatchOperatorEqualsAny ScrubbingRuleEntryMatchOperator = "EqualsAny"
)

// PossibleScrubbingRuleEntryMatchOperatorValues returns the possible values for the ScrubbingRuleEntryMatchOperator const type.
func PossibleScrubbingRuleEntryMatchOperatorValues() []ScrubbingRuleEntryMatchOperator {
	return []ScrubbingRuleEntryMatchOperator{
		ScrubbingRuleEntryMatchOperatorEquals,
		ScrubbingRuleEntryMatchOperatorEqualsAny,
	}
}

// ScrubbingRuleEntryMatchVariable - The variable to be scrubbed from the logs.
type ScrubbingRuleEntryMatchVariable string

const (
	ScrubbingRuleEntryMatchVariableRequestArgNames     ScrubbingRuleEntryMatchVariable = "RequestArgNames"
	ScrubbingRuleEntryMatchVariableRequestCookieNames  ScrubbingRuleEntryMatchVariable = "RequestCookieNames"
	ScrubbingRuleEntryMatchVariableRequestHeaderNames  ScrubbingRuleEntryMatchVariable = "RequestHeaderNames"
	ScrubbingRuleEntryMatchVariableRequestIPAddress    ScrubbingRuleEntryMatchVariable = "RequestIPAddress"
	ScrubbingRuleEntryMatchVariableRequestJSONArgNames ScrubbingRuleEntryMatchVariable = "RequestJSONArgNames"
	ScrubbingRuleEntryMatchVariableRequestPostArgNames ScrubbingRuleEntryMatchVariable = "RequestPostArgNames"
)

// PossibleScrubbingRuleEntryMatchVariableValues returns the possible values for the ScrubbingRuleEntryMatchVariable const type.
func PossibleScrubbingRuleEntryMatchVariableValues() []ScrubbingRuleEntryMatchVariable {
	return []ScrubbingRuleEntryMatchVariable{
		ScrubbingRuleEntryMatchVariableRequestArgNames,
		ScrubbingRuleEntryMatchVariableRequestCookieNames,
		ScrubbingRuleEntryMatchVariableRequestHeaderNames,
		ScrubbingRuleEntryMatchVariableRequestIPAddress,
		ScrubbingRuleEntryMatchVariableRequestJSONArgNames,
		ScrubbingRuleEntryMatchVariableRequestPostArgNames,
	}
}

// ScrubbingRuleEntryState - Defines the state of log scrubbing rule. Default value is Enabled.
type ScrubbingRuleEntryState string

const (
	ScrubbingRuleEntryStateDisabled ScrubbingRuleEntryState = "Disabled"
	ScrubbingRuleEntryStateEnabled  ScrubbingRuleEntryState = "Enabled"
)

// PossibleScrubbingRuleEntryStateValues returns the possible values for the ScrubbingRuleEntryState const type.
func PossibleScrubbingRuleEntryStateValues() []ScrubbingRuleEntryState {
	return []ScrubbingRuleEntryState{
		ScrubbingRuleEntryStateDisabled,
		ScrubbingRuleEntryStateEnabled,
	}
}

// SecurityConfigurationRuleAccess - Whether network traffic is allowed or denied.
type SecurityConfigurationRuleAccess string

const (
	SecurityConfigurationRuleAccessAllow       SecurityConfigurationRuleAccess = "Allow"
	SecurityConfigurationRuleAccessAlwaysAllow SecurityConfigurationRuleAccess = "AlwaysAllow"
	SecurityConfigurationRuleAccessDeny        SecurityConfigurationRuleAccess = "Deny"
)

// PossibleSecurityConfigurationRuleAccessValues returns the possible values for the SecurityConfigurationRuleAccess const type.
func PossibleSecurityConfigurationRuleAccessValues() []SecurityConfigurationRuleAccess {
	return []SecurityConfigurationRuleAccess{
		SecurityConfigurationRuleAccessAllow,
		SecurityConfigurationRuleAccessAlwaysAllow,
		SecurityConfigurationRuleAccessDeny,
	}
}

// SecurityConfigurationRuleDirection - The direction of the rule. The direction specifies if the rule will be evaluated on
// incoming or outgoing traffic.
type SecurityConfigurationRuleDirection string

const (
	SecurityConfigurationRuleDirectionInbound  SecurityConfigurationRuleDirection = "Inbound"
	SecurityConfigurationRuleDirectionOutbound SecurityConfigurationRuleDirection = "Outbound"
)

// PossibleSecurityConfigurationRuleDirectionValues returns the possible values for the SecurityConfigurationRuleDirection const type.
func PossibleSecurityConfigurationRuleDirectionValues() []SecurityConfigurationRuleDirection {
	return []SecurityConfigurationRuleDirection{
		SecurityConfigurationRuleDirectionInbound,
		SecurityConfigurationRuleDirectionOutbound,
	}
}

// SecurityConfigurationRuleProtocol - Network protocol this rule applies to.
type SecurityConfigurationRuleProtocol string

const (
	SecurityConfigurationRuleProtocolAh   SecurityConfigurationRuleProtocol = "Ah"
	SecurityConfigurationRuleProtocolAny  SecurityConfigurationRuleProtocol = "Any"
	SecurityConfigurationRuleProtocolEsp  SecurityConfigurationRuleProtocol = "Esp"
	SecurityConfigurationRuleProtocolIcmp SecurityConfigurationRuleProtocol = "Icmp"
	SecurityConfigurationRuleProtocolTCP  SecurityConfigurationRuleProtocol = "Tcp"
	SecurityConfigurationRuleProtocolUDP  SecurityConfigurationRuleProtocol = "Udp"
)

// PossibleSecurityConfigurationRuleProtocolValues returns the possible values for the SecurityConfigurationRuleProtocol const type.
func PossibleSecurityConfigurationRuleProtocolValues() []SecurityConfigurationRuleProtocol {
	return []SecurityConfigurationRuleProtocol{
		SecurityConfigurationRuleProtocolAh,
		SecurityConfigurationRuleProtocolAny,
		SecurityConfigurationRuleProtocolEsp,
		SecurityConfigurationRuleProtocolIcmp,
		SecurityConfigurationRuleProtocolTCP,
		SecurityConfigurationRuleProtocolUDP,
	}
}

// SecurityPartnerProviderConnectionStatus - The current state of the connection with Security Partner Provider.
type SecurityPartnerProviderConnectionStatus string

const (
	SecurityPartnerProviderConnectionStatusConnected          SecurityPartnerProviderConnectionStatus = "Connected"
	SecurityPartnerProviderConnectionStatusNotConnected       SecurityPartnerProviderConnectionStatus = "NotConnected"
	SecurityPartnerProviderConnectionStatusPartiallyConnected SecurityPartnerProviderConnectionStatus = "PartiallyConnected"
	SecurityPartnerProviderConnectionStatusUnknown            SecurityPartnerProviderConnectionStatus = "Unknown"
)

// PossibleSecurityPartnerProviderConnectionStatusValues returns the possible values for the SecurityPartnerProviderConnectionStatus const type.
func PossibleSecurityPartnerProviderConnectionStatusValues() []SecurityPartnerProviderConnectionStatus {
	return []SecurityPartnerProviderConnectionStatus{
		SecurityPartnerProviderConnectionStatusConnected,
		SecurityPartnerProviderConnectionStatusNotConnected,
		SecurityPartnerProviderConnectionStatusPartiallyConnected,
		SecurityPartnerProviderConnectionStatusUnknown,
	}
}

// SecurityProviderName - The Security Providers.
type SecurityProviderName string

const (
	SecurityProviderNameCheckpoint SecurityProviderName = "Checkpoint"
	SecurityProviderNameIBoss      SecurityProviderName = "IBoss"
	SecurityProviderNameZScaler    SecurityProviderName = "ZScaler"
)

// PossibleSecurityProviderNameValues returns the possible values for the SecurityProviderName const type.
func PossibleSecurityProviderNameValues() []SecurityProviderName {
	return []SecurityProviderName{
		SecurityProviderNameCheckpoint,
		SecurityProviderNameIBoss,
		SecurityProviderNameZScaler,
	}
}

// SecurityRuleAccess - Whether network traffic is allowed or denied.
type SecurityRuleAccess string

const (
	SecurityRuleAccessAllow SecurityRuleAccess = "Allow"
	SecurityRuleAccessDeny  SecurityRuleAccess = "Deny"
)

// PossibleSecurityRuleAccessValues returns the possible values for the SecurityRuleAccess const type.
func PossibleSecurityRuleAccessValues() []SecurityRuleAccess {
	return []SecurityRuleAccess{
		SecurityRuleAccessAllow,
		SecurityRuleAccessDeny,
	}
}

// SecurityRuleDirection - The direction of the rule. The direction specifies if rule will be evaluated on incoming or outgoing
// traffic.
type SecurityRuleDirection string

const (
	SecurityRuleDirectionInbound  SecurityRuleDirection = "Inbound"
	SecurityRuleDirectionOutbound SecurityRuleDirection = "Outbound"
)

// PossibleSecurityRuleDirectionValues returns the possible values for the SecurityRuleDirection const type.
func PossibleSecurityRuleDirectionValues() []SecurityRuleDirection {
	return []SecurityRuleDirection{
		SecurityRuleDirectionInbound,
		SecurityRuleDirectionOutbound,
	}
}

// SecurityRuleProtocol - Network protocol this rule applies to.
type SecurityRuleProtocol string

const (
	SecurityRuleProtocolAh       SecurityRuleProtocol = "Ah"
	SecurityRuleProtocolAsterisk SecurityRuleProtocol = "*"
	SecurityRuleProtocolEsp      SecurityRuleProtocol = "Esp"
	SecurityRuleProtocolIcmp     SecurityRuleProtocol = "Icmp"
	SecurityRuleProtocolTCP      SecurityRuleProtocol = "Tcp"
	SecurityRuleProtocolUDP      SecurityRuleProtocol = "Udp"
)

// PossibleSecurityRuleProtocolValues returns the possible values for the SecurityRuleProtocol const type.
func PossibleSecurityRuleProtocolValues() []SecurityRuleProtocol {
	return []SecurityRuleProtocol{
		SecurityRuleProtocolAh,
		SecurityRuleProtocolAsterisk,
		SecurityRuleProtocolEsp,
		SecurityRuleProtocolIcmp,
		SecurityRuleProtocolTCP,
		SecurityRuleProtocolUDP,
	}
}

// ServiceProviderProvisioningState - The ServiceProviderProvisioningState state of the resource.
type ServiceProviderProvisioningState string

const (
	ServiceProviderProvisioningStateDeprovisioning ServiceProviderProvisioningState = "Deprovisioning"
	ServiceProviderProvisioningStateNotProvisioned ServiceProviderProvisioningState = "NotProvisioned"
	ServiceProviderProvisioningStateProvisioned    ServiceProviderProvisioningState = "Provisioned"
	ServiceProviderProvisioningStateProvisioning   ServiceProviderProvisioningState = "Provisioning"
)

// PossibleServiceProviderProvisioningStateValues returns the possible values for the ServiceProviderProvisioningState const type.
func PossibleServiceProviderProvisioningStateValues() []ServiceProviderProvisioningState {
	return []ServiceProviderProvisioningState{
		ServiceProviderProvisioningStateDeprovisioning,
		ServiceProviderProvisioningStateNotProvisioned,
		ServiceProviderProvisioningStateProvisioned,
		ServiceProviderProvisioningStateProvisioning,
	}
}

// Severity - The severity of the issue.
type Severity string

const (
	SeverityError   Severity = "Error"
	SeverityWarning Severity = "Warning"
)

// PossibleSeverityValues returns the possible values for the Severity const type.
func PossibleSeverityValues() []Severity {
	return []Severity{
		SeverityError,
		SeverityWarning,
	}
}

// SlotType - Specifies slot info on a cloud service
type SlotType string

const (
	SlotTypeProduction SlotType = "Production"
	SlotTypeStaging    SlotType = "Staging"
)

// PossibleSlotTypeValues returns the possible values for the SlotType const type.
func PossibleSlotTypeValues() []SlotType {
	return []SlotType{
		SlotTypeProduction,
		SlotTypeStaging,
	}
}

// SyncMode - Backend address synchronous mode for the backend pool
type SyncMode string

const (
	SyncModeAutomatic SyncMode = "Automatic"
	SyncModeManual    SyncMode = "Manual"
)

// PossibleSyncModeValues returns the possible values for the SyncMode const type.
func PossibleSyncModeValues() []SyncMode {
	return []SyncMode{
		SyncModeAutomatic,
		SyncModeManual,
	}
}

type SyncRemoteAddressSpace string

const (
	SyncRemoteAddressSpaceTrue SyncRemoteAddressSpace = "true"
)

// PossibleSyncRemoteAddressSpaceValues returns the possible values for the SyncRemoteAddressSpace const type.
func PossibleSyncRemoteAddressSpaceValues() []SyncRemoteAddressSpace {
	return []SyncRemoteAddressSpace{
		SyncRemoteAddressSpaceTrue,
	}
}

// TransportProtocol - The transport protocol for the endpoint.
type TransportProtocol string

const (
	TransportProtocolAll TransportProtocol = "All"
	TransportProtocolTCP TransportProtocol = "Tcp"
	TransportProtocolUDP TransportProtocol = "Udp"
)

// PossibleTransportProtocolValues returns the possible values for the TransportProtocol const type.
func PossibleTransportProtocolValues() []TransportProtocol {
	return []TransportProtocol{
		TransportProtocolAll,
		TransportProtocolTCP,
		TransportProtocolUDP,
	}
}

// TunnelConnectionStatus - The current state of the tunnel.
type TunnelConnectionStatus string

const (
	TunnelConnectionStatusConnected    TunnelConnectionStatus = "Connected"
	TunnelConnectionStatusConnecting   TunnelConnectionStatus = "Connecting"
	TunnelConnectionStatusNotConnected TunnelConnectionStatus = "NotConnected"
	TunnelConnectionStatusUnknown      TunnelConnectionStatus = "Unknown"
)

// PossibleTunnelConnectionStatusValues returns the possible values for the TunnelConnectionStatus const type.
func PossibleTunnelConnectionStatusValues() []TunnelConnectionStatus {
	return []TunnelConnectionStatus{
		TunnelConnectionStatusConnected,
		TunnelConnectionStatusConnecting,
		TunnelConnectionStatusNotConnected,
		TunnelConnectionStatusUnknown,
	}
}

// UsageUnit - An enum describing the unit of measurement.
type UsageUnit string

const (
	UsageUnitCount UsageUnit = "Count"
)

// PossibleUsageUnitValues returns the possible values for the UsageUnit const type.
func PossibleUsageUnitValues() []UsageUnit {
	return []UsageUnit{
		UsageUnitCount,
	}
}

// UseHubGateway - Flag if need to use hub gateway.
type UseHubGateway string

const (
	UseHubGatewayFalse UseHubGateway = "False"
	UseHubGatewayTrue  UseHubGateway = "True"
)

// PossibleUseHubGatewayValues returns the possible values for the UseHubGateway const type.
func PossibleUseHubGatewayValues() []UseHubGateway {
	return []UseHubGateway{
		UseHubGatewayFalse,
		UseHubGatewayTrue,
	}
}

// VPNAuthenticationType - VPN authentication types enabled for the virtual network gateway.
type VPNAuthenticationType string

const (
	VPNAuthenticationTypeAAD         VPNAuthenticationType = "AAD"
	VPNAuthenticationTypeCertificate VPNAuthenticationType = "Certificate"
	VPNAuthenticationTypeRadius      VPNAuthenticationType = "Radius"
)

// PossibleVPNAuthenticationTypeValues returns the possible values for the VPNAuthenticationType const type.
func PossibleVPNAuthenticationTypeValues() []VPNAuthenticationType {
	return []VPNAuthenticationType{
		VPNAuthenticationTypeAAD,
		VPNAuthenticationTypeCertificate,
		VPNAuthenticationTypeRadius,
	}
}

// VPNClientProtocol - VPN client protocol enabled for the virtual network gateway.
type VPNClientProtocol string

const (
	VPNClientProtocolIkeV2   VPNClientProtocol = "IkeV2"
	VPNClientProtocolOpenVPN VPNClientProtocol = "OpenVPN"
	VPNClientProtocolSSTP    VPNClientProtocol = "SSTP"
)

// PossibleVPNClientProtocolValues returns the possible values for the VPNClientProtocol const type.
func PossibleVPNClientProtocolValues() []VPNClientProtocol {
	return []VPNClientProtocol{
		VPNClientProtocolIkeV2,
		VPNClientProtocolOpenVPN,
		VPNClientProtocolSSTP,
	}
}

// VPNConnectionStatus - The current state of the vpn connection.
type VPNConnectionStatus string

const (
	VPNConnectionStatusConnected    VPNConnectionStatus = "Connected"
	VPNConnectionStatusConnecting   VPNConnectionStatus = "Connecting"
	VPNConnectionStatusNotConnected VPNConnectionStatus = "NotConnected"
	VPNConnectionStatusUnknown      VPNConnectionStatus = "Unknown"
)

// PossibleVPNConnectionStatusValues returns the possible values for the VPNConnectionStatus const type.
func PossibleVPNConnectionStatusValues() []VPNConnectionStatus {
	return []VPNConnectionStatus{
		VPNConnectionStatusConnected,
		VPNConnectionStatusConnecting,
		VPNConnectionStatusNotConnected,
		VPNConnectionStatusUnknown,
	}
}

// VPNGatewayGeneration - The generation for this VirtualNetworkGateway. Must be None if gatewayType is not VPN.
type VPNGatewayGeneration string

const (
	VPNGatewayGenerationGeneration1 VPNGatewayGeneration = "Generation1"
	VPNGatewayGenerationGeneration2 VPNGatewayGeneration = "Generation2"
	VPNGatewayGenerationNone        VPNGatewayGeneration = "None"
)

// PossibleVPNGatewayGenerationValues returns the possible values for the VPNGatewayGeneration const type.
func PossibleVPNGatewayGenerationValues() []VPNGatewayGeneration {
	return []VPNGatewayGeneration{
		VPNGatewayGenerationGeneration1,
		VPNGatewayGenerationGeneration2,
		VPNGatewayGenerationNone,
	}
}

// VPNGatewayTunnelingProtocol - VPN protocol enabled for the VpnServerConfiguration.
type VPNGatewayTunnelingProtocol string

const (
	VPNGatewayTunnelingProtocolIkeV2   VPNGatewayTunnelingProtocol = "IkeV2"
	VPNGatewayTunnelingProtocolOpenVPN VPNGatewayTunnelingProtocol = "OpenVPN"
)

// PossibleVPNGatewayTunnelingProtocolValues returns the possible values for the VPNGatewayTunnelingProtocol const type.
func PossibleVPNGatewayTunnelingProtocolValues() []VPNGatewayTunnelingProtocol {
	return []VPNGatewayTunnelingProtocol{
		VPNGatewayTunnelingProtocolIkeV2,
		VPNGatewayTunnelingProtocolOpenVPN,
	}
}

// VPNLinkConnectionMode - Vpn link connection mode.
type VPNLinkConnectionMode string

const (
	VPNLinkConnectionModeDefault       VPNLinkConnectionMode = "Default"
	VPNLinkConnectionModeInitiatorOnly VPNLinkConnectionMode = "InitiatorOnly"
	VPNLinkConnectionModeResponderOnly VPNLinkConnectionMode = "ResponderOnly"
)

// PossibleVPNLinkConnectionModeValues returns the possible values for the VPNLinkConnectionMode const type.
func PossibleVPNLinkConnectionModeValues() []VPNLinkConnectionMode {
	return []VPNLinkConnectionMode{
		VPNLinkConnectionModeDefault,
		VPNLinkConnectionModeInitiatorOnly,
		VPNLinkConnectionModeResponderOnly,
	}
}

// VPNNatRuleMode - The Source NAT direction of a VPN NAT.
type VPNNatRuleMode string

const (
	VPNNatRuleModeEgressSnat  VPNNatRuleMode = "EgressSnat"
	VPNNatRuleModeIngressSnat VPNNatRuleMode = "IngressSnat"
)

// PossibleVPNNatRuleModeValues returns the possible values for the VPNNatRuleMode const type.
func PossibleVPNNatRuleModeValues() []VPNNatRuleMode {
	return []VPNNatRuleMode{
		VPNNatRuleModeEgressSnat,
		VPNNatRuleModeIngressSnat,
	}
}

// VPNNatRuleType - The type of NAT rule for VPN NAT.
type VPNNatRuleType string

const (
	VPNNatRuleTypeDynamic VPNNatRuleType = "Dynamic"
	VPNNatRuleTypeStatic  VPNNatRuleType = "Static"
)

// PossibleVPNNatRuleTypeValues returns the possible values for the VPNNatRuleType const type.
func PossibleVPNNatRuleTypeValues() []VPNNatRuleType {
	return []VPNNatRuleType{
		VPNNatRuleTypeDynamic,
		VPNNatRuleTypeStatic,
	}
}

// VPNPolicyMemberAttributeType - The Vpn Policy member attribute type.
type VPNPolicyMemberAttributeType string

const (
	VPNPolicyMemberAttributeTypeAADGroupID         VPNPolicyMemberAttributeType = "AADGroupId"
	VPNPolicyMemberAttributeTypeCertificateGroupID VPNPolicyMemberAttributeType = "CertificateGroupId"
	VPNPolicyMemberAttributeTypeRadiusAzureGroupID VPNPolicyMemberAttributeType = "RadiusAzureGroupId"
)

// PossibleVPNPolicyMemberAttributeTypeValues returns the possible values for the VPNPolicyMemberAttributeType const type.
func PossibleVPNPolicyMemberAttributeTypeValues() []VPNPolicyMemberAttributeType {
	return []VPNPolicyMemberAttributeType{
		VPNPolicyMemberAttributeTypeAADGroupID,
		VPNPolicyMemberAttributeTypeCertificateGroupID,
		VPNPolicyMemberAttributeTypeRadiusAzureGroupID,
	}
}

// VPNType - The type of this virtual network gateway.
type VPNType string

const (
	VPNTypePolicyBased VPNType = "PolicyBased"
	VPNTypeRouteBased  VPNType = "RouteBased"
)

// PossibleVPNTypeValues returns the possible values for the VPNType const type.
func PossibleVPNTypeValues() []VPNType {
	return []VPNType{
		VPNTypePolicyBased,
		VPNTypeRouteBased,
	}
}

// VerbosityLevel - Verbosity level.
type VerbosityLevel string

const (
	VerbosityLevelFull    VerbosityLevel = "Full"
	VerbosityLevelMinimum VerbosityLevel = "Minimum"
	VerbosityLevelNormal  VerbosityLevel = "Normal"
)

// PossibleVerbosityLevelValues returns the possible values for the VerbosityLevel const type.
func PossibleVerbosityLevelValues() []VerbosityLevel {
	return []VerbosityLevel{
		VerbosityLevelFull,
		VerbosityLevelMinimum,
		VerbosityLevelNormal,
	}
}

// VirtualNetworkEncryptionEnforcement - If the encrypted VNet allows VM that does not support encryption
type VirtualNetworkEncryptionEnforcement string

const (
	VirtualNetworkEncryptionEnforcementAllowUnencrypted VirtualNetworkEncryptionEnforcement = "AllowUnencrypted"
	VirtualNetworkEncryptionEnforcementDropUnencrypted  VirtualNetworkEncryptionEnforcement = "DropUnencrypted"
)

// PossibleVirtualNetworkEncryptionEnforcementValues returns the possible values for the VirtualNetworkEncryptionEnforcement const type.
func PossibleVirtualNetworkEncryptionEnforcementValues() []VirtualNetworkEncryptionEnforcement {
	return []VirtualNetworkEncryptionEnforcement{
		VirtualNetworkEncryptionEnforcementAllowUnencrypted,
		VirtualNetworkEncryptionEnforcementDropUnencrypted,
	}
}

// VirtualNetworkGatewayConnectionMode - Gateway connection type.
type VirtualNetworkGatewayConnectionMode string

const (
	VirtualNetworkGatewayConnectionModeDefault       VirtualNetworkGatewayConnectionMode = "Default"
	VirtualNetworkGatewayConnectionModeInitiatorOnly VirtualNetworkGatewayConnectionMode = "InitiatorOnly"
	VirtualNetworkGatewayConnectionModeResponderOnly VirtualNetworkGatewayConnectionMode = "ResponderOnly"
)

// PossibleVirtualNetworkGatewayConnectionModeValues returns the possible values for the VirtualNetworkGatewayConnectionMode const type.
func PossibleVirtualNetworkGatewayConnectionModeValues() []VirtualNetworkGatewayConnectionMode {
	return []VirtualNetworkGatewayConnectionMode{
		VirtualNetworkGatewayConnectionModeDefault,
		VirtualNetworkGatewayConnectionModeInitiatorOnly,
		VirtualNetworkGatewayConnectionModeResponderOnly,
	}
}

// VirtualNetworkGatewayConnectionProtocol - Gateway connection protocol.
type VirtualNetworkGatewayConnectionProtocol string

const (
	VirtualNetworkGatewayConnectionProtocolIKEv1 VirtualNetworkGatewayConnectionProtocol = "IKEv1"
	VirtualNetworkGatewayConnectionProtocolIKEv2 VirtualNetworkGatewayConnectionProtocol = "IKEv2"
)

// PossibleVirtualNetworkGatewayConnectionProtocolValues returns the possible values for the VirtualNetworkGatewayConnectionProtocol const type.
func PossibleVirtualNetworkGatewayConnectionProtocolValues() []VirtualNetworkGatewayConnectionProtocol {
	return []VirtualNetworkGatewayConnectionProtocol{
		VirtualNetworkGatewayConnectionProtocolIKEv1,
		VirtualNetworkGatewayConnectionProtocolIKEv2,
	}
}

// VirtualNetworkGatewayConnectionStatus - Virtual Network Gateway connection status.
type VirtualNetworkGatewayConnectionStatus string

const (
	VirtualNetworkGatewayConnectionStatusConnected    VirtualNetworkGatewayConnectionStatus = "Connected"
	VirtualNetworkGatewayConnectionStatusConnecting   VirtualNetworkGatewayConnectionStatus = "Connecting"
	VirtualNetworkGatewayConnectionStatusNotConnected VirtualNetworkGatewayConnectionStatus = "NotConnected"
	VirtualNetworkGatewayConnectionStatusUnknown      VirtualNetworkGatewayConnectionStatus = "Unknown"
)

// PossibleVirtualNetworkGatewayConnectionStatusValues returns the possible values for the VirtualNetworkGatewayConnectionStatus const type.
func PossibleVirtualNetworkGatewayConnectionStatusValues() []VirtualNetworkGatewayConnectionStatus {
	return []VirtualNetworkGatewayConnectionStatus{
		VirtualNetworkGatewayConnectionStatusConnected,
		VirtualNetworkGatewayConnectionStatusConnecting,
		VirtualNetworkGatewayConnectionStatusNotConnected,
		VirtualNetworkGatewayConnectionStatusUnknown,
	}
}

// VirtualNetworkGatewayConnectionType - Gateway connection type.
type VirtualNetworkGatewayConnectionType string

const (
	VirtualNetworkGatewayConnectionTypeExpressRoute VirtualNetworkGatewayConnectionType = "ExpressRoute"
	VirtualNetworkGatewayConnectionTypeIPsec        VirtualNetworkGatewayConnectionType = "IPsec"
	VirtualNetworkGatewayConnectionTypeVPNClient    VirtualNetworkGatewayConnectionType = "VPNClient"
	VirtualNetworkGatewayConnectionTypeVnet2Vnet    VirtualNetworkGatewayConnectionType = "Vnet2Vnet"
)

// PossibleVirtualNetworkGatewayConnectionTypeValues returns the possible values for the VirtualNetworkGatewayConnectionType const type.
func PossibleVirtualNetworkGatewayConnectionTypeValues() []VirtualNetworkGatewayConnectionType {
	return []VirtualNetworkGatewayConnectionType{
		VirtualNetworkGatewayConnectionTypeExpressRoute,
		VirtualNetworkGatewayConnectionTypeIPsec,
		VirtualNetworkGatewayConnectionTypeVPNClient,
		VirtualNetworkGatewayConnectionTypeVnet2Vnet,
	}
}

// VirtualNetworkGatewaySKUName - Gateway SKU name.
type VirtualNetworkGatewaySKUName string

const (
	VirtualNetworkGatewaySKUNameBasic            VirtualNetworkGatewaySKUName = "Basic"
	VirtualNetworkGatewaySKUNameErGw1AZ          VirtualNetworkGatewaySKUName = "ErGw1AZ"
	VirtualNetworkGatewaySKUNameErGw2AZ          VirtualNetworkGatewaySKUName = "ErGw2AZ"
	VirtualNetworkGatewaySKUNameErGw3AZ          VirtualNetworkGatewaySKUName = "ErGw3AZ"
	VirtualNetworkGatewaySKUNameHighPerformance  VirtualNetworkGatewaySKUName = "HighPerformance"
	VirtualNetworkGatewaySKUNameStandard         VirtualNetworkGatewaySKUName = "Standard"
	VirtualNetworkGatewaySKUNameUltraPerformance VirtualNetworkGatewaySKUName = "UltraPerformance"
	VirtualNetworkGatewaySKUNameVPNGw1           VirtualNetworkGatewaySKUName = "VpnGw1"
	VirtualNetworkGatewaySKUNameVPNGw1AZ         VirtualNetworkGatewaySKUName = "VpnGw1AZ"
	VirtualNetworkGatewaySKUNameVPNGw2           VirtualNetworkGatewaySKUName = "VpnGw2"
	VirtualNetworkGatewaySKUNameVPNGw2AZ         VirtualNetworkGatewaySKUName = "VpnGw2AZ"
	VirtualNetworkGatewaySKUNameVPNGw3           VirtualNetworkGatewaySKUName = "VpnGw3"
	VirtualNetworkGatewaySKUNameVPNGw3AZ         VirtualNetworkGatewaySKUName = "VpnGw3AZ"
	VirtualNetworkGatewaySKUNameVPNGw4           VirtualNetworkGatewaySKUName = "VpnGw4"
	VirtualNetworkGatewaySKUNameVPNGw4AZ         VirtualNetworkGatewaySKUName = "VpnGw4AZ"
	VirtualNetworkGatewaySKUNameVPNGw5           VirtualNetworkGatewaySKUName = "VpnGw5"
	VirtualNetworkGatewaySKUNameVPNGw5AZ         VirtualNetworkGatewaySKUName = "VpnGw5AZ"
)

// PossibleVirtualNetworkGatewaySKUNameValues returns the possible values for the VirtualNetworkGatewaySKUName const type.
func PossibleVirtualNetworkGatewaySKUNameValues() []VirtualNetworkGatewaySKUName {
	return []VirtualNetworkGatewaySKUName{
		VirtualNetworkGatewaySKUNameBasic,
		VirtualNetworkGatewaySKUNameErGw1AZ,
		VirtualNetworkGatewaySKUNameErGw2AZ,
		VirtualNetworkGatewaySKUNameErGw3AZ,
		VirtualNetworkGatewaySKUNameHighPerformance,
		VirtualNetworkGatewaySKUNameStandard,
		VirtualNetworkGatewaySKUNameUltraPerformance,
		VirtualNetworkGatewaySKUNameVPNGw1,
		VirtualNetworkGatewaySKUNameVPNGw1AZ,
		VirtualNetworkGatewaySKUNameVPNGw2,
		VirtualNetworkGatewaySKUNameVPNGw2AZ,
		VirtualNetworkGatewaySKUNameVPNGw3,
		VirtualNetworkGatewaySKUNameVPNGw3AZ,
		VirtualNetworkGatewaySKUNameVPNGw4,
		VirtualNetworkGatewaySKUNameVPNGw4AZ,
		VirtualNetworkGatewaySKUNameVPNGw5,
		VirtualNetworkGatewaySKUNameVPNGw5AZ,
	}
}

// VirtualNetworkGatewaySKUTier - Gateway SKU tier.
type VirtualNetworkGatewaySKUTier string

const (
	VirtualNetworkGatewaySKUTierBasic            VirtualNetworkGatewaySKUTier = "Basic"
	VirtualNetworkGatewaySKUTierErGw1AZ          VirtualNetworkGatewaySKUTier = "ErGw1AZ"
	VirtualNetworkGatewaySKUTierErGw2AZ          VirtualNetworkGatewaySKUTier = "ErGw2AZ"
	VirtualNetworkGatewaySKUTierErGw3AZ          VirtualNetworkGatewaySKUTier = "ErGw3AZ"
	VirtualNetworkGatewaySKUTierHighPerformance  VirtualNetworkGatewaySKUTier = "HighPerformance"
	VirtualNetworkGatewaySKUTierStandard         VirtualNetworkGatewaySKUTier = "Standard"
	VirtualNetworkGatewaySKUTierUltraPerformance VirtualNetworkGatewaySKUTier = "UltraPerformance"
	VirtualNetworkGatewaySKUTierVPNGw1           VirtualNetworkGatewaySKUTier = "VpnGw1"
	VirtualNetworkGatewaySKUTierVPNGw1AZ         VirtualNetworkGatewaySKUTier = "VpnGw1AZ"
	VirtualNetworkGatewaySKUTierVPNGw2           VirtualNetworkGatewaySKUTier = "VpnGw2"
	VirtualNetworkGatewaySKUTierVPNGw2AZ         VirtualNetworkGatewaySKUTier = "VpnGw2AZ"
	VirtualNetworkGatewaySKUTierVPNGw3           VirtualNetworkGatewaySKUTier = "VpnGw3"
	VirtualNetworkGatewaySKUTierVPNGw3AZ         VirtualNetworkGatewaySKUTier = "VpnGw3AZ"
	VirtualNetworkGatewaySKUTierVPNGw4           VirtualNetworkGatewaySKUTier = "VpnGw4"
	VirtualNetworkGatewaySKUTierVPNGw4AZ         VirtualNetworkGatewaySKUTier = "VpnGw4AZ"
	VirtualNetworkGatewaySKUTierVPNGw5           VirtualNetworkGatewaySKUTier = "VpnGw5"
	VirtualNetworkGatewaySKUTierVPNGw5AZ         VirtualNetworkGatewaySKUTier = "VpnGw5AZ"
)

// PossibleVirtualNetworkGatewaySKUTierValues returns the possible values for the VirtualNetworkGatewaySKUTier const type.
func PossibleVirtualNetworkGatewaySKUTierValues() []VirtualNetworkGatewaySKUTier {
	return []VirtualNetworkGatewaySKUTier{
		VirtualNetworkGatewaySKUTierBasic,
		VirtualNetworkGatewaySKUTierErGw1AZ,
		VirtualNetworkGatewaySKUTierErGw2AZ,
		VirtualNetworkGatewaySKUTierErGw3AZ,
		VirtualNetworkGatewaySKUTierHighPerformance,
		VirtualNetworkGatewaySKUTierStandard,
		VirtualNetworkGatewaySKUTierUltraPerformance,
		VirtualNetworkGatewaySKUTierVPNGw1,
		VirtualNetworkGatewaySKUTierVPNGw1AZ,
		VirtualNetworkGatewaySKUTierVPNGw2,
		VirtualNetworkGatewaySKUTierVPNGw2AZ,
		VirtualNetworkGatewaySKUTierVPNGw3,
		VirtualNetworkGatewaySKUTierVPNGw3AZ,
		VirtualNetworkGatewaySKUTierVPNGw4,
		VirtualNetworkGatewaySKUTierVPNGw4AZ,
		VirtualNetworkGatewaySKUTierVPNGw5,
		VirtualNetworkGatewaySKUTierVPNGw5AZ,
	}
}

// VirtualNetworkGatewayType - The type of this virtual network gateway.
type VirtualNetworkGatewayType string

const (
	VirtualNetworkGatewayTypeExpressRoute VirtualNetworkGatewayType = "ExpressRoute"
	VirtualNetworkGatewayTypeLocalGateway VirtualNetworkGatewayType = "LocalGateway"
	VirtualNetworkGatewayTypeVPN          VirtualNetworkGatewayType = "Vpn"
)

// PossibleVirtualNetworkGatewayTypeValues returns the possible values for the VirtualNetworkGatewayType const type.
func PossibleVirtualNetworkGatewayTypeValues() []VirtualNetworkGatewayType {
	return []VirtualNetworkGatewayType{
		VirtualNetworkGatewayTypeExpressRoute,
		VirtualNetworkGatewayTypeLocalGateway,
		VirtualNetworkGatewayTypeVPN,
	}
}

// VirtualNetworkPeeringLevel - The peering sync status of the virtual network peering.
type VirtualNetworkPeeringLevel string

const (
	VirtualNetworkPeeringLevelFullyInSync             VirtualNetworkPeeringLevel = "FullyInSync"
	VirtualNetworkPeeringLevelLocalAndRemoteNotInSync VirtualNetworkPeeringLevel = "LocalAndRemoteNotInSync"
	VirtualNetworkPeeringLevelLocalNotInSync          VirtualNetworkPeeringLevel = "LocalNotInSync"
	VirtualNetworkPeeringLevelRemoteNotInSync         VirtualNetworkPeeringLevel = "RemoteNotInSync"
)

// PossibleVirtualNetworkPeeringLevelValues returns the possible values for the VirtualNetworkPeeringLevel const type.
func PossibleVirtualNetworkPeeringLevelValues() []VirtualNetworkPeeringLevel {
	return []VirtualNetworkPeeringLevel{
		VirtualNetworkPeeringLevelFullyInSync,
		VirtualNetworkPeeringLevelLocalAndRemoteNotInSync,
		VirtualNetworkPeeringLevelLocalNotInSync,
		VirtualNetworkPeeringLevelRemoteNotInSync,
	}
}

// VirtualNetworkPeeringState - The status of the virtual network peering.
type VirtualNetworkPeeringState string

const (
	VirtualNetworkPeeringStateConnected    VirtualNetworkPeeringState = "Connected"
	VirtualNetworkPeeringStateDisconnected VirtualNetworkPeeringState = "Disconnected"
	VirtualNetworkPeeringStateInitiated    VirtualNetworkPeeringState = "Initiated"
)

// PossibleVirtualNetworkPeeringStateValues returns the possible values for the VirtualNetworkPeeringState const type.
func PossibleVirtualNetworkPeeringStateValues() []VirtualNetworkPeeringState {
	return []VirtualNetworkPeeringState{
		VirtualNetworkPeeringStateConnected,
		VirtualNetworkPeeringStateDisconnected,
		VirtualNetworkPeeringStateInitiated,
	}
}

// VirtualNetworkPrivateEndpointNetworkPolicies - Enable or Disable apply network policies on private end point in the subnet.
type VirtualNetworkPrivateEndpointNetworkPolicies string

const (
	VirtualNetworkPrivateEndpointNetworkPoliciesDisabled VirtualNetworkPrivateEndpointNetworkPolicies = "Disabled"
	VirtualNetworkPrivateEndpointNetworkPoliciesEnabled  VirtualNetworkPrivateEndpointNetworkPolicies = "Enabled"
)

// PossibleVirtualNetworkPrivateEndpointNetworkPoliciesValues returns the possible values for the VirtualNetworkPrivateEndpointNetworkPolicies const type.
func PossibleVirtualNetworkPrivateEndpointNetworkPoliciesValues() []VirtualNetworkPrivateEndpointNetworkPolicies {
	return []VirtualNetworkPrivateEndpointNetworkPolicies{
		VirtualNetworkPrivateEndpointNetworkPoliciesDisabled,
		VirtualNetworkPrivateEndpointNetworkPoliciesEnabled,
	}
}

// VirtualNetworkPrivateLinkServiceNetworkPolicies - Enable or Disable apply network policies on private link service in the
// subnet.
type VirtualNetworkPrivateLinkServiceNetworkPolicies string

const (
	VirtualNetworkPrivateLinkServiceNetworkPoliciesDisabled VirtualNetworkPrivateLinkServiceNetworkPolicies = "Disabled"
	VirtualNetworkPrivateLinkServiceNetworkPoliciesEnabled  VirtualNetworkPrivateLinkServiceNetworkPolicies = "Enabled"
)

// PossibleVirtualNetworkPrivateLinkServiceNetworkPoliciesValues returns the possible values for the VirtualNetworkPrivateLinkServiceNetworkPolicies const type.
func PossibleVirtualNetworkPrivateLinkServiceNetworkPoliciesValues() []VirtualNetworkPrivateLinkServiceNetworkPolicies {
	return []VirtualNetworkPrivateLinkServiceNetworkPolicies{
		VirtualNetworkPrivateLinkServiceNetworkPoliciesDisabled,
		VirtualNetworkPrivateLinkServiceNetworkPoliciesEnabled,
	}
}

// VirtualWanSecurityProviderType - The virtual wan security provider type.
type VirtualWanSecurityProviderType string

const (
	VirtualWanSecurityProviderTypeExternal VirtualWanSecurityProviderType = "External"
	VirtualWanSecurityProviderTypeNative   VirtualWanSecurityProviderType = "Native"
)

// PossibleVirtualWanSecurityProviderTypeValues returns the possible values for the VirtualWanSecurityProviderType const type.
func PossibleVirtualWanSecurityProviderTypeValues() []VirtualWanSecurityProviderType {
	return []VirtualWanSecurityProviderType{
		VirtualWanSecurityProviderTypeExternal,
		VirtualWanSecurityProviderTypeNative,
	}
}

// VnetLocalRouteOverrideCriteria - Parameter determining whether NVA in spoke vnet is bypassed for traffic with destination
// in spoke vnet.
type VnetLocalRouteOverrideCriteria string

const (
	VnetLocalRouteOverrideCriteriaContains VnetLocalRouteOverrideCriteria = "Contains"
	VnetLocalRouteOverrideCriteriaEqual    VnetLocalRouteOverrideCriteria = "Equal"
)

// PossibleVnetLocalRouteOverrideCriteriaValues returns the possible values for the VnetLocalRouteOverrideCriteria const type.
func PossibleVnetLocalRouteOverrideCriteriaValues() []VnetLocalRouteOverrideCriteria {
	return []VnetLocalRouteOverrideCriteria{
		VnetLocalRouteOverrideCriteriaContains,
		VnetLocalRouteOverrideCriteriaEqual,
	}
}

// WebApplicationFirewallAction - Type of Actions.
type WebApplicationFirewallAction string

const (
	WebApplicationFirewallActionAllow WebApplicationFirewallAction = "Allow"
	WebApplicationFirewallActionBlock WebApplicationFirewallAction = "Block"
	WebApplicationFirewallActionLog   WebApplicationFirewallAction = "Log"
)

// PossibleWebApplicationFirewallActionValues returns the possible values for the WebApplicationFirewallAction const type.
func PossibleWebApplicationFirewallActionValues() []WebApplicationFirewallAction {
	return []WebApplicationFirewallAction{
		WebApplicationFirewallActionAllow,
		WebApplicationFirewallActionBlock,
		WebApplicationFirewallActionLog,
	}
}

// WebApplicationFirewallEnabledState - The state of the policy.
type WebApplicationFirewallEnabledState string

const (
	WebApplicationFirewallEnabledStateDisabled WebApplicationFirewallEnabledState = "Disabled"
	WebApplicationFirewallEnabledStateEnabled  WebApplicationFirewallEnabledState = "Enabled"
)

// PossibleWebApplicationFirewallEnabledStateValues returns the possible values for the WebApplicationFirewallEnabledState const type.
func PossibleWebApplicationFirewallEnabledStateValues() []WebApplicationFirewallEnabledState {
	return []WebApplicationFirewallEnabledState{
		WebApplicationFirewallEnabledStateDisabled,
		WebApplicationFirewallEnabledStateEnabled,
	}
}

// WebApplicationFirewallMatchVariable - Match Variable.
type WebApplicationFirewallMatchVariable string

const (
	WebApplicationFirewallMatchVariablePostArgs       WebApplicationFirewallMatchVariable = "PostArgs"
	WebApplicationFirewallMatchVariableQueryString    WebApplicationFirewallMatchVariable = "QueryString"
	WebApplicationFirewallMatchVariableRemoteAddr     WebApplicationFirewallMatchVariable = "RemoteAddr"
	WebApplicationFirewallMatchVariableRequestBody    WebApplicationFirewallMatchVariable = "RequestBody"
	WebApplicationFirewallMatchVariableRequestCookies WebApplicationFirewallMatchVariable = "RequestCookies"
	WebApplicationFirewallMatchVariableRequestHeaders WebApplicationFirewallMatchVariable = "RequestHeaders"
	WebApplicationFirewallMatchVariableRequestMethod  WebApplicationFirewallMatchVariable = "RequestMethod"
	WebApplicationFirewallMatchVariableRequestURI     WebApplicationFirewallMatchVariable = "RequestUri"
)

// PossibleWebApplicationFirewallMatchVariableValues returns the possible values for the WebApplicationFirewallMatchVariable const type.
func PossibleWebApplicationFirewallMatchVariableValues() []WebApplicationFirewallMatchVariable {
	return []WebApplicationFirewallMatchVariable{
		WebApplicationFirewallMatchVariablePostArgs,
		WebApplicationFirewallMatchVariableQueryString,
		WebApplicationFirewallMatchVariableRemoteAddr,
		WebApplicationFirewallMatchVariableRequestBody,
		WebApplicationFirewallMatchVariableRequestCookies,
		WebApplicationFirewallMatchVariableRequestHeaders,
		WebApplicationFirewallMatchVariableRequestMethod,
		WebApplicationFirewallMatchVariableRequestURI,
	}
}

// WebApplicationFirewallMode - The mode of the policy.
type WebApplicationFirewallMode string

const (
	WebApplicationFirewallModeDetection  WebApplicationFirewallMode = "Detection"
	WebApplicationFirewallModePrevention WebApplicationFirewallMode = "Prevention"
)

// PossibleWebApplicationFirewallModeValues returns the possible values for the WebApplicationFirewallMode const type.
func PossibleWebApplicationFirewallModeValues() []WebApplicationFirewallMode {
	return []WebApplicationFirewallMode{
		WebApplicationFirewallModeDetection,
		WebApplicationFirewallModePrevention,
	}
}

// WebApplicationFirewallOperator - The operator to be matched.
type WebApplicationFirewallOperator string

const (
	WebApplicationFirewallOperatorAny                WebApplicationFirewallOperator = "Any"
	WebApplicationFirewallOperatorBeginsWith         WebApplicationFirewallOperator = "BeginsWith"
	WebApplicationFirewallOperatorContains           WebApplicationFirewallOperator = "Contains"
	WebApplicationFirewallOperatorEndsWith           WebApplicationFirewallOperator = "EndsWith"
	WebApplicationFirewallOperatorEqual              WebApplicationFirewallOperator = "Equal"
	WebApplicationFirewallOperatorGeoMatch           WebApplicationFirewallOperator = "GeoMatch"
	WebApplicationFirewallOperatorGreaterThan        WebApplicationFirewallOperator = "GreaterThan"
	WebApplicationFirewallOperatorGreaterThanOrEqual WebApplicationFirewallOperator = "GreaterThanOrEqual"
	WebApplicationFirewallOperatorIPMatch            WebApplicationFirewallOperator = "IPMatch"
	WebApplicationFirewallOperatorLessThan           WebApplicationFirewallOperator = "LessThan"
	WebApplicationFirewallOperatorLessThanOrEqual    WebApplicationFirewallOperator = "LessThanOrEqual"
	WebApplicationFirewallOperatorRegex              WebApplicationFirewallOperator = "Regex"
)

// PossibleWebApplicationFirewallOperatorValues returns the possible values for the WebApplicationFirewallOperator const type.
func PossibleWebApplicationFirewallOperatorValues() []WebApplicationFirewallOperator {
	return []WebApplicationFirewallOperator{
		WebApplicationFirewallOperatorAny,
		WebApplicationFirewallOperatorBeginsWith,
		WebApplicationFirewallOperatorContains,
		WebApplicationFirewallOperatorEndsWith,
		WebApplicationFirewallOperatorEqual,
		WebApplicationFirewallOperatorGeoMatch,
		WebApplicationFirewallOperatorGreaterThan,
		WebApplicationFirewallOperatorGreaterThanOrEqual,
		WebApplicationFirewallOperatorIPMatch,
		WebApplicationFirewallOperatorLessThan,
		WebApplicationFirewallOperatorLessThanOrEqual,
		WebApplicationFirewallOperatorRegex,
	}
}

// WebApplicationFirewallPolicyResourceState - Resource status of the policy.
type WebApplicationFirewallPolicyResourceState string

const (
	WebApplicationFirewallPolicyResourceStateCreating  WebApplicationFirewallPolicyResourceState = "Creating"
	WebApplicationFirewallPolicyResourceStateDeleting  WebApplicationFirewallPolicyResourceState = "Deleting"
	WebApplicationFirewallPolicyResourceStateDisabled  WebApplicationFirewallPolicyResourceState = "Disabled"
	WebApplicationFirewallPolicyResourceStateDisabling WebApplicationFirewallPolicyResourceState = "Disabling"
	WebApplicationFirewallPolicyResourceStateEnabled   WebApplicationFirewallPolicyResourceState = "Enabled"
	WebApplicationFirewallPolicyResourceStateEnabling  WebApplicationFirewallPolicyResourceState = "Enabling"
)

// PossibleWebApplicationFirewallPolicyResourceStateValues returns the possible values for the WebApplicationFirewallPolicyResourceState const type.
func PossibleWebApplicationFirewallPolicyResourceStateValues() []WebApplicationFirewallPolicyResourceState {
	return []WebApplicationFirewallPolicyResourceState{
		WebApplicationFirewallPolicyResourceStateCreating,
		WebApplicationFirewallPolicyResourceStateDeleting,
		WebApplicationFirewallPolicyResourceStateDisabled,
		WebApplicationFirewallPolicyResourceStateDisabling,
		WebApplicationFirewallPolicyResourceStateEnabled,
		WebApplicationFirewallPolicyResourceStateEnabling,
	}
}

// WebApplicationFirewallRuleType - The rule type.
type WebApplicationFirewallRuleType string

const (
	WebApplicationFirewallRuleTypeInvalid       WebApplicationFirewallRuleType = "Invalid"
	WebApplicationFirewallRuleTypeMatchRule     WebApplicationFirewallRuleType = "MatchRule"
	WebApplicationFirewallRuleTypeRateLimitRule WebApplicationFirewallRuleType = "RateLimitRule"
)

// PossibleWebApplicationFirewallRuleTypeValues returns the possible values for the WebApplicationFirewallRuleType const type.
func PossibleWebApplicationFirewallRuleTypeValues() []WebApplicationFirewallRuleType {
	return []WebApplicationFirewallRuleType{
		WebApplicationFirewallRuleTypeInvalid,
		WebApplicationFirewallRuleTypeMatchRule,
		WebApplicationFirewallRuleTypeRateLimitRule,
	}
}

// WebApplicationFirewallScrubbingState - State of the log scrubbing config. Default value is Enabled.
type WebApplicationFirewallScrubbingState string

const (
	WebApplicationFirewallScrubbingStateDisabled WebApplicationFirewallScrubbingState = "Disabled"
	WebApplicationFirewallScrubbingStateEnabled  WebApplicationFirewallScrubbingState = "Enabled"
)

// PossibleWebApplicationFirewallScrubbingStateValues returns the possible values for the WebApplicationFirewallScrubbingState const type.
func PossibleWebApplicationFirewallScrubbingStateValues() []WebApplicationFirewallScrubbingState {
	return []WebApplicationFirewallScrubbingState{
		WebApplicationFirewallScrubbingStateDisabled,
		WebApplicationFirewallScrubbingStateEnabled,
	}
}

// WebApplicationFirewallState - Describes if the custom rule is in enabled or disabled state. Defaults to Enabled if not
// specified.
type WebApplicationFirewallState string

const (
	WebApplicationFirewallStateDisabled WebApplicationFirewallState = "Disabled"
	WebApplicationFirewallStateEnabled  WebApplicationFirewallState = "Enabled"
)

// PossibleWebApplicationFirewallStateValues returns the possible values for the WebApplicationFirewallState const type.
func PossibleWebApplicationFirewallStateValues() []WebApplicationFirewallState {
	return []WebApplicationFirewallState{
		WebApplicationFirewallStateDisabled,
		WebApplicationFirewallStateEnabled,
	}
}

// WebApplicationFirewallTransform - Transforms applied before matching.
type WebApplicationFirewallTransform string

const (
	WebApplicationFirewallTransformHTMLEntityDecode WebApplicationFirewallTransform = "HtmlEntityDecode"
	WebApplicationFirewallTransformLowercase        WebApplicationFirewallTransform = "Lowercase"
	WebApplicationFirewallTransformRemoveNulls      WebApplicationFirewallTransform = "RemoveNulls"
	WebApplicationFirewallTransformTrim             WebApplicationFirewallTransform = "Trim"
	WebApplicationFirewallTransformURLDecode        WebApplicationFirewallTransform = "UrlDecode"
	WebApplicationFirewallTransformURLEncode        WebApplicationFirewallTransform = "UrlEncode"
	WebApplicationFirewallTransformUppercase        WebApplicationFirewallTransform = "Uppercase"
)

// PossibleWebApplicationFirewallTransformValues returns the possible values for the WebApplicationFirewallTransform const type.
func PossibleWebApplicationFirewallTransformValues() []WebApplicationFirewallTransform {
	return []WebApplicationFirewallTransform{
		WebApplicationFirewallTransformHTMLEntityDecode,
		WebApplicationFirewallTransformLowercase,
		WebApplicationFirewallTransformRemoveNulls,
		WebApplicationFirewallTransformTrim,
		WebApplicationFirewallTransformURLDecode,
		WebApplicationFirewallTransformURLEncode,
		WebApplicationFirewallTransformUppercase,
	}
}
