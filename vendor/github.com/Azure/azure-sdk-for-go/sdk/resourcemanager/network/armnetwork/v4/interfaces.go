//go:build go1.18
// +build go1.18

// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License. See License.txt in the project root for license information.
// Code generated by Microsoft (R) AutoRest Code Generator. DO NOT EDIT.
// Changes may cause incorrect behavior and will be lost if the code is regenerated.

package armnetwork

// ActiveBaseSecurityAdminRuleClassification provides polymorphic access to related types.
// Call the interface's GetActiveBaseSecurityAdminRule() method to access the common type.
// Use a type switch to determine the concrete type.  The possible types are:
// - *ActiveBaseSecurityAdminRule, *ActiveDefaultSecurityAdminRule, *ActiveSecurityAdminRule
type ActiveBaseSecurityAdminRuleClassification interface {
	// GetActiveBaseSecurityAdminRule returns the ActiveBaseSecurityAdminRule content of the underlying type.
	GetActiveBaseSecurityAdminRule() *ActiveBaseSecurityAdminRule
}

// BaseAdminRuleClassification provides polymorphic access to related types.
// Call the interface's GetBaseAdminRule() method to access the common type.
// Use a type switch to determine the concrete type.  The possible types are:
// - *AdminRule, *BaseAdminRule, *DefaultAdminRule
type BaseAdminRuleClassification interface {
	// GetBaseAdminRule returns the BaseAdminRule content of the underlying type.
	GetBaseAdminRule() *BaseAdminRule
}

// EffectiveBaseSecurityAdminRuleClassification provides polymorphic access to related types.
// Call the interface's GetEffectiveBaseSecurityAdminRule() method to access the common type.
// Use a type switch to determine the concrete type.  The possible types are:
// - *EffectiveBaseSecurityAdminRule, *EffectiveDefaultSecurityAdminRule, *EffectiveSecurityAdminRule
type EffectiveBaseSecurityAdminRuleClassification interface {
	// GetEffectiveBaseSecurityAdminRule returns the EffectiveBaseSecurityAdminRule content of the underlying type.
	GetEffectiveBaseSecurityAdminRule() *EffectiveBaseSecurityAdminRule
}

// FirewallPolicyRuleClassification provides polymorphic access to related types.
// Call the interface's GetFirewallPolicyRule() method to access the common type.
// Use a type switch to determine the concrete type.  The possible types are:
// - *ApplicationRule, *FirewallPolicyRule, *NatRule, *Rule
type FirewallPolicyRuleClassification interface {
	// GetFirewallPolicyRule returns the FirewallPolicyRule content of the underlying type.
	GetFirewallPolicyRule() *FirewallPolicyRule
}

// FirewallPolicyRuleCollectionClassification provides polymorphic access to related types.
// Call the interface's GetFirewallPolicyRuleCollection() method to access the common type.
// Use a type switch to determine the concrete type.  The possible types are:
// - *FirewallPolicyFilterRuleCollection, *FirewallPolicyNatRuleCollection, *FirewallPolicyRuleCollection
type FirewallPolicyRuleCollectionClassification interface {
	// GetFirewallPolicyRuleCollection returns the FirewallPolicyRuleCollection content of the underlying type.
	GetFirewallPolicyRuleCollection() *FirewallPolicyRuleCollection
}
